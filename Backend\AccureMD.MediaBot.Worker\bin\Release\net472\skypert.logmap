0015a2ab:"WinAsymmCryptoCngImpl::decrypt: src size should be equal to rsa_size\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::decrypt"
00230c2a:"usrsctp_setsockopt SCTP_ENABLE_STREAM_RESET: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
006cea8a:"onTerminalStateReached","RootTools/roottools/net/src/sysdeps/berkeley/resolver_bsd.cpp";"`anonymous-namespace\'::NameResolverOperation::onTerminalStateReached"
0077dc33:"Failed to create ThreadPoolManager","RootTools/roottools/auf/src/thread_pool.cpp";"auf::threadPoolFromKeyCore"
007b718c:"%s","RootTools/roottools/spl/src/sysdeps/win/platform_rng_win.cpp";"spl::getRngProvider"
01054634:"noPingCallback: Pinger failed","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::TraceRouteOperationNF::noPingCallback"
010ba4e7:"WinAsymmCryptoCngImpl::encrypt: src size too large\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::encrypt"
015662ce:"ConnectionIsReadyToSend %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::ConnectionIsReadyToSend"
015c580d:"**** Error 0x%x building subject name","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::logPeerCert"
01b93efc:"Failed to get gpu memory status [adapter = %d], not able to retrieve segment Information","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUMemoryStatus"
01ba6661:"DbgHelpFunctions.unload %d","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::DbgHelpFunctions::unload"
01bcb28f:"exit unregisterListener","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::unregisterListener"
01d138c8:"RQ%u: Request launch timeout","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Launch"
01f3f698:"WlanConnectionQuality returned empty data block","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::wlanConnectionQuality"
022c1a08:"Connect to %s:%d: err or hangup %d: %s","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::socketConnected"
023f02f6:"spl::threadCreate(): _beginthreadex() failed, error code: %lx\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::osCreateThread"
025ad778:"directoryFlush: failed to flush \'%s\', error: %lx","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"WinSplDirImpl::flush"
028fe2f5:"Discarded %u nodes","RootTools/roottools/net/src/dns_cache_v2.cpp";"`anonymous-namespace\'::DNSCache::DoHousekeeping"
02af575c:"send(%p, %I64u) -> %d","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::ITlsIO_write"
031d9ff6:"doStore: Success to write %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::doStore"
034697cf:"WlanQueryInterface for wlan_intf_opcode_realtime_connection_quality returned unexpected size %u (expected >= %u)","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::wlanConnectionQuality"
03a5f037:"Log file updated, removing existing log file","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogFile"
03ba7817:"Factory::ctor","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"rtnet::internal::IOCPFactory::IOCPFactory"
03d24df0:"Volume serial number: %x","RootTools/roottools/spl/src/sysdeps/win/fingerprint_win.cpp";"spl::internal::appendVolumeInformation"
044c4ca2:"Trace FIFO size (L2 of num bytes): %u","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
0489ab60:"Encrypt: SSL_write: %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Encrypt"
048d147f:"Request is sent to %s:%u","RootTools/roottools/net/src/proxy_tcp_connect_v2.cpp";"`anonymous-namespace\'::HttpSerializerV2::IStreamSocketDelegate_bufferSent"
0497543a:"Unexpected SCTP notification %d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::DrawInputPipeline"
049fc51e:"getNetwork: getCachedNetworkList successful: count=%zu","RootTools/roottools/net/src/netmon.cpp";"rtnet::getNetworks"
04a12fc2:"generateResponse: usableList %s","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
04bcc36c:"tlsWriteAlert: Encrypting Alert failed: err %d","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsWriteAlert"
04bdca2f:"Creating temporary model.json file","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
04c28d4d:"anonymization disabled, log not dumped","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::mergeAndDumpLogBuffer"
0515f3ef:"FindConnectionPoint of INetworkEvents failed with HRESULT=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
0538aa56:"tlsWrite: exit partial write: wrote %I64d instead of %I64u","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsWrite"
05e98bfc:"proxyConnsStarted: %d ops count: %d failures count: %d; error [%d:%s] inserted at %s","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::IStreamSocketDelegate_error"
05ffa5c6:"%s","RootTools/roottools/spl/src/file.cpp";"spl::debugOpenFileHandles"
06394472:"WinSymmCryptoCngImpl::doOp: BCryptDecrypt failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::doOp"
068c6f9b:"SPL information:\n","RootTools/roottools/spl/src/spl_common.cpp";"spl::sysInfoLogDetails"
06c5673a:"Expected !m_triggerConfigs.empty()","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
0715b516:"Endpoint %s:%d; attempts %u terminal %u; %s","RootTools/roottools/net/src/connect_tcp_n.cpp";"`anonymous-namespace\'::TCP_N_Operation::onTerminalStateReached"
072192ec:"wakeupDeferred() type %d","RootTools/roottools/net/src/net_wakeup.cpp";"rtnet::WakeupNetworkOperation::wakeupDeferred"
0738b2cf:"WSAIoctl failed to get a function pointer with error code %lx. BUG","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"loadWinFunc"
074784b2:"Git revision: %s","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
07e16f5c:"Failed to load Legacy provider, %s","RootTools/roottools/spl/src/spl_common.cpp";"`anonymous-namespace\'::OpensslProviders::load"
08053a20:"INetworkInfoDelegate_interfaces: count=%d","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::INetworkInfoDelegate_interfaces"
0806183b:"%s","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::GetDynLibHelper"
080dd9d8:"RequestAccessAsync init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocatorImpl_RequestAccessAsync"
08b8affb:"Cached auth method found %s","RootTools/roottools/net/src/proxy_tcp_connect_v2.cpp";"`anonymous-namespace\'::ProxyOperationV2::DoStart"
08d917f1:"Failure creating a datagram socket for %s:%d (family %d)","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::doUdpBind"
08e9aed5:"INetworkInfoDelegate_networkChange: completed in %s","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::INetworkInfoDelegate_interfaces"
08ff0cba:"[%u] ","RootTools/roottools/spl/src/file.cpp";"spl::priv::FileHandlesTracker::log"
090e0696:"LOOK! SPL is executing the empty SEH handler and continuing, because g_dontTerminateOnThreadException was set. This WILL result in program malfunction\n","RootTools/roottools/spl/src/sysdeps/win/exception_filter.cpp";"spl::priv::winSehLoggingExceptionFilter"
091ccee0:"Failed to use the generated cert. %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Setup"
094ced61:"Bin %u (Sizes %I64u-%I64u)%s\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackBin::dump"
099d75e6:"keepPeerPublicKeyFingerprint: invalid session","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::keepPeerPublicKeyFingerprint"
09b8fe41:"Interface cache invalidated","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::getCachedInterfaceList"
09d47191:"stop(): timed out after %lld us. This might lead to that the target thread (%s) is leaked, due to not finishing in time.\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::stop"
0a022630:"Socket was in terminal state even before we started; cancelling","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::do_start"
0a225fcf:"RQ%u: Complete read %d bytes","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::processStreamRequest"
0a533059:"Could not initiate zlib: %d","RootTools/roottools/auf/src/log2.cpp";"auf::CompressWriter::open"
0a63d7d3:"Invalid thread hint configuration (prio = %d)\n","RootTools/roottools/auxd/skype/src/win/skype_win.cpp";"auf::threadSchedHintApply"
0a77a732:"tlsHandshakeStep: Certificate verification failed","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsHandshakeStep"
0a8350ac:"Detected security product type:%d, state: %d, name: %s, ","RootTools/roottools/net/src/sysdeps/win/sec_product_win.cpp";"rtnet::GetSecurityProductsState"
0a860dbd:"DeviceIoControl(IOCTL_BATTERY_QUERY_INFORMATION) failed with code %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
0ab9131c:"Calling into validateCatalogContents for file %s","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
0b254d2b:"deferredPacketSent: WSASend%s status %u","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::DatagramSocketImpl::packetSent"
0b2e5d37:"PersistentStorage load: %zu bytes","RootTools/roottools/spl/src/rt_persistent.cpp";"`anonymous-namespace\'::PersistentStorage::LoadFile"
0b33ec5e:"tlsDecrypt: DecryptMessage abnormal behavior, extra ciphertext is not in the end of ciphertext. Extra: %p (%lu bytes), ciphertext: %p (%zu bytes)","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsDecrypt"
0b70df97:"Failed to get gpu memory status [adapter = %d], D3DKMTQueryStatistics is not loaded","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUMemoryStatus"
0bbf1e35:"SplOpaqueUpperLayerThread: (ctor this=%p) realtime transport creation failed\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::SplOpaqueUpperLayerThread"
0bc54cee:"WinSymmCryptoCngImpl::doAEOp: BCryptDecrypt failed with NTSTATUS %d %s\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::doAEOp"
0be72b9a:"getResult4(): IcmpParseReplies failed: %u","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::getResult4"
0bfc65f2:"RegisterFromBuffer: replacing existing model %s with a new one sized %u","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromBuffer"
0c341d2e:"RQ%u: Decompression error %d (%s)","RootTools/roottools/httpstack/src/sysdeps/rt/decompressor.cpp";"http_stack::skypert::Decompressor::Decompress"
0c8572b4:"start","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::Nat64PrefixDiscoveryOperation::start"
0c987422:"Factory::dtor","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"rtnet::internal::IOCPFactory::~IOCPFactory"
0ca1d449:"WindowsReactorImpl::unregisterHandle COMPLETE","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::unregisterHandle"
0ca8f274:"DATA_CHANNEL_ACK received %s@%d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OnDataChannelAck"
0cd10225:"usrsctp_finish...","RootTools/roottools/sctp/src/sctp_stack.cpp";"rtsctp::priv::SCTPStack::~SCTPStack"
0cdc07bf:"SuspensionManager::strandUnregisterTask","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandUnregisterTask"
0cdd5d3b:"Log trigger telemetry: log upload span %s to %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::logBufferVisitStats"
0cee6499:"Done with merger, processed %u lines","RootTools/roottools/auf/src/log2.cpp";"auf::LogMergeOperationImpl::Appender::ILogAppender_close"
0d25aaff:"LogMap filter updated, removing existing filter","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogMapFilter"
0d7dc5e1:"attempt to get WlanRssiListener shutdown, failing","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::get"
0d8e4e8a:"rssi=0, getting new reading","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::signalStrength"
0dc50d59:"RQ%u: Added cookie: %s","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::ConnectionAvailable"
0dd75a46:"appCapabilityStatics->Create failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::DisplayAccessHint"
0ddca845:"Node ID: %I64x\n","RootTools/roottools/spl/src/sysdeps/win/sysinfo_win.cpp";"spl::priv::sysInfoLogDetails"
0de876aa:"Failed to query OS version from system, falling back to default\n","RootTools/roottools/spl/src/sysdeps/win/sysinfo_win.cpp";"spl::priv::WinVersion::WinVersion"
0e0faae9:"Old log file removed: %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::removeOldLogFiles"
0e1faf1a:"Terminate thread %lu.\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::osCreateThread"
0e31dd6f:"Failed to create reactor thread","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::WindowsReactorImpl"
0e65e321:"rtnet::priv::WlanApiWrapper::WlanApiWrapper(): Missing functions in wlanapi.dll","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanApiWrapper::WlanApiWrapper"
0e9d10cb:"acceptOnTransportAsync: setsockopt: %s","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::finishAccept"
0ec559ed:"Is system IPv6 capable: %s","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
0f67cb34:"%s","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::Connect"
0f9b0b4e:"spl::socketBind: setsockopt: %s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketBind"
0fd58707:"ProxyManagerV1 discovered %u proxies","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::discoverProxyInSeparateStrand"
100db535:"Invoke: result %d, Geo accessStatus %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeolocationAccessAsyncOpHandler::Invoke"
102b5e4a:"Log trigger telemetry: log buffer total lines %u, matched lines %u, uploaded lines %u","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::logBufferVisitStats"
1066e4ab:"RegisterFromIReferencedFile: cannot register model %s, since no model type metadata is present","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromIReferencedFile"
10880acb:"HMACWinCngImpl::HMACWinCngImpl: cloning failed\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::HMACWinCngImpl::clone"
10af46ad:"Reactor already running","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::start"
111e04b3:": unreferenced extent.\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::checkConsistency"
1154b7a1:"Unexpected signature error","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
116f4dcc:"exit addDelegate","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::addDelegate"
11723331:"MonitorOperation::strandDispatchChange","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::strandDispatchChange"
117420e4:"Stacktrace of inversion discovery:","RootTools/roottools/auf/src/mutex_orderer_tree.cpp";"auf::internal::MutexOrdererTree::checkForLockInversions"
11b0ba4a:"Failure to listen on socket for %s:%d","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::startWithAddressDeferred"
124020ac:"RQ%u: No response body expected","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::analyzeHeaders"
12425e0b:"Lockfree space depletion in BufferQueue::enqueueDesc(). Terminating","RootTools/roottools/net/private/net/common.hpp";"rtnet::BufferQueue<struct rtnet::StreamOutputBuffer>::enqueueDesc"
128243a6:"verifyKeyCertSerialized: empty certificate","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::verifyKeyCertSerialized"
1286f580:"RQ%u: Will send %d bytes of body buffer","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Launch"
1299f4d1:"makeAuthProvider: method=%s, usableList: %s","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::createTopMethod"
12c3b8fa:"WlanCapabilityChecker (value=%d) cached","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanCapabilityChecker::RequestAccessAsync"
12c5784e:"spl::threadCreate(): _beginthreadex() failed\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::osCreateThread"
12c74dc9:"Failed to create PL_APP_DATA_DIR","RootTools/roottools/spl/src/sysdeps/win/path_location_win.cpp";"spl::pathInitFromLocation"
12cb1ccc:"dtlsCreate: failed to import private key","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::dtlsCreate"
132f4c74:"Header value check failed for %s","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::SetHeader"
13504ed8:"Text file logging not allowed in public clients","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::effectiveLogFileType"
136efeef:"Destroyed","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::~Connection"
138cfb30:"UnregisterClass failed; error=%lx","RootTools/roottools/auf/apal/sysdeps/win/window_class.cpp";"apal::WindowClass::~WindowClass"
13c15677:"Unexpected error (%x): %s","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::WinTrustData::~WinTrustData"
13cd5273:"Unable to register ETW provider guid (%s). Error: %u","RootTools/roottools/auf/src/log2_wpp_appender.cpp";"auf::EtlLogAppender::EtlLogAppender"
13d790d0:"usrsctp_connect: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
1413354a:"No allocation performed.\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::allocateBin"
1415d6ef:"lldp.readPortId: got port type=%u, length=%zu, value=%s","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::LLDPreader::readPortId"
147c8175:"Address::presentationStringWithPort: unspecified address stored (family %u), returning empty string","RootTools/roottools/net/src/address.cpp";"rtnet::Address::presentationStringWithPort"
14c5c36c:"doHashClone: failed to allocate memory for hash object\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::doHashClone"
14ffd31b:"Unknown/unset local IP","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::Pinger"
1504ef09:"Invoke (AppCapabilityChangedHandler) name = %s, access = %d, args = %p","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::Invoke"
15187d9e:"tlsSetVerifyPeer: invalid session","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsSetVerifyPeer"
15643315:"Verifying file %s","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
1585de6c:"exit CapabilityListener()","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::CapabilityListener"
15945eee:"Count decreased to %u, because object ID%u (%p, type=%d) was destroyed","RootTools/roottools/spl/include/rt/rt_object_registry.hpp";"rt::ObjectRegistry::remove"
15bb7928:"Attempt to set min number of thread pool threads higher than max","RootTools/roottools/auf/src/thread_pool.cpp";"auf::createCompatThreadPoolExecutor"
16041d16:"BCryptGenRandom: failed to fill buffer with random bytes, status=%ld","RootTools/roottools/spl/src/sysdeps/win/platform_rng_win.cpp";"spl::platformRNGGetRandomBytes"
162019b4:"Thread %s starting\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::run"
163bf171:"lldp.readPortId: no access","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::LLDPreader::readPortId"
167de5aa:"insert()","RootTools/roottools/auf/private/misc.hpp";"auf::Cache<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,bool,struct std::chrono::steady_clock>::insert"
16b2d42d:"Unexpected SCTP notification %d","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::ProcessSctpToUser"
17115d91:"Obtaining app data path from SLIMCORE_TEMP_PATH env variable, value=%s","RootTools/roottools/spl/src/path_location.cpp";"spl::internal::getCustomTempDir"
17335044:"spl::socketAccept(): failed ioctlsocket(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketAccept"
1745b368:"**** Error 0x%x building issuer name","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::logPeerCert"
1756a15a:"Closing socket %I64u from thread %u","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::doOffloadedSocketClose"
178e8112:"LinkInfo Link ID %u: Center Channnel = %u MHz, Bandwidth = %u, RSSI = %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::LogConnectionQuality"
17a0bfca:"TimerHandler: unable to create the m_sema sema. Fatal.\n","RootTools/roottools/auf/src/thread_pool_timer.cpp";"auf::TimerHandler::TimerHandler"
18291a00:"S.%I64u Strand created, served by P.%s id 0x%I64x","RootTools/roottools/auf/src/thread_pool.cpp";"auf::createStrandCore"
182eeb1f:"usrsctp_setsockopt SO_LINGER: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
18348b2e:"delegate %p removed!","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::removeDelegate"
185e2f5e:"RegisterFromIReferencedFile: registered model %s from IReferencedFile %p","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromIReferencedFile"
186eb0c1:"getPrimaryInterface got no interface","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::gatherNetworks"
18730d4f:"auf::init() reinitialization, auf has been initialized %d times","RootTools/roottools/auf/src/auf.cpp";"auf::internal::init"
1877d66d:"WinHashCngImpl::finalize: BCryptFinishHash failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinHashCngImpl::finalize"
188b3abe:"Received response for hop %d","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::processPing"
18989c27:"No transport","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::Pinger"
18bcacfe:"Resolver operation failed","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::ResolverOperation::start"
18cc3227:"Logmap json file, no loglines array: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_read_file"
194153a5:"Buffer updated, adding new buffer (storeUnsafe=%s,levels=%s)","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogBuffer"
194797b2:"Getting model.cat from TarFile","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
196d9377:"WinAsymmCryptoCngImpl::verifySignature: unsupported algorithm type: %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::verifySignature"
1978a40c:"RQ%u: Cannot resolve against non-hierarchical base: %s","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Resolve"
197a3518:"Stopped","RootTools/roottools/net/src/dns_cache_v2.cpp";"`anonymous-namespace\'::DNSCache::Stop"
1b1759c0:"Registers dump:\n","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logRegistersFromContext"
1b33d7bf:"RQ%u: Sent %d bytes","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::RequestChunkSent"
1bce5c5b:"SuspensionManager::strandRegisterTask","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandRegisterTask"
1c2ffe32:"RQ%u: Decompressed %d -> %d, leftover %d","RootTools/roottools/httpstack/src/sysdeps/rt/decompressor.cpp";"http_stack::skypert::Decompressor::Decompress"
1c998488:"generateCert: bad hash algorithm","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::generateCert"
1caf1e0a:"\n*****************************************************************************\n*  ERROR! RootTools synchronous log in use! Do NOT use this in production!  *\n*****************************************************************************\n","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::addFilter"
1d83489d:"MonitorOperation::cleanup_v4only","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::cleanup_v4only"
1dae7f28:"Error: %s","RootTools/roottools/net/src/proxy_tcp_connect_v2.cpp";"`anonymous-namespace\'::ProxyOperationV2::HTTPResponseReceived"
1ded4cd1:"ERROR: Invocation of wait() from AsyncOperation onTerminalStateReached() callout.","RootTools/roottools/auf/src/async_op.cpp";"auf::AsyncOperation::waitCore"
1e4ad618:"(%p) Start TLS, local %s","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::IStreamSocketDelegate_connected"
1e51f8d6:"WlanEventMetricsOperation::start(): not starting due to privacy flag","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanEventMetricsOperation::start"
1e5e6ba0:"RQ%u: Chunked encoding support is not implemented","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Launch"
1e6be14c:"spl::socketCreate(): failure socket(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketCreate"
1e93b1f6:"%s","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::deferredStart"
1e9f3303:"Dispatcher %p: error, can\'t run runUntilQuit with no semaphore to wait for.","RootTools/roottools/auf/src/dispatcher.cpp";"auf::Dispatcher::runUntilQuit"
1ec4700b:"rtnet::priv::WlanApiHandle::WlanApiHandle(): WlanOpenHandle error 0x%x","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanApiHandle::WlanApiHandle"
1ed5231a:"RequestAccessAsync set completion failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocatorImpl_RequestAccessAsync"
1eef2cdd:"Socket disconnected during success callout","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::IStreamSocketDelegate_error"
1f1b3bf5:"WindowsReactorImpl::instanceWindowProc","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::instanceWindowProc"
1f42182b:"Destroyed","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::~RequestPool"
1f516c47:"doHashClone: BCryptDuplicateHash failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::doHashClone"
1f66be3f:"verifyServerCert: Failed to encode Public Key Info 2","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::verifyServerCert"
1f6725ba:"NAT64 IPv4 address: %s","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::splitUpAddresses"
1f7941d1:"WinAsymmCryptoCngImpl::setPublicKey: decodeDerPublicKey failed","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::setPublicKey"
1f8c82b3:"WindowsReactorImpl::registerHandle COMPLETE","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::registerHandle"
1f8d2170:"%sbroken bytes %I64u\n","RootTools/roottools/auf/src/thread_pool.cpp";"auf::logPoolStats"
1fbb3318:"Interface cache invalidated, re-fetching network interfaces async","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::dispatchChange"
2007cf40:"WinSymmCryptoCngImpl::doOp: BCryptEncrypt failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::doOp"
201f4638:"LockfreeStackPool: safe allocation failed (2)\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::allocate"
2062089e:"Unknown dest IP","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::PingerNF"
2070b4c1:"spl::atStop: no handle leaks","RootTools/roottools/spl/src/file.cpp";"spl::countOpenFileHandles::<lambda_...>::operator ()"
2076c99f:"SuspensionManager::unregisterTask","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::unregisterTask"
20cc699b:"WARNING: totalFreeBytes != binFreeBytes + markerFreeBytes!\n","RootTools/roottools/auf/src/thread_pool.cpp";"auf::logPoolStats"
20d54558:"doLoad: Failed to read file %s: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::doLoad"
21184816:"LogTrigger %s has triggered","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogTrigger::ILogAppender_log"
211c727a:"getNetwork: CachedNetworkListOperation wait failed (timeout=%s)","RootTools/roottools/net/src/netmon.cpp";"rtnet::getNetworks"
213494ce:"RegisterFromFile: replacing existing model %s with a new one, from path %s","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromFile"
213ccedf:"AUF: WARNING! Log line too huge; computed size = %zu bytes. Max size including bookkeeping: %zu bytes.\n","RootTools/roottools/auf/src/log2.cpp";"auf::internal::LogArgsIpcRecord::populate"
2145f0d2:"auf::Mutex lock attempt: Thread %u holding %s (%p)\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::ThreadDumper::dumpThreadsIfCollected"
215ef1d4:"RQ%u: Cannot launch HTTPRequest: %s","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::ConnectionAvailable"
216bd12e:"Failed to create dump log file","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::triggeredDefered"
21aeb2f1:"spl::priv::setReuseAddr(): failed setsockopt(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::setReuseAddr"
21f1afc8:"Loading of %s failed, hash mismatch (%s vs expected %s)","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile::Read::<lambda_...>::operator ()"
221f33d4:"RQ%u: OnResponseReceived callout: HTTP %u, body size %d","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::CalloutOnResponseReceived"
22236579:"Missing handle wlanHnd=%p wlanDll=%p","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanEventMetricsOperation::onTerminalStateReached"
22ed88aa:"spl::sockeSetOption(): socket %I64u, setsockopt(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketSetOption"
22f15b3c:"WindowsReactorImpl::handleRegisterMessage","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::handleRegisterMessage"
2300d2a8:"RoGate init failed\n","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::unregisterListener"
2309d616:"\n*****************************************************************************\n*  ERROR! RootTools synchronous log in use! Do NOT use this in production!  *\n*****************************************************************************\n","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactoryImpl::internalAddAppender"
2337dd66:"spl::isWritable(): OpenProcessToken() failed, error code %lx","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::pathIsReadWritable"
23584be1:"certExpireIn: time comparison failed: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::certExpireIn"
23b1b6e2:"Compatibility thread pool max threads: %u","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
23da67a7:"TimerHandler(%p)::purge: %u incoming timers and %u live timers were removed","RootTools/roottools/auf/src/thread_pool_timer.cpp";"auf::TimerHandler::purgeCore"
23dbd4f5:"Attempt to create rtnet::SystemProxyManager during shutdown, failing","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::getSysProxyManager"
241c1c9c:"ONNX runtime (onnxruntime.dll) not found/usable, ONNX inference would not work","RootTools/roottools/inference/inference_engine/src/onnx_engine.cpp";"`anonymous-namespace\'::OnnxRuntime::Make"
2463611f:"generateRsaKey: start","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::generateRsaKey"
2465c62c:"Failed to write logmap file: %s: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_write_file"
24ae92e1:"Socket disconnected during success callout","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::IStreamSocketDelegate_error"
24b42750:"WindowsReactorImpl::unregisterHandle","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::unregisterHandle"
24b45c16:"listNetworkInterfacesAsync()","RootTools/roottools/net/src/netinfo.cpp";"rtnet::listNetworkInterfacesAsync"
250c2242:"Connect: Server reset connection","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::Connect"
2518ed46:"Did not match:","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::checkV4Addresses"
251dd297:"Frm#\tAddress\tSymbName\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::logStackBackTraceDbgHelpCore"
25577cd9:"WinAsymmCryptoCngImpl::setPrivateKey: BCryptImportKeyPair failed with NTSTATUS %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::setPrivateKey"
2561027b:"RoGate init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::DisplayAccessHint"
25e5ce95:"SplOpaqueUpperLayerThread: (ctor this=%p) std transport creation failed\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::SplOpaqueUpperLayerThread"
2600ca99:"MonitorOperation::initialize_v4only","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::initialize_v4only"
26192ca5:"RQ%u: Configuration [connectionReuse=%u, maxRedirects=%u, requestTimeLimit=%s]","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Open"
26254184:"Avoiding proxy path, because of ProxyPolicy::DirectOnly","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::deferredStart"
2626bdba:"Could not open logmap file: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_read_file"
26399c72:"HTTP Request: %s %s","RootTools/roottools/net/src/proxy_tcp_connect_v2.cpp";"`anonymous-namespace\'::ProxyOperationV2::SendRequest"
2678eb67:"startWithAddress: spl::socketListen(%s:%d): %s","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::startWithAddressDeferred"
26f1fe0c:"gatherNetworks: completed in %s","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::INetworkInfoDelegate_interfaces"
270a78b6:"threadSetPriority failed\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::win::threadSetPriority"
2724a3d3:"MFThreadPool: MFUnlockQueue() failed (m_queueId = %d) err = %x\n","RootTools/roottools/spl/src/sysdeps/win/thread_pool_win.cpp";"`anonymous-namespace\'::MFThreadPool::UnlockQueue"
27631114:"Thread %s stopping\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::run"
27d40fda:"rtnet::priv::WlanApiWrapper::WlanApiWrapper(): Loading wlanapi.dll failed","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanApiWrapper::WlanApiWrapper"
2809a3eb:"getNetwork: NetworkMonitor::get failed","RootTools/roottools/net/src/netmon.cpp";"rtnet::getNetworks"
28332f5e:"Captured back trace is empty","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::logBackTraceInfoForThread"
28357c62:"DtlsKeyCertManager::threadEntry: cert already expired","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::DtlsKeyCertManager::threadEntry"
284e514e:"spl::internal::dumpMemory: begin = %p, size = %I64u\n","RootTools/roottools/spl/src/debug.cpp";"spl::internal::dumpMemory"
28726399:"Unknown model type, assuming default","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Check::<lambda_...>::operator ()"
287583de:"sendRate cache expired","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::sendRate"
28833106:"fileOpen: invalid disposition, idx=%u","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::WinSplFileImpl::fileOpen"
288a2afc:"spl::isWritable(): GetFileSecurity() failed, error code %lx","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::pathIsReadWritable"
289df504:"Log file dumped to %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::dumpLogBuffer"
28ec4899:"verifyServerCert: error: CertGetCertificateChain failed %x","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::verifyServerCert"
28faf594:"createConvertedString: failed to convert: %lx.","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::createConvertedStringAndCapacity"
2947aefa:"RQ%u: Complete read with EOS","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::processStreamRequest"
2955f6fa:"WlanRssiListener WlanRegisterNotification result: %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::start"
2966c6a1:"Failed to get engine count [adapter = %d], error = %x","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetEngineCount"
2973f028:"ProxyManagerV2 discovered %u proxies%s","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::discoverProxyInSeparateStrand"
29b0339e:"spl::socketPeerAddress(): socket %I64u, getpeername(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketPeerAddress"
2a331b3e:"TlsEndpoint: %s, %s","RootTools/roottools/auf/src/tls_endpoint.cpp";"auf::`anonymous-namespace\'::TlsEndpoint::logOpensslError"
2a52f337:"lldp.readChassisId: no access","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::LLDPreader::readChassisId"
2a5fa4a3:"Proxy auto-configuration subsystem is busy","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
2a7d3ad6:"entry removeDelegate","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::removeDelegate"
2a80ce64:"drain atStop queue (%d): %s","RootTools/roottools/spl/src/spl_common.cpp";"spl::priv::drainAtStopQueue"
2aae8fb0:"NAT64 prefix discovery failed with %s (code %d)","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INat64PrefixResolveDelegate_prefix"
2aed0187:"Primary index is: %lu","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getPrimaryIfIdx"
2b0c7222:"Expected \'%c\'\n","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::parse_char"
2b19c02d:"RQ%u: Created","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::RequestOp"
2b2efb09:"MonitorOperation::dispatchResume","RootTools/roottools/auf/src/background.cpp";"auf::internal::MonitorOperation::dispatchResume"
2b66d1a9:"Mother board SN: %s","RootTools/roottools/spl/src/sysdeps/win/fingerprint_win.cpp";"spl::internal::appendBoardSN"
2be2c846:"Receiver done","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::ReceiverHasDoneAll"
2c0f4d37:"Failed to get engine count [adapter = %d], D3DKMTQueryStatistics is not loaded","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetEngineCount"
2c642f5b:"%u cache entries stored (%u bytes)","RootTools/roottools/net/src/dns_cache_v2.cpp";"`anonymous-namespace\'::DNSCache::StorePersistent"
2c6d813b:"startDeferred","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::UdpBindOperationWinsock::startDeferred"
2c736c2a:"ONNX inference session %s (%p), loading metadata keys failed: %s","RootTools/roottools/inference/inference_engine/src/onnx_session.cpp";"inference::onnx::OnnxInferenceSession::OnnxInferenceSession"
2c74aa02:"spl::sockeOption(): socket %I64u, getsockopt(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketOption"
2cef5fc6:"Failed to load wintrust.dll","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
2d10ee52:"Created: maxParallelRequests=%u retries=%u delays=%s","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::RequestPool"
2d50cc2a:"RQ%u: IntroduceSender: Server reset connection","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IntroduceSender"
2d595077:"rtnet::priv::WlanApiHandle::WlanApiHandle(): No wlan dll handle","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanApiHandle::WlanApiHandle"
2d5c408e:"System %.4s battery is detected: %umWh","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
2d6daaf2:"Lockfree space depletion in BufferQueue::enqueueDesc(). Terminating","RootTools/roottools/net/private/net/common.hpp";"rtnet::BufferQueue<struct rtnet::DatagramOutputBuffer>::enqueueDesc"
2e1144a6:"Setup: generateEcdsaKeyCertOpenssl: %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Setup"
2e3baaa9:"Stacktrace of %s:\n","RootTools/roottools/auf/src/mutex_orderer_tree.cpp";"auf::internal::MutexOrdererTree::checkForLockInversions"
2e3d6cd4:"ReadGeoPositionAsync_MOCK used","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::ReadGeoPositionAsync"
2e5ccca1:"WindowsReactorImpl::dtor","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::~WindowsReactorImpl"
2e69f62f:"Path::appendComp: Unable to convert extension %s","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::Path::appendComp"
2e7052d0:"BCryptOpenAlgorithmProvider for hmac algorithm %d failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::CngCryptData::initCngHmacProvider"
2e88861f:"WinHttpGetIEProxyConfigForCurrentUser for [%s]: %s","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
2eadecaf:"fileOpen: FS_EXCL_READABLE_FILE && (foa & FOA_WRITEONLY)","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::WinSplFileImpl::fileOpen"
2f006ea6:"unable to get model metadata.json: %s (%d)","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Check::<lambda_...>::operator ()"
2f710a05:"MonitorOperation::onTerminalStateReached","RootTools/roottools/auf/src/background.cpp";"auf::internal::MonitorOperation::onTerminalStateReached"
30177003:"(%p) Connect to %s:%d via %s proxy at %s:%u","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::startProxyConnectionsV1"
3038445b:"spl::fileWrite(%s): %s","RootTools/roottools/spl/src/json_file.cpp";"rt::priv::SaveJsonFileSerialized"
30583cee:"fileOpen: FS_EXCL_WRITABLE_FILE && (foa & FOA_READONLY)","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::WinSplFileImpl::fileOpen"
305b1550:"SCTP_COMM_UP: out_streams=%d in_streams=%d","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::OnAssocChangeEvent"
3061e3bd:"createTempFile(model.json) failed with %d(%s)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
306ebc6c:"entry run","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::`anonymous-namespace\'::ThreadExec::run"
30784c9b:"Loading of %s failed during reading, %s (%d)","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile::Read::<lambda_...>::operator ()"
30a884a6:"Build time: %s %s","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
30d02ded:"startWithAddress: connecting to [%s]:%d","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::startWithAddressPair"
30d1d780:"spl::switchRealtimeStreamingMode(): Missing functions in wlanapi.dll","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::priv::wlanApiInit"
30d863fe:"Plan stream reset %s@%d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::PlanStreamReset"
30db0b3a:"RQ%u: Fail read %s","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::processStreamRequest"
30de6475:"spl::socketRecvFrom: recvfrom: %s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketRecvFrom"
31281639:"RQ%u: Shutdown","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::shutdown"
3143412a:"MonitorOperation::dispatchSuspending","RootTools/roottools/auf/src/background.cpp";"auf::internal::MonitorOperation::dispatchSuspending"
31438623:"generateCert: signing the cert failed: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::generateCert"
318d80ad:"spl::switchRealtimeStreamingMode(): Loading wlanapi.dll failed","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::priv::wlanApiInit"
319fa144:"Sending a fake ClientHello","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::do_start"
31a6f229:"WinKeyPairGenerationCngImpl::exportPublicKey unsupported format %u","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::exportPublicKey"
31ceb2e1:"Received IPv6 address for IPv4 query","RootTools/roottools/net/src/dns.cpp";"fillAddressList"
31dbbe48:"  WLAN Rate Set: {%s}","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::LogConnectionQuality"
31e17096:"INetwork::GetConnectivity failed with hr=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::constructNetwork"
33392075:"Wlan: IAppCapabilityInfoDelegate_Change: %s : %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanCapabilityChecker::IAppCapabilityInfoDelegate_Change"
336fd307:"Setup: SSL_CTX_check_private_key: %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Setup"
338ca2bd:"Setup: Failed to use private key. %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Setup"
339376c8:"Zero prefix length - interface: %s (%I64x) ip: %s","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INetworkInfoDelegate_interfaces"
33c09516:"Appender removed, detached=%s","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::removeAppender"
33f1117e:"generateRsaKey: completed %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::generateRsaKey"
33f8cc61:"start","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::TraceRouteOperationNF::start"
34125a18:"TraceRouteOperation::ctor","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::TraceRouteOperationNF::TraceRouteOperationNF"
342c0e37:"DROP SCTP PACKET for user, SCTP type %02x, packet len %I64u","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssocProxy::OnPacketFromSctpToUser"
34423c29:"LockfreeStackPoolImp: safe allocation failed\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::allocateCore"
345a1ba7:"Pinger::cancelSync","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::cancelSync"
345c18cf:"SplOpaqueUpperLayerThread: (ctor this=%p) event creation failed\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::SplOpaqueUpperLayerThread"
34645fe9:"Visiting log buffer normal order","RootTools/roottools/auf/src/log2.cpp";"auf::LogBuffer::deferredVisit"
3465add9:"Invalid thread handle for backtrace, probably because the thread has terminated\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_context_native.cpp";"spl::priv::captureContextForThreadCoreBegin"
346f0c7b:"Failed to get gpu memory status [adapter = %d], not able to retrieve segment Count","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUMemoryStatus"
34855f3f:"DNS result IPv4: %s","RootTools/roottools/net/src/sysdeps/berkeley/happy_eyeballs_bsd_v2.cpp";"`anonymous-namespace\'::HappyEyeballsV2::IDnsResolveDelegate_address"
348cbf7e:"Unable to parse binary.json","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::getBinaryMetadata"
3594eda8:"RegisterSuspendResumeNotification failed; error=%lx","RootTools/roottools/auf/apal/sysdeps/win/connected_standby.cpp";"apal::registerForConnectedStandbyPowerNotifications"
3599aeae:"sendAsync: dest IP not set","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::sendAsync"
359d3176:"threadSetMmCharacteristics failed with error = %lx\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::win::threadSetMmCharacteristics"
35fc00f2:"sendAsync: already in progress","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::sendAsync"
35fc851d:"makeAuthProvider: unable to create authProvider for method=%s","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::createTopMethod"
362da947:"Attempt to create rtnet::Disconnecter during shutdown, failing","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::get"
3632addd:"generateResponse: no more data from server for auth","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::generateResponse"
369cad78:"%s","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"GetProxyManagerV2"
36a757fa:"BCryptGetProperty BCRYPT_OBJECT_LENGTH for hash algorithm %d failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::CngCryptData::initCngHashProvider"
36b54683:"INetworkInfoDelegate_error","RootTools/roottools/net/src/netmon.cpp";"rtnet::priv::NetworkMonitor::INetworkInfoDelegate_error"
36b567eb:"CryptEncodeObjectEx (get blob) failed with error 0x%lx\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt.cpp";"spl::internal::CryptEncodeObjectExWrap"
36cd60ba:"Destroyed","RootTools/roottools/httpstack/src/httpstack_impl.cpp";"http_stack::HttpStack::~HttpStack"
36cfdd29:"Failure binding socket for %s:%d","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::doUdpBind"
36efe485:"Missing handle m_wlanHnd=%p wlanDll=%p","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::start"
3752dbec:"Address::Address: sockAddrFromPresentationString() failed for address %s port %d","RootTools/roottools/net/src/address.cpp";"rtnet::Address::fromString"
3770f074:"zlib::deflate() returned error: %d","RootTools/roottools/auf/src/log2.cpp";"auf::CompressWriter::write"
3789ca82:"GetNetwork failed. hr %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getNetworkParams"
3798e32e:"spl::atStop: %d leaked handles","RootTools/roottools/spl/src/file.cpp";"spl::countOpenFileHandles::<lambda_...>::operator ()"
37a4ec8a:"verifyServerCert: CertVerifyCertificateChainPolicy failed 0x%x","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::verifyServerCert"
38074521:"DNS result IPv6: %s","RootTools/roottools/net/src/sysdeps/berkeley/happy_eyeballs_bsd_v3.cpp";"`anonymous-namespace\'::HappyEyeballsV3::IDnsResolveDelegate_address"
380cc492:"TarFile::GetFile(model.json) failed with %d(%s)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
381be2e9:"clearTransport: illegal transport key (%u)","RootTools/roottools/auf/src/thread.cpp";"auf::ThreadRef::clearTransport"
3864b649:"%sfree bytes (total, marker + bin: %I64u, %I64u + %I64u = %I64u)\n","RootTools/roottools/auf/src/thread_pool.cpp";"auf::logPoolStats"
38b77337:"Loading of %s failed, size mismatch (%zu vs expected %zu)","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile::Read::<lambda_...>::operator ()"
38ebd859:"tlsConnect: read error %s(%d)","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsConnect"
38edcd7c:"Set high accuracy failed","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::realReadGeoPositionAsync"
390c062b:"SCTP_STREAM_RESET error notification","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OnStreamResetNotification"
391bbaad:"delegate %p not found!","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::removeDelegate"
392c5b49:"spl::pathReplace(%s,%s): %s","RootTools/roottools/spl/src/json_file.cpp";"rt::priv::SaveJsonFileSerialized"
399097fc:"entry addDelegate","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::addDelegate"
39a2316b:"startTlsAsyncDeferred(): ongoing TLS upgrade; ignoring this call","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::startTlsAsync"
39f6a79e:"doHashInit: failed to allocate memory for hash object\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::doHashInit"
3a04fbf1:"exit blocking_call","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::`anonymous-namespace\'::ThreadExec::blocking_call"
3a53d191:"Got Geo CapabilityAccessResponse: %s","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocationCapabilityChecker::RequestAccessAsync::<lambda_...>::operator ()"
3a63ef8d:"entry CapabilityMonitor","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::CapabilityMonitor"
3a707b9c:"WinAsymmCryptoCngImpl::setPublicKey: decodePemPublicKey failed","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::setPublicKey"
3a72e9a6:"Bin 0 (Sizes 0-%I64u)%s\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackBin::dump"
3a7b7b44:"generateCert: encoding cert to DER failed: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::generateCert"
3a7e5582:"binary.json not found","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::getBinaryMetadata"
3a937fdf:"Initialized DTLS OpenSSL backend","RootTools/roottools/net/src/dtls.cpp";"rtnet::internal::DtlsImpWrapper::imp"
3a9b1e54:"System cannot run on battery power","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
3aa411cb:"sendNextPacket","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::DatagramSocketImpl::sendNextPacket"
3b104973:"LockfreeStackPool: safe allocation failed (1)\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::allocate"
3b1dfdca:"HMACWinCngImpl::update: BCryptHashData failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::HMACWinCngImpl::update"
3b351cc1:"verifyServerCert: CertVerifyCertificateChainPolicy trust error: 0x%x: %s","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::verifyServerCert"
3b7df82d:"MonitorOperation::start COMPLETE","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::start"
3b9f101e:"Attempt to create rtnet::NetworkMonitor during shutdown, failing","RootTools/roottools/net/src/netmon.cpp";"rtnet::priv::NetworkMonitor::get"
3bcd135e:"SuspensionManager::strandCheckIfReadyForSuspended","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandCheckIfReadyForSuspended"
3bdb88f8:"RQ%u: Unsupported protocol \'%s\'","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::GetPromise"
3be1b9ec:"LogTrigger %s: condition %s met","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogTrigger::ILogAppender_log::<lambda_...>::operator ()"
3be86c1a:"Failed to write logmap file: %s: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_write_file_cpp"
3bf3d114:"RequestWlanAccessSync wait timeout: %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::RequestWlanAccessSync"
3d5fc697:"tlsWrite: exit: Encryption failed: err %d","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsWrite"
3d716189:"WinAsymmCryptoCngImpl::doOp: BCryptDecrypt failed with NTSTATUS %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::decrypt"
3da66520:"Cannot load ecs_override.conf: %s","RootTools/roottools/spl/src/ecs.cpp";"`anonymous-namespace\'::EcsConfig::EcsConfig"
3dd91277:"doDtlsConnectAccept: peer BIO_ADDR creation failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::doDtlsConnectAccept"
3debb724:"(%p) Connect %s -> %s:%d via %s proxy at %s:%u","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::startProxyConnectionsV2"
3df49e15:"Getting INetworkCostManager failed with HRESULT=0x%08x, which is not expected on Win8+; contining without it","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
3e116a01:"Flush: %s","RootTools/roottools/spl/src/rt_persistent.cpp";"`anonymous-namespace\'::PersistentStorage::Flush"
3e2803c8:"usrsctp_init","RootTools/roottools/sctp/src/sctp_stack.cpp";"rtsctp::priv::SCTPStack::SCTPStack"
3e335776:"Failed because there is no WindowsReactor, probably because we\'re shutting down","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::start"
3e48627b:"Loading of %s failed, invalid expected size/hash supplied","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile::Read::<lambda_...>::operator ()"
3e4cd0f6:"INetworkInfoDelegate_error","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INetworkInfoDelegate_error"
3e70bb94:"Max size global lock free stack pool size (L2 of num bytes): %u","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
3e889436:"Geo: IAppCapabilityInfoDelegate_Change: %s : %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocationCapabilityChecker::IAppCapabilityInfoDelegate_Change"
3ec515c3:"RQ%u: Cannot init zlib context error %d (errno %d)","RootTools/roottools/httpstack/src/sysdeps/rt/decompressor.cpp";"http_stack::skypert::Decompressor::createContext"
3ed968bb:"createTempFile(model.cat) failed with %d(%s)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
3f2d9729:"usrsctp_setsockopt SCTP_RESET_STREAMS: error (%d) %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::ResetOutStreams"
3f7eb3bb:"DbgHelpFunctions.load %d -> %d","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::DbgHelpFunctions::load"
3f8ded77:"Bad marker byte encountered","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay1"
3f929614:"Using system proxy override, %s:%u, user=%s","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::getSystemProxyListForUrl"
3fa0b833:"Platform is not capable of creating IPv6 sockets","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"checkIpv6Capability"
3fd93e68:"Cached auth method succeeded","RootTools/roottools/net/src/proxy_tcp_connect_v2.cpp";"`anonymous-namespace\'::ProxyOperationV2::DoStart"
401f2cf8:"MonitorOperation::start","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::start"
4029ae1e:"InterfaceImpl::wlanBssInfo(): off due to privacy flag","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::wlanBssInfo"
40441a56:"TimerImpStateMachine::startDispatch: Illegal state","RootTools/roottools/auf/src/thread_pool_timer_sm.cpp";"auf::TimerImpStateMachine::startDispatch"
40548b1b:"Not persisting log config, disabled","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::persistConfig"
40b407ac:"InterfaceImpl: updating m_bssinfo cache: has_value: %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::wlanBssInfo"
40c5f46c:"Directory path is %s","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
40e7804e:"Has native thread pool. Used by default: %s\n","RootTools/roottools/spl/src/spl_common.cpp";"spl::sysInfoLogDetails"
410f9deb:"ThreadPoolStop: Strand count %d, not dropped to 0.","RootTools/roottools/auf/src/thread_pool.cpp";"auf::internal::threadPoolStop"
414d12fb:"Failure creating accept socket, family %d","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::accept"
418888a8:": extent used multiple times (eg crosslinked somehow). First faulting chunk: address 0x%I64x (chunk %I64u)\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"clearRange"
4195c005:"SetupDiGetClassDevsW failed with code %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
41bbde18:"usrsctp_set_non_blocking: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
4204eebc:"getVPNName: RasEnumConnectionsW(%p, %d, %d) error %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::getVPNName"
4235117e:"spl::socketListen: listen: %s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketListen"
42357487:"Unknown/unset local IP","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::PingerNF"
425148dc:"auf::stopInternal() component %p -> %s still in initialization list, initialized %I64u times","RootTools/roottools/auf/src/auf.cpp";"AufInitializationRegistry::handleFinalAufStop"
427a73bb:"WinKeyPairGenerationCngImpl::exportPublicKey: encode X509_PUBLIC_KEY_INFO failed\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::exportPublicKey"
428bc335:"Address::presentationString: unspecified address stored (family %u), returning empty string","RootTools/roottools/net/src/address.cpp";"rtnet::Address::presentationString"
42a9aafd:"OUT_RESET_REQUESTED %s@%d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::SendOutStreamResets"
42d0e066:"spl::pathCreateFromFixed(): Could not add path component %s","RootTools/roottools/spl/src/path_location.cpp";"spl::pathCreateFromFixed"
434b2a96:"reapplyLogLevels()","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::reapplyLogLevels"
435db34d:"Handshake: DTLS_set_link_mtu: %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Handshake"
43682981:"RegisterFromIReferencedFile:  replacing existing model %s from IReferencedFile %p with a new one","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromIReferencedFile"
438cdb24:"MutexOrderer v2 enabled: %u; aborts: %u","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
43d57135:"RQ%u: Cannot redirect %d with stream body","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::DetectRedirect"
44188291:"socketBind: %s","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::doConnectSingle"
44361bd1:"spl::isWritable(): couldn\'t allocate memory for the SECURITY_DESCRIPTOR structure","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::pathIsReadWritable"
44615eb6:"spl::switchRealtimeStreamingMode(): found connected interface\n","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::priv::switchRealtimeStreamingMode"
44663f2b:"memcpy_s buffer overlap: dest=%p destsz=%I64u src=%p count=%I64u","RootTools/roottools/spl/src/wrap.cpp";"splFailBufferOverlap"
448326be:"S.%I64u LFSP StrandExecutor suspicious queue size: %d","RootTools/roottools/auf/src/thread_pool_strand.cpp";"auf::StrandExecutorImp::post"
44f20855:"Expected: \'\"\'\n","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_unescape"
44f2e299:"splitRecordsToMTUSize: cannot split DTLS packet to fit into MTU","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::splitRecordsToMTUSize"
4540e156:"%s","RootTools/roottools/spl/src/rt_persistent.cpp";"GetDBManager"
45b8ae00:"MonitorOperation::strandResume","RootTools/roottools/auf/src/background.cpp";"auf::internal::MonitorOperation::strandResume"
45baa890:"logReadLogmap(%s)","RootTools/roottools/auf/src/logmap.cpp";"auf::logReadLogmap"
45c1dd08:"WinKeyPairGenerationCngImpl::generateKeys: BCryptFinalizeKeyPair failed with NTSTATUS 0x%x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::generateKeys"
45ce3d93:"No transport","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::PingerNF"
461fd53c:"InternetConnectivityOperation::start()","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityOperation::start"
462239d6:"LogMap filter updated, adding new filter","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogMapFilter"
464346f3:"Failed to close adapter by handle, error = %x","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetAdapterFromLUID::<lambda_...>::operator ()"
468b03a6:"No networking installed","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
46925e77:"Current time: Local=%u-%02u-%02uT%02u:%02u:%02u.%03u ; Utc=%u-%02u-%02uT%02u:%02u:%02u.%03u ; tzBias=%ds","RootTools/roottools/auf/src/log2.cpp";"auf::logLocalTimestamp"
469b8eb6:"set g_scopeOfPrivacySensitiveApis from %u to %u","RootTools/roottools/net/src/netinfo.cpp";"rtnet::setScopeOfPrivacySensitiveApis"
46d8cb8c:"Log level change %s: %s -> %s","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactoryImpl::setLevel"
46ff3150:"cryptProtectEncrypt: ::CryptProtectMemory() failed %lu","RootTools/roottools/spl/src/sysdeps/win/cryptprotect_win.cpp";"spl::encryptWithTempKey"
4703c5f2:"MutexOrderer shutting down\n","RootTools/roottools/auf/src/mutex_orderer_tree.cpp";"auf::internal::MutexOrdererTree::~MutexOrdererTree"
4708d66e:"makeAuthProvider: erroneous authentication method in list: 0x%.2X","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::createTopMethod"
47388158:"External log filtering changed, filtering %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::updateLogFileConfig"
4744971d:"The hash representing the subject or the publisher wasn\'t explicitly trusted by the admin and admin policy has disabled user trust. No signature, publisher or timestamp errors","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
4798e68e:"Log lines lost while asynchronous pipeline was stopping: %d","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactoryImpl::internalStopAsyncThread"
47a1c584:"Logmap json file, invalid logline in %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_read_file"
47de3269:"doLoad: empty file %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::doLoad"
480284d7:"RQ%u: Cannot get location for redirect %d","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::DetectRedirect"
4867f8de:"dtor","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::~TcpListenOperationWin"
48de89bf:"TraceRouteOperation::~dtor","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::TraceRouteOperation::~TraceRouteOperation"
48e932bc:"getKeyCertFileNames: key/cert store not supported 2","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::getKeyCertFileNames"
490b4c97:"auf::Mutex lock attempt: Thread %u waiting for %s (%p)\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::ThreadDumper::dumpThreadsIfCollected"
4954fbf6:"#%02u\t%016I64x\t%s+0x%I64x\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::logStackBackTraceDbgHelpCore"
49717de5:"Socket disconnected during success callout","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::IStreamSocketDelegate_error"
49867c51:"spl::fileOpen(%s, W): %s","RootTools/roottools/spl/src/json_file.cpp";"rt::priv::SaveJsonFileSerialized"
499ad6b5:"Registration for INetworkEvents failed with HRESULT=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
49a5309f:"GetActivationFactory failed","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::getAppCapabilityStatusSync"
49b956ac:"WinHttpGetIEProxyConfigForCurrentUser: error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
49c09465:"recv(%u)","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::recvNextPacket"
49d171e3:"erase()","RootTools/roottools/auf/private/misc.hpp";"auf::Cache<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,bool,struct std::chrono::steady_clock>::erase"
49ed1182:"auf::stopInternal() unmatched auf::stop from %s, stopped %I64u times","RootTools/roottools/auf/src/auf.cpp";"AufInitializationRegistry::handleFinalAufStop"
4a019925:"GeoLocationCapabilityChecker request executed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocatorImpl_RequestAccessAsync"
4a537514:"ThreadPoolExecutorImp: not created well (prio P.%s)\n","RootTools/roottools/auf/src/thread_pool.cpp";"auf::createCompatThreadPoolExecutor"
4a841130:"Aborting due to leaks detection","RootTools/roottools/spl/include/rt/rt_object_registry.hpp";"rt::ObjectRegistry::stop"
4a97bca6:"OS ver = %s build = %ld","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::DisplayAccessHint"
4a983c35:"Failure creating a socket for %s:%d (family %d)","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::startWithAddressDeferred"
4a9ab816:"tlsDecrypt: Session renegotiation not supported","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsDecrypt"
4aab40e5:"verifyServerCert: Failed to encode Public Key Info","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::verifyServerCert"
4aced2a0:"Log file updated, adding log file MaxSize=%I64u MaxRotations=%d Encryption=%d File=%s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogFile"
4aff4af1:"deferredDispatchPrivacyChangeEvent","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::deferredDispatchPrivacyChangeEvent"
4b14d9c4:"Backend %s is not available","RootTools/roottools/httpstack/src/backend.cpp";"http_stack::CreateHttpStack"
4b3dab1c:"spl::priv::clearWindowsQwaveQOSSocketProperty(): handle %p, QOSCloseHandle(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::clearWindowsQwaveQOSSocketProperty"
4b490985:"Is SAW device: %s\n","RootTools/roottools/spl/src/spl_common.cpp";"spl::sysInfoLogDetails"
4b658484:"Hop %d status: Success","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::processPing"
4b80b171:"Could not open logmap file: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_write_file"
4b9815be:"%s","RootTools/roottools/spl/src/sysdeps/win/system_resources_pdh_win.cpp";"`anonymous-namespace\'::getCpuStatsQueryState"
4b9c341e:"rtnet::priv::WlanApiWrapper::WlanApiWrapper(): Missing functions in Rasapi32.dll","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanApiWrapper::WlanApiWrapper"
4ba4b95b:"Sender done","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::SenderHasDoneAll"
4badef02:"GetNetworks failed with hr=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::gatherNetworks"
4bc8d0ef:"decodeCert: failed to decode cert: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::decodeCert"
4be6c0a9:"Configured backend %s, will use %s","RootTools/roottools/httpstack/src/backend.cpp";"http_stack::CreateHttpStack"
4c12bd3f:"Obtaining app data path from legacy SLIMCORE_APP_DATA_PATH env variable, value=%s","RootTools/roottools/spl/src/path_location.cpp";"spl::internal::getCustomAppDataDir"
4c173fba:"Failed to read logmap file: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::LineExtractor::read"
4c477db1:"initCngSymmCryptoProvider: BCryptOpenAlgorithmProvider for algorithm %d failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::CngCryptData::initCngSymmCryptoProvider"
4c4be2d7:"tlsConnect: called after handshake","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsConnect"
4c560c6d:"created","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::WlanRssiListener"
4c9e2ccb:"threadSetMmPriority failed, no characteristics set\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::win::threadSetMmPriority"
4cfb1871:"AsyncOperation::complete: Invalid to attempt complete() in status %u","RootTools/roottools/auf/src/async_op.cpp";"auf::AsyncOperation::complete"
4d0fb7a0:"Spawning new worker (concurrency %u, cur count %u)\n","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::ThreadPoolExecutorImp::triggerUpscaling"
4d3ad928:"listNetworkInterfacesAsync failed with error, cannot proceed","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::INetworkInfoDelegate_error"
4d41c58f:"RQ%u: OnBackendResponse HTTP %d","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::OnBackendResponse"
4da8ca92:"splitRecordsToMTUSize: cannot split DTLS packet to fit into MTU 1","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::splitRecordsToMTUSize"
4de09587:"Expected: hex digit\n","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_unescape"
4dee9246:"%s: IPv4: %s, IPv6: %s, NAT64 prefixes used: %s","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::dispatchChange"
4e23f7ab:"store: %d, %d, %d","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::store"
4e4fc4e3:"ProxyManagerV1 discovered %u proxies","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::discoverProxyInSeparateStrand"
4e5812f8:"Hop %d status: TtlExpired","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::processPing"
4ec513ab:"STATE: S_ACTIVE","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandResume"
4efbe7a3:"spl::traceFileOpenIDs() no open file handles.\n","RootTools/roottools/spl/src/file.cpp";"spl::priv::FileHandlesTracker::log"
4f02dcbc:"Unable to convert string to GUID, error: %ld","RootTools/roottools/spl/src/sysdeps/win/utils.cpp";"spl::priv::win::strToGuid"
4f35429c:"ONNX inference session %s (%p), loading metadata failed: %s (%d)","RootTools/roottools/inference/inference_engine/src/onnx_session.cpp";"inference::onnx::OnnxInferenceSession::OnnxInferenceSession"
4f37cc5c:"Cannot create reactor window","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::eventLoop"
4f686185:"HMACWinCngImpl::HMACWinCngImpl: constructing failed\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::HMACWinCngImpl::HMACWinCngImpl"
4f79e434:"ctor","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::TcpListenOperationWin"
4fc02533:"packetSent(%u, %u)","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::packetSent"
4fd6a954:"Frm#\tAddress\tSymbName\n","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logStackBackTraceManualCore"
5003aaaf:"TLS session handshake: %s","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsHandshakeStep"
5057a8fa:"MonitorOperation::strandSuspending","RootTools/roottools/auf/src/background.cpp";"auf::internal::MonitorOperation::strandSuspending"
50fccfb5:"spl::pathCreateFromFileName(): No \'\\\' found in %s","RootTools/roottools/spl/src/path_location.cpp";"spl::pathCreateFromFileName"
511fd705:"GeoPosition init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::realReadGeoPositionAsync"
51585c55:"Registration for INetworkCostManagerEvents failed with HRESULT=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
51713fe5:"Operation cancel/cleanup done","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::GeoPositionOperation::cancel"
5188cbf1:"toRtnetErrorCode: SEC_E_INCOMPLETE_MESSAGE","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::toHandshakeResult"
518a18f3:"Created thread %lu.\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::threadWinEntry"
51e6b6dc:"pathInitFromLocation: GetModuleHandleExW failed with error %d, no PL_MODULE_DIR","RootTools/roottools/spl/src/sysdeps/win/path_location_win.cpp";"spl::pathInitFromLocation"
51f3c068:"Got Wlan CapabilityAccessResponse: %s","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanCapabilityChecker::RequestAccessAsync::<lambda_...>::operator ()"
52285d54:"usrsctp_bind %d: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
523173c3:"WindowsReactorImpl::handleUnregisterHandle","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::handleUnregisterHandle"
52332c87:"RQ%u: Dequeued","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::DequeueRequest"
526141ef:"%s","RootTools/roottools/spl/src/ecs.cpp";"`anonymous-namespace\'::EcsConfig::instance"
52acb8fe:"Encrypt: input data buffer %zu bytes exceeds data MTU %zu","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Encrypt"
52ca8480:"Failed to get gpu utilization, unexpected adapter index = %d","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUUtilization"
52ebe78d:"onTerminalStateReached","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::Nat64PrefixDiscoveryOperation::onTerminalStateReached"
531b1298:"Local port range %d-%d: retry %d after: (%d) %s","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::doConnectSingle"
5323c912:"tlsHandshakeStep: QueryContextAttributes failed","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsHandshakeStep"
538c8f46:"Found %I64u valid automatic proxies","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getHttpProxies"
539275ef:"packetReceived: WSARecvFrom/WSARecvMsg status %u","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::DatagramSocketImpl::packetReceived"
53af0259:"Obtaining app data path from SLIMCORE_LOG_PATH env variable, value=%s","RootTools/roottools/spl/src/path_location.cpp";"spl::internal::getCustomLogDir"
53de7fc4:"Session %s (%p) inputs:\n%s","RootTools/roottools/inference/inference_engine/src/onnx_session.cpp";"inference::onnx::OnnxInferenceSession::CreateInputPlaceholder"
5416855b:"Failed to create DXGIFactory","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::InitializeDXGIFactory"
5481e134:"proxyCallbackFunc: proxy resolution failed with error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::proxyCallbackFunc"
54896c7b:"CPU time monitor interval too short: %u ms","RootTools/roottools/auf/src/cputime_monitor.cpp";"auf::internal::CpuTimeMonitor::updateInterval"
54c32e40:"Proxy auto-detection is disabled","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
54d2c483:"WinHttpGetProxyForUrl failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutoProxyLegacy"
54e5bf6e:"RQ%u: Total write size %u bytes in %u chunks, total read size %u bytes in %u chunks","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::shutdown"
55140556:"BCryptOpenAlgorithmProvider for hash algorithm %d failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::CngCryptData::initCngHashProvider"
5539cd19:"displayAccessHint: \"%s\" capability: \"%s\"","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::DisplayAccessHint"
5589f114:"InterfaceImpl ctor","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::InterfaceImpl"
55b74202:"TimerImpStateMachine::cancel: Illegal state","RootTools/roottools/auf/src/thread_pool_timer_sm.cpp";"auf::TimerImpStateMachine::cancel"
56207e69:"Received IPv4 address for IPv6 query","RootTools/roottools/net/src/dns.cpp";"fillAddressList"
5637e261:"deletePool: p=%p","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::operator delete"
5699a6bf:"RQ%u: Body size %u","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::analyzeHeaders"
56a7ec71:"Pushed keys: %.70s","RootTools/roottools/spl/src/ecs.cpp";"`anonymous-namespace\'::EcsConfig::setData"
56d8a432:"RQ%u: Destroyed","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::~RequestOp"
575a526c:"spl::socketCreate(): failed ioctlsocket(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketCreate"
577132a9:"pathInitFromLocation: GetTempPathW failed, can\'t get temp path","RootTools/roottools/spl/src/sysdeps/win/path_location_win.cpp";"spl::pathInitFromLocation"
57a0af5d:"Creating temporary model.cat file","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
57bcce9c:"makeAuthProvider: no suitable authentication method","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::createTopMethod"
57c1a95c:"pathInitFromLocation: SHGetFolderPath failed, no PL_APP_DATA_DIR","RootTools/roottools/spl/src/sysdeps/win/path_location_win.cpp";"spl::defaultAppPath"
57c25870:"BCryptGetProperty BCRYPT_OBJECT_LENGTH for hmac algorithm %d failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::CngCryptData::initCngHmacProvider"
57de5fc8:"generateCert: X509_new failed: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::generateCert"
57eabd14:"Route notification arrived: %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::routeChanged"
57f767b0:"WlanConnectionQuality cache expired","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::wlanConnectionQuality"
582223de:"send4: IcmpCreateFile: %d","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::send4"
582fa8cf:"GeoPosition set completion failed","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::realReadGeoPositionAsync"
582ff457:"Loading of %s failed during opening, %s (%d)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::loadArchiveFile"
5835f76c:"Sending request failed at hop %d","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::processPing"
58388d03:"DNS request failed: %s","RootTools/roottools/net/src/sysdeps/berkeley/resolver_bsd.cpp";"`anonymous-namespace\'::AddressResolverOperation::Completed"
585fcbc1:"WinHttpGetIEProxyConfigForCurrentUser failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutomaticProxy"
58975935:"Loaded Modules:","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::logStackBackTraceDbgHelpCore"
589b4877:"prepareMethod %s provided, but not allowed: 0x%.2X","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::AuthenticationHandlerImpl"
58b151f3:"CapabilityCheckBase: not supported: OS ver = %s build = %ld","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::CapabilityCheckBase::PreCheck::<lambda_...>::operator ()"
5907befb:"attachTransport: attempted to attach transport to standard key (%u)","RootTools/roottools/auf/src/thread.cpp";"auf::ThreadRef::attachTransport"
595dbf0f:"getDtlsSrtpParameters: bad arguments","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getDtlsSrtpParameters"
5961bfea:"Bad file signature","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay"
59b111c3:"KeyCertManager: %s, %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::logOpensslError"
59ba3d29:"Getting GeoPosition took %lld ms","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::GeoPositionAsyncOpHandler::Invoke"
59c3117f:"Pinger::~dtor","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::~PingerNF"
59f0bf61:"Geolocation access callback invoked with status %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeolocationAccessAsyncOpHandler::Invoke"
5a0aa789:"Override keys from ecs_override.conf: %s","RootTools/roottools/spl/src/ecs.cpp";"`anonymous-namespace\'::EcsConfig::EcsConfig"
5a15c2c1:"LockfreeStackPool Check: While considering extent addresses 0x%I64x -- 0x%I64x (chunk beginning %I64u -- ending %I64u)","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"partiallyLogFailedCheck"
5a455a08:"socketBindPortRange: invalid range %d-%d","RootTools/roottools/spl/src/bindportrange.cpp";"spl::socketBindPortRange"
5a48f653:"inflateInit2() failed: %d","RootTools/roottools/auf/src/log2.cpp";"auf::CompressReader::CompressReader"
5a7104f6:"SuspensionManager::resume","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::resume"
5a7ab722:"CPU Topology: {logical CPUs: %u, Cores: %u, Packages: %u, NUMA nodes: %u}\n","RootTools/roottools/spl/src/spl_common.cpp";"spl::sysInfoLogDetails"
5a80eb5d:"Startup keys from ecs.conf: %s","RootTools/roottools/spl/src/ecs.cpp";"`anonymous-namespace\'::EcsConfig::EcsConfig"
5a9572ed:"InternetConnectivityOperation::InternetConnectivityOperation()","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityOperation::InternetConnectivityOperation"
5a9ebe1d:"doTLSConnect(): TLS handshake failed with remote %s (using cert. name \'%s\')","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::doTLSConnect"
5b78bb81:"Traceroute to %s","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::TraceRouteOperation::continueOp"
5c1c9841:"Cannot drain atStop queue: %s","RootTools/roottools/spl/src/spl_common.cpp";"spl::priv::drainAtStopQueue"
5c349515:"getPrimaryInterface picked interface %s, flags=0x%llx","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::gatherNetworks"
5cd39c7c:"Failed to enumerate adapter, DXGIFactory is not initialized","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::EnumerateAdapters"
5d7827f1:"RoGate init failed\n","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::registerListener"
5d93a152:"Failed to get gpu memory status, unexpected adapter index = %d","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUMemoryStatus"
5dc3d12c:"appCapability2->put_DisplayMessage failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::DisplayAccessHint"
5dda39ea:"Creating temporary model.dat file","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
5e037e2c:"End of RootTools build information","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
5e037fbf:"SPL is passing the exception on to the next SEH handler, whichever it is\n","RootTools/roottools/spl/src/sysdeps/win/exception_filter.cpp";"spl::priv::winSehLoggingExceptionFilter"
5e09c4cc:"pathInitFromLocation: GetModuleFileNameW failed with error %d, no PL_MODULE_DIR","RootTools/roottools/spl/src/sysdeps/win/path_location_win.cpp";"spl::pathInitFromLocation"
5e342c92:"(While checking object space, %I64u bytes)\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::checkConsistency"
5e4d30b1:"proxyCallbackFunc: proxy resolution failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::proxyCallbackFunc"
5e637b20:"doConnectSingle: setStreamSocketOptions()","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::doConnectSingle"
5e84456a:"Shutdown","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::Shutdown"
5e9032dc:"AcceptEx() failed: %d %s","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::socketAccepted"
5ea6ba07:"changeIntervals()","RootTools/roottools/auf/private/misc.hpp";"auf::Cache<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,enum rtnet::internal::SystemProxyStatus,struct std::chrono::steady_clock>::changeIntervals"
5ec4dc92:"WlanRssiListener WlanRegisterNotification in terminalState res: %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::onTerminalStateReached"
5edb423a:"Could not open for rotation log dirrectory %s, error %d","RootTools/roottools/auf/src/log2.cpp";"auf::FileLogAppender::rotateLogFile"
5efd8fbf:"WinAsymmCryptoCngImpl::setPrivateKey: CryptDecodeObjectEx failed with error %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::setPrivateKey"
5f0555d8:"WinAsymmCryptoCngImpl::verifySignature: signature size should be equal to rsa_size\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::verifySignature"
5fa40eac:"DATA_CHANNEL_OPEN received %s@%d %s%s","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OnDataChannelOpen"
5ffbba01:"There was an exception in spl::priv::winSehLoggingExceptionFilter(), and it was ignored\n","RootTools/roottools/spl/src/sysdeps/win/exception_filter.cpp";"spl::priv::winSehLoggingExceptionFilter"
601947e2:"InternetConnectivityOperation::~InternetConnectivityOperation()","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityOperation::~InternetConnectivityOperation"
6029b7bd:"Terminal state: cancelled","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::Nat64PrefixDiscoveryOperation::onTerminalStateReached"
6066bfab:"%s","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::prepareHeaders"
608ea3b0:"verifyKeyCertSerialized: empty key","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::verifyKeyCertSerialized"
60b80e80:"Buffer not enabled, log not dumped","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::dumpLogBuffer"
60b8e18b:"ONNX CreateSession %s failed: %s (%d)","RootTools/roottools/inference/inference_engine/src/onnx_engine.cpp";"`anonymous-namespace\'::OnnxInferenceEngine::CreateSession"
60cc8862:"InternetConnectivityManager::start()","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::start"
611ca6af:"OS product type: %s\n","RootTools/roottools/spl/src/sysdeps/win/sysinfo_win.cpp";"spl::priv::sysInfoLogDetails"
6175503c:"dtlsCreate: failed to load certificate","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::dtlsCreate"
618e2389:"socketConnected: setsockopt: %s","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::socketConnected"
619d5952:"start","RootTools/roottools/net/src/sysdeps/berkeley/resolver_bsd.cpp";"`anonymous-namespace\'::AddressResolverOperation::start"
61d34292:"getPrimaryIfIdxv6: GetBestRoute2() returned error %u","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getPrimaryIfIdxv6"
61e32b2d:"Failed to create IOCP: %lu","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"rtnet::internal::createIOCP"
61e33f76:"%u cache entries preloaded","RootTools/roottools/net/src/dns_cache_v2.cpp";"`anonymous-namespace\'::DNSCache::LoadPersistent"
620180b5:"MutexDeadlockMonitor periodic check starting\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::MutexDeadlockMonitor::runCore"
623a40a3:"RequestWlanAccessSync: timeout %lld us","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::RequestWlanAccessSync"
623cd6ce:"RQ%u: Disable KeepAlive","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IntroduceSender"
6254928c:"netinfo: getCachedInterfaceList: total %d","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::getCachedInterfaceList"
6277ae63:"(%p) Connect %s -> %s:%d directly","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::startDirectConnection"
6296b7ea:"Creation of worker failed","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::Pinger"
62b4f7c4:"Received IPv4 mapped address for IPv6 query","RootTools/roottools/net/src/dns.cpp";"fillAddressList"
62d98073:"getPeerCert: no peer certificate present","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getPeerCert"
6303575f:"Could not open logmap file: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_write_file_cpp"
634b4a9f:"::PdhAddEnglishCounterW for CPU temperature failed, status %d","RootTools/roottools/spl/src/sysdeps/win/system_resources_pdh_win.cpp";"`anonymous-namespace\'::CpuStatsQueryState::init"
639fa5b6:"Receiving response failed at hop %d","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::processPing"
63bfeaf5:"Not a normal file for logging: %s","RootTools/roottools/auf/src/log2.cpp";"auf::FileLogAppender::open"
6400588a:"OS proxy query successful: no proxy set","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::getSystemProxyListForUrl"
64252831:"Read() not available for datafile with standalone catalog, use Deploy() instead","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Read::<lambda_...>::operator ()"
643cd5fb:"LockfreePacker: Unable to allocate memory.","RootTools/roottools/auf/src/auf.cpp";"auf::LockfreePacker::allocMem"
6445299f:"RQ%u: Restart on 100 Continue response","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::analyzeHeaders"
6486562e:"appCapabilityStatics->Create failed","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::getAppCapabilityStatusSync"
6490ac8e:"RQ%u: AsyncStream read: %s","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Fail"
64f80d1c:"S_CANCELLED: no error reported","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::onTerminalStateReached"
65142992:"generateCookieCallback: Session not found","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::generateCookieCallback"
651666d7:"Timeout waiting for objects! Stuff was leaked!","RootTools/roottools/spl/src/spl_common.cpp";"spl::stop"
65c3d5e6:"createConvertedString: out of memory.","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::createConvertedStringAndCapacity"
65e5ebfc:"Setup: bad X509Cert object layout. %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Setup"
6603210f:"Failed register handle: %lu","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"rtnet::internal::SingleThreadIOCP::subscribe"
660cf255:"Creating temporary binary file %s","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Deploy"
663ac779:"IntroduceReceiver: Server reset connection","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IntroduceReceiver"
665f1676:"Received %d bytes from %s:%u [%s]","RootTools/roottools/net/src/proxy_tcp_connect_v2.cpp";"`anonymous-namespace\'::HttpSerializerV2::IStreamSocketDelegate_firstAvailableBufferReceived"
66630d01:"MonitorOperation::strandSuspended","RootTools/roottools/auf/src/background.cpp";"auf::internal::MonitorOperation::strandSuspended"
668c95b4:"AllocFails = %u\n","RootTools/roottools/auf/src/transport/srmw.cpp";"auf::SRMWFifo::dump"
669bb35b:"spl::socketSend: send: %s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketSend"
66b52b80:"Neither fAutoDetect nor lpszAutoConfigUrl set, no auto proxy","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutomaticProxy"
66d91d2e:"logReadLogmapDir(%s)","RootTools/roottools/auf/src/logmap.cpp";"auf::logReadLogmapDir"
6711984c:"RQ%u: Shutdown fired","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Shutdown"
67299a9e:"RQ%u: Cannot open request: invalid URL \"%s\"","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Open"
674c0683:"RQ%u: Pass response header %s: %s","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::HTTPResponseReceived"
67731423:"getAllowPrivacySensitiveApis: no","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::ReadGeoPositionAsync"
677521ae:"GetAdaptersAddresses return value %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::deferredStart"
67ee2e04:"ProxyManagerV2 discovered %u proxies%s","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::discoverProxyInSeparateStrand"
67f8041a:"RQ%u: OnFailure callout: %s","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::CalloutOnFailure"
6807d67f:"Log file version is newer than known version for decoder. Decoding might fail.","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay"
6818835e:"pbkdf2Hmac: PKCS5_PBKDF2_HMAC failed","RootTools/roottools/spl/src/crypto_pbkdf2.cpp";"spl::pbkdf2Hmac"
6853d5cd:"unable to get model_name","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Check::<lambda_...>::operator ()"
68ad1a11:"GeoPosition result failed","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::GeoPositionAsyncOpHandler::Invoke"
68afd166:"Failed to parse log file","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay"
68b3675b:"RQ%u: Resolved opaque URI with non-matching scheme: %s","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Resolve"
68c102bb:"SCTP_CANT_STR_ASSOC sac_flags=%d sac_error=%d (state_=%d)","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::OnAssocChangeEvent"
69117e18:"verifyServerCert: Certificate checking disabled. Succeeding.","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::verifyServerCert"
6935beb5:"FinalizationTask::strandCreated","RootTools/roottools/auf/src/background.cpp";"auf::internal::FinalizationTask::strandCreated"
6987d23a:"exit ~CapabilityListener()","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::~CapabilityListener"
69daa2aa:"Time out is %.3fs","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::start"
69e211fa:"Bad log component id encountered","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replayOne2"
6a4281aa:"doLoad: failed to obtain file size %s: error %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::doLoad"
6a894f34:"Shutdown","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::Shutdown"
6ae4c22d:"accessRequestHandler init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WiFiAdapterImpl_RequestAccessAsync"
6af8d680:"Destroyed","RootTools/roottools/httpstack/src/sysdeps/rt/backend_impl.cpp";"http_stack::skypert::BackendImpl::~BackendImpl"
6afe7a85:"%s: MMCSS not available, thread running on standard scheduler @THREAD_PRIORITY_TIME_CRITICAL priority\n","RootTools/roottools/auxd/skype/src/win/skype_win.cpp";"auf::threadSchedHintApplyBeforeStart"
6b1e408e:"nameToX509Name: failed to convert name string: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::nameToX509Name"
6b4f646b:"WinKeyPairGenerationCngImpl::exportPublicKey: encode PUBLICKEYBLOB failed\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::exportPublicKey"
6bd8fe35:"Failed to get adapter by LUID, error = %x","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetAdapterFromLUID"
6c413b05:"Build config: %s/%s/%u","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
6c47c17e:"WinHttpGetProxyResult failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutoProxyEx"
6c48230a:"Log file updated, could not create file appender MaxSize=%I64u MaxRotations=%d Encryption=%d File=%s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogFile"
6c49db43:"Local port range %d-%d: error: %s","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::doConnectSingle"
6c4dc11d:"WSAStartup failed with error: %d. Networking is unavailable.\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::WSAInitHandle::WSAInitHandle"
6c55cc5a:"Windows %d.%d.%d %ls\n","RootTools/roottools/spl/src/sysdeps/win/sysinfo_win.cpp";"spl::priv::sysInfoLogDetails"
6cd91167:"(%p) Connect %s -> %s:%d directly","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::startDirectConnection"
6ced2a26:"Log trigger telemetry: log buffer L%u span %s to %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::logBufferVisitStats"
6d004661:"DNS result IPv6: %s","RootTools/roottools/net/src/sysdeps/berkeley/happy_eyeballs_bsd_v2.cpp";"`anonymous-namespace\'::HappyEyeballsV2::IDnsResolveDelegate_address"
6d00ff76:"Attempt to create Proxy Cache during shutdown, failing","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::getProxyCache"
6d042752:"Initial ::PdhCollectQueryData for CPU stats failed, status %d","RootTools/roottools/spl/src/sysdeps/win/system_resources_pdh_win.cpp";"`anonymous-namespace\'::CpuStatsQueryState::init"
6d0b7b15:"recv(%p, %I64u) -> RTNET_TLS_RETRY_READ","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::ITlsIO_read"
6d1b8c22:"start","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::TraceRouteOperation::start"
6d2fbc51:"RQ%u: Connecting TCP%s %s:%d","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::Connect"
6d409087:"send6: GetLastError: %d","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::send6"
6d7ac071:"Rotating log file: %s","RootTools/roottools/auf/src/log2.cpp";"auf::FileLogAppender::rotateLogFile"
6d86b91b:"RQ%u: Redirecting (%d) %d %s to %s","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::DetectRedirect"
6dcf5a13:"getDtlsSrtpParameters: handshake not completed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getDtlsSrtpParameters"
6e0b4185:"SslWrap(): socket has no peer address. Possibly already disconnected.","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::IOCPSslWrap"
6e8dd979:"auf::stopInternal() auf::init from %s still in initialization list, initialized %I64u times","RootTools/roottools/auf/src/auf.cpp";"AufInitializationRegistry::handleFinalAufStop"
6e93c25d:"Created","RootTools/roottools/httpstack/src/httpstack_impl.cpp";"http_stack::HttpStack::HttpStack"
6f66a330:"spl::isWritable(): DuplicateToken() failed, error code %lx","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::pathIsReadWritable"
6f6ad11b:"Hop %d status: Fail","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::processPing"
6f750e8e:"Terminal state: failed with error %d","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::Nat64PrefixDiscoveryOperation::onTerminalStateReached"
6f9110ad:"logReadLogmapDir: access denied to %s","RootTools/roottools/auf/src/logmap.cpp";"auf::logReadLogmapDir"
6fa8b051:"Failed to open log file %s : %s","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay2"
6fc677af:"GetNetworkId failed. hr %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getNetworkParams"
6fd99905:"%ls\t%016I64x","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::logStackBackTraceDbgHelpCore"
704fccf1:"Expected m_logConsoleOptions.get() != nullptr","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
7051e0ad:"AcquireCredentialsHandle failed: error=%x (username_present=%d, domain=%ls, method=%ls)","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::initIdentity"
708fc617:"WlanCapabilityChecker executed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WiFiAdapterImpl_RequestAccessAsync"
708fe9c1:"WlanRegisterNotification result: %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanEventMetricsOperation::onTerminalStateReached"
709bc69e:"entry blocking_call %p","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::`anonymous-namespace\'::ThreadExec::blocking_call"
70c98ab8:"generateResponse: no credentials","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::BasicAuth::generateResponse"
710e9c19:"spl::priv::setIgnoreUnreachable(): failed ioctlsocket(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::setIgnoreUnreachableICMP"
7179ac4f:"spl::pathCreateAndInit(): Can\'t create directory %s: %s","RootTools/roottools/spl/src/path_location.cpp";"spl::pathCreateAndInit"
718c1c9c:"Dispatcher %p: not empty at destruction time.","RootTools/roottools/auf/src/dispatcher.cpp";"auf::Dispatcher::~Dispatcher"
71a0a393:"DatagramSocketImpl::deferredPacketReceived: WSARecvFrom/WSARecvMsg gave us more bytes (%u) than we asked for (%u). BUG","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::DatagramSocketImpl::packetReceived"
71c822d5:"Log console updated, adding log console","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogConsole"
71d93125:"Using system proxy override: no proxy","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::getSystemProxyListForUrl"
7205ab4f:"ConnectionIsIdle %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::ConnectionIsIdle"
7240811e:"Log file dumped to %s (matched lines: %u, skipped lines: %u, other: %u)","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::mergeAndDumpLogBuffer"
724e24d0:"System FingerPrint: %s","RootTools/roottools/auf/src/fingerprint.cpp";"auf::internal::printFingerPrint"
726a626d:"WinSymmCryptoCngImpl::WinSymmCryptoCngImpl: failed to determine allowed tag lengths. Using default value 16","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::WinSymmCryptoCngImpl"
728e3540:"FreeLibrary returned error %d trying to free library %p","RootTools/roottools/spl/src/sysdeps/win/dynamic_library.cpp";"spl::FreeDynamicLibrary"
72beb024:"interface: %s (%I64x)","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INetworkInfoDelegate_interfaces"
72fcdfbb:"getCertHash: bad arguments","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getCertHash"
7357c360:"Endpoint %s:%d; %s","RootTools/roottools/net/src/sysdeps/berkeley/happy_eyeballs_bsd_v2.cpp";"`anonymous-namespace\'::HappyEyeballsV2::onTerminalStateReached"
73b702c2:"getPeerCert: encoding cert to DER failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getPeerCert"
73e480ea:"Unexpected message size=%u sid=%u ppid=%u messageType=%u dropped","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::DrawInputPipeline"
73e75ceb:"RQ%u: Callout: HTTP %u, %s","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::PooledRequest::Callout"
749a4690:"WinKeyPairGenerationCngImpl::exportPrivateKey unsupported format %u","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::exportPrivateKey"
74bff1e9:"FinalizationTask::onTerminalStateReached","RootTools/roottools/auf/src/background.cpp";"auf::internal::FinalizationTask::onTerminalStateReached"
750f3608:"WindowsReactorImpl::ctor","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::WindowsReactorImpl"
75129fdf:"Statics init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WiFiAdapterImpl_RequestAccessAsync"
751b4a19:"start","RootTools/roottools/net/src/sysdeps/berkeley/resolver_bsd.cpp";"`anonymous-namespace\'::NameResolverOperation::start"
7526d49a:"cannot start wlanMetricsOperation","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanEventMetricsOperation::start"
7542a9b0:"OS proxy %s filtered out, because of unsuitable schema (%d vs %d)","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::getSystemProxyListForUrl"
7579fee4:"fileOpen: adsName == NULL","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::WinSplFileImpl::fileOpen"
75955c0c:"[reg] rax    %016llx [reg] rbx    %016llx [reg] rcx    %016llx [reg] rdx    %016llx [reg] rsi    %016llx [reg] rdi    %016llx [reg] rbp    %016llx [reg] rsp    %016llx\n","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logRegistersFromContext"
75ae612f:"tlsDecrypt: Unspecified error: 0x%x","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsDecrypt"
75b87056:"RQ%u: Parse offset=%d size=%d","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::ResponseChunkReceived"
762d22fb:"getTlsBackend(): attempt to use TSL without backend implementation","RootTools/roottools/net/src/tls.cpp";"rtnet::internal::getTlsBackend"
762f6fb0:"spl::socketLocalAddress(): socket %I64u, getsockname(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketLocalAddress"
764c6031:"Not loading the configuration, persistent config not enabled","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::init"
764e4c35:"Decrypt: SSL_read: err %d, rc %d, %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Decrypt"
76c91bc7:"getSelfSignedCert: keyCertSync failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getSelfSignedCert"
76f1451f:"Getting INetworkCostManager failed with HRESULT=0x%08x, but this is expected on Win7","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
76fb0f04:"entry spl.CapabilityMonitor.atStop","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::instance::<lambda_...>::operator ()"
77550924:"Failed to create log buffer","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogBuffer"
775738b9:"WinHttpGetProxyResult failed with error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
7773df07:"(%p) Connect %s -> %s:%d via %s proxy at %s:%u","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::startProxyConnection"
77787a57:"SuspensionManager::registerTask","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::registerTask"
77bc1a17:"Address::port: unspecified address stored, returning port 0","RootTools/roottools/net/src/address.cpp";"rtnet::Address::port"
77bc969c:"HMACWinCngImpl::HMACWinCngImpl: cloning failed\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::HMACWinCngImpl::HMACWinCngImpl"
77f13da9:"exit ~CapabilityMonitor","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::~CapabilityMonitor"
7803f8e6:"start","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::UdpBindOperationWinsock::startWithAddress"
787ce070:"Deadlock monitor enabled: %s. Pending timeout %I64u us. Check period: %I64u us.","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
78822845:"GetLanIdentifiers denied access allowPrivate=%d","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::LLDPreader::LLDPreader"
7886768a:"Failed to get GPU adapter info, error = %x","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUEngines"
78c11ef7:"Bad file signature","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay1"
7911e8ef:"RegisterFromFile: registered model %s from path %s","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromFile"
794c1285:"spl::socketCreate(): failed socket(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketCreate"
7965d83f:"spl::pathCreateAndInit(): No r/w access to %s: %s","RootTools/roottools/spl/src/path_location.cpp";"spl::pathCreateAndInit"
79980870:"SplitIfNeededThenEnqueue: generated DTLS packet size %zu exceeds link MTU %zu","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::SplitIfNeededThenEnqueue"
7a07caf7:"Invalid thread handle for backtrace, probably because the thread has terminated (%lx)\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_context_native.cpp";"spl::priv::captureContextForThreadCoreBegin"
7a8d9497:"Obfuscation type not found in metadata","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::getObfuscation"
7a9b368b:"Failure binding socket for %s to port from range %d ... %d","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::doUdpBind"
7b0022dd:"generateResponse: credentials too long","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::BasicAuth::generateResponse"
7b134ca0:"exit ~GeoLocationCapabilityChecker","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocationCapabilityChecker::~GeoLocationCapabilityChecker"
7b7a8ba7:"=================================================================\n","RootTools/roottools/spl/src/file.cpp";"spl::priv::FileHandlesTracker::log"
7bb5bf95:"Cannot register for GUID_ACDC_POWER_SOURCE notifications, error code %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::start"
7bc64975:"spl::pathCreateAndInit(): Could not initialize outPath to %s","RootTools/roottools/spl/src/path_location.cpp";"spl::pathCreateAndInit"
7bce9881:"WinHashCngImpl::WinHashCngImpl: no hash provider\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinHashCngImpl::WinHashCngImpl"
7bd7d5dd:"tlsDecrypt: DecryptMessage abnormal behavior, plaintext is not in-place. Plaintext: %p (%lu bytes), ciphertext: %p (%zu bytes)","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsDecrypt"
7c1440fe:"OS proxy query failed: intermittent failure","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::queryProxySettings"
7c3c2633:"Timer cancelled","RootTools/roottools/auf/private/misc.hpp";"auf::Cache<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,bool,struct std::chrono::steady_clock>::timerStop"
7c5264ee:"anonymization disabled, log not dumped","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::dumpLogBuffer"
7c6feb29:"done. No objects were leaked","RootTools/roottools/spl/src/spl_common.cpp";"spl::stop"
7c8dab7b:"displayAccessHint: not supported: OS ver = %s build = %ld","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::DisplayAccessHint::<lambda_...>::operator ()"
7c96704b:"Start DTLS handshake %c...","RootTools/roottools/sctp/src/dtls_pipe.cpp";"`anonymous-namespace\'::DTLSPipe::HousekeepDtls"
7cc18a43:"Starting CPU time monitor with interval %u ms","RootTools/roottools/auf/src/cputime_monitor.cpp";"auf::internal::CpuTimeMonitor::updateInterval"
7cf945f3:"sendAsync to=%s from=%s v6=%d maxHops %d timeout %s tos=%d\n","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::sendAsync"
7d1d8a24:"WinSymmCryptoCngImpl::setKey: no IV (initialization vector) provided but one is needed; call setIV before calling setKey\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::setKey"
7d35aa31:"Cannot register for GUID_POWER_SAVING_STATUS notifications, error code %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::start"
7d3f3175:"MonitorOperation::initialize_v4only: failed because WindowsReactor is missing","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::initialize_v4only"
7d9d3d5c:"Stat age=%us size=%u; hits=%u misses=%u; os_queries=%u threads=%u queue=%u","RootTools/roottools/net/src/dns_cache_v2.cpp";"`anonymous-namespace\'::DNSCache::LogStatistics"
7daba262:"packetReceived: WSARecv status %u\n","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::packetReceived"
7dc6c92b:"generateResponse: auth failed, final state","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::BasicAuth::generateResponse"
7df07b0d:"Path::stringValue: Impossible to get converted path value.","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::Path::stringValue"
7e03ed5f:"Terminate thread %lu.\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::threadUnreliableJoin"
7e2bf715:"GetProcAddress returned error %d trying to load function \"%s\"","RootTools/roottools/spl/src/sysdeps/win/dynamic_library.cpp";"spl::GetFunctionAddress"
7e354f80:"MutexOrderer active\n","RootTools/roottools/auf/src/mutex_orderer_tree.cpp";"auf::internal::MutexOrdererTree::MutexOrdererTree"
7e4112ec:"%s","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::Get"
7f1be6d7:"spl::switchRealtimeStreamingMode(): failed to switch streaming mode %s\n","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::priv::switchRealtimeStreamingMode"
7f2d0b28:"   %s/%I64u","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INetworkInfoDelegate_interfaces"
7f57985e:"lldp.readChassisId: index=%d","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::LLDPreader::readChassisId"
7f6a7783:"TlsEndpoint::init: SSL_CTX_new() failed","RootTools/roottools/auf/src/tls_endpoint.cpp";"auf::`anonymous-namespace\'::TlsEndpoint::init"
7f90f986:"Log file dumped to %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::triggeredDefered"
7fb99e49:"RequestAccessAsync init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WiFiAdapterImpl_RequestAccessAsync"
7fccbbe0:"System %.4s battery is detected, but its capacity is reported in relative units only: %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
7fd16271:"Logging to asynchronous appenders blocked in synchronous mode, proceeding","RootTools/roottools/auf/src/log2.cpp";"auf::AsyncTraceThread::dispatchLoop"
8072b100:"dtlsCreate: SSL_CTX_new() failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::dtlsCreate"
8089e305:"entry GeoLocationCapabilityChecker","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocationCapabilityChecker::GeoLocationCapabilityChecker"
808a6ab6:"Unable to get signature data","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
80f1a574:"RegisterClass failed; error=%lu","RootTools/roottools/auf/apal/sysdeps/win/window_class.cpp";"apal::WindowClass::WindowClass"
81416f5c:"tlsRead: hard error, some data not decrypted and not returned to user","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsRead"
814e74aa:"Failed to open log file %s : %s","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::replayLogFile"
81c4f3d7:"Operation cancelled","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::do_cancel"
81fd6eed:"RQ%u: Reading response body","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::OnBackendResponse"
82018842:"RQ%u: Cannot send request: HTTPStack has already destroyed","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::DoSend"
8215e48a:"Fatal error: could not allocate timer object.\n","RootTools/roottools/auf/src/thread_pool_timer.cpp";"auf::Timer::operator new"
822018af:"Sender failed","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::SenderFailed"
82341df8:"doStore: Failed to write %s: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::doStore"
82373bbd:"WinKeyPairGenerationCngImpl::generateKeys: BCryptGenerateKeyPair failed with NTSTATUS 0x%x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::generateKeys"
82539b36:"threadSetMmPriority failed with error = %lx\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::win::threadSetMmPriority"
826b51fa:"Setup: force auf::%s = %s","RootTools/roottools/auf/src/setup.cpp";"importBoolConfig"
826cf1a3:"GeoCoordinates_Enabled: no","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::ReadGeoPositionAsync"
82738ef9:"WindowsReactorImpl::unregisterMessage","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::unregisterMessage"
8297561e:"OS proxy query successful: HTTP proxy detected, %s:%u, user=%s","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::queryProxySettings"
82b97ecd:"Failed to retry spl::ReadGeoPositionAsync","RootTools/roottools/net/src/geolocation.cpp";"rtnet::GeoPositionOperation::deferredCallback"
82d10438:"Not reading configuration, persistent config not enabled","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readFile"
82e8fe04:"spl::socketAccept(): failed accept(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketAccept"
82f7d6c7:"Pinger::cancelSync","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::cancelSync"
8304ad76:"Socket closed during handshake%s","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::IStreamSocketDelegate_closed"
832ca28b:"Failed to create LLDP reader","RootTools/roottools/net/src/lldp.cpp";"rtnet::ReadLLDPinfo"
8348b79e:"dtlsCreate: keyCertSync failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::dtlsCreate"
83502ff2:"Loaded Modules:","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logStackBackTraceManualCore"
835858a6:"Proxy auto-config URL %s","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
835931c8:"tlsConnect: doHandshakeStep failed","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsConnect"
835f79d3:"DnsResolve(%s) AF%d failed: %s (took %.3f s)","RootTools/roottools/net/src/dns_cache_v2.cpp";"`anonymous-namespace\'::DNSCache::SyncResolveWorker"
83792b5f:"Traceroute to %s","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::TraceRouteOperationNF::continueOp"
837dd2fe:"receiveBufferAsync(%I64u)","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::receiveBufferAsync"
83e40374:"Buffer updated, only changing component levels","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogBuffer"
83fd0ada:"Warning: MMCSS-enabled scheduling hint could not be applied. Using regular priority.","RootTools/roottools/auxd/skype/src/win/skype_win.cpp";"auf::threadSchedHintApply"
8414eef2:"Log levels updated, setting %s->%s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogLevels"
8420e817:"Init","RootTools/roottools/httpstack/src/stack_init.cpp";"http_stack::init"
842cb927:"recv(%p, %I64u) -> %d","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::ITlsIO_read"
8434e701:"Failed to join WindowsReactorImpl thread!","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::stop"
843c69da:"FinalizationTask::start","RootTools/roottools/auf/src/background.cpp";"auf::internal::FinalizationTask::start"
844009c7:"Log file marked as processed: %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::markLogFileProcessed"
84508000:"Unknown obfuscation type in metadata : %s","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::getObfuscation"
8463ab2f:"cryptProtectDecrypt: ::CryptUnprotectMemory() failed. data was not decrypted %lu","RootTools/roottools/spl/src/sysdeps/win/cryptprotect_win.cpp";"spl::decryptWithTempKey"
84737a31:"spl::priv::setWindowsQwaveQOSSocketProperty(): socket %I64u, QOSCreateHandle(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::setWindowsQwaveQOSSocketProperty"
847871a5:"PII unsafe logs included in log buffer, log not dumped","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::mergeAndDumpLogBuffer"
848f71f2:"doLoad: Failed to open file %s: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::doLoad"
84eee914:"NLM doesn\'t have IConnectionPointContainer interface, HRESULT=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
84f17c8e:"DnsReverseResolve GetNameInfo error: %d","RootTools/roottools/net/src/dns.cpp";"callGetNameInfo"
84f49cd1:"RQ%u: Connected %s -> proxy %s, auth %u","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IStreamSocketDelegate_connected"
8515bf71:"rtnet::internal::win::InterfaceImpl: CLSIDFromString() call failed, error %lx","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::InterfaceImpl"
853c5f28:"ONNX info: %s (%s)","RootTools/roottools/inference/inference_engine/src/onnx_engine.cpp";"`anonymous-namespace\'::OnnxLog"
858b651d:"auf::stop() from %s g_aufUp=%d","RootTools/roottools/auf/src/auf.cpp";"auf::stop"
8608f576:"BCryptExportKey (get blob) failed with NTSTATUS 0x%x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::BCryptExportKeyWrap"
863588f1:"Failed to get gpu utilization, unexpected engine index = %d","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUUtilization"
8679f8a4:"stopProcessing completed, took %lld ms","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::WorkStable::stopProcessing"
867d0c18:"getPeerCert: invalid argument","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getPeerCert"
8694735e:"Ignoring callback, network status is the same: %s","RootTools/roottools/net/src/netmon.cpp";"rtnet::priv::NetworkMonitor::INetworkInfoDelegate_networkChange"
869fecaf:"(%p) Connect %s -> %s:%d directly","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::startDirectOp"
86ac23ad:"Unlimited retries with %u_ms delay, 1_s delay will be used instead","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::RequestPool"
86c4d880:"ECS settings are ignored as dangerous: TCP_N_MaxAttempts=%u TCP_N_RetryDelay_Ms=%u","RootTools/roottools/net/src/connect_tcp_n.cpp";"rtnet::connectTCP_N_Async"
86c955f9:"RQ%u: Will send without body","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Launch"
86c99a54:"%s","RootTools/roottools/inference/inference_engine/src/onnx_dll_helper.cpp";"inference::onnx::DllHelper::get"
86dad60d:"LLDP info read complete. returning %zu of %u items of data","RootTools/roottools/net/src/lldp.cpp";"rtnet::ReadLLDPinfo"
86eb8c43:"%s: thread running on MMCSS %s scheduler\n","RootTools/roottools/auxd/skype/src/win/skype_win.cpp";"auf::threadSchedHintApplyBeforeStart"
86f9dec2:"onTerminalStateReached","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::onTerminalStateReached"
874d5823:"WlanConnectionQuality not supported: %ld.%ld.%ld","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::wlanConnectionQuality"
87e67ba3:"Cannot register reactor window class","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::eventLoop"
87ef7c46:"%s","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::dumpCalls"
87f838a7:"LogTrigger %s: resetCondition met","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogTrigger::ILogAppender_log"
87f8d9da:"Resolver operation returned no valid results","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::ResolverOperation::IDnsResolveDelegate_address"
8855fa20:"directoryFlush: failed to open \'%s\', error: %lx","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"WinSplDirImpl::flush"
8879a00d:"Failed to parse logmap json file: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_read_file"
888b271c:"Avoiding direct path, because of ProxyPolicy::ProxyOnly","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::deferredStart"
88cdd3d4:"send6: Icmp6CreateFile: %d","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::send6"
88f0dd50:"usrsctp_setsockopt SCTP_EVENT: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
89005b89:"spl::switchRealtimeStreamingMode(): streaming mode successfully switched %s\n","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::priv::switchRealtimeStreamingMode"
8906fc6a:"getSelfSignedCert: OPENSSL_malloc failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getSelfSignedCert"
890c727b:"WinAsymmCryptoCngImpl::setPublicKey: BCryptImportKeyPair failed with NTSTATUS %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::setPublicKey"
892a3270:"Abort all requests","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::Abort"
893c5b35:"TarFile::GetFile(model.dat) failed with %d(%s)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
89718566:"StreamSocket: %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IStreamSocketDelegate_error"
897ec029:"lldp.readChassisId: success. type=%u, length=%zu, value=%s","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::LLDPreader::readChassisId"
89e832fa:"AsyncOperation::start: illegal state %u","RootTools/roottools/auf/src/async_op.cpp";"auf::AsyncOperation::startOperationCore"
89ec6869:"SplOpaqueUpperLayerThread::setSchedHint() invoked on a thread that is started, with hint %p\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::setSchedHint"
8a3981d0:"SuspensionManager::strandSuspendable","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandSuspendable"
8a4e5d54:"NAT64 IPv6 address: %s","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::splitUpAddresses"
8a6925d6:"getWlanInfos: numberOfItems: %d, privacySensitiveAPI scope: %u","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getWlanInfos"
8a74abd2:"(%p) Start Pseudo-TLS","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::IStreamSocketDelegate_connected"
8a75de61:"Attempt to create rtnet::Factory during shutdown, failing","RootTools/roottools/net/src/common.cpp";"rtnet::getFactory"
8a97de91:"Handshake failed because server sent more than a ServerHello: early start? This is not supported.","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::IStreamSocketDelegate_firstAvailableBufferReceived"
8aa87c9c:"Thread %u could not be joined within the given time of %lld us\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::joinCore"
8b275423:"createPool: Too large pool requested, %u bytes\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::ILockfreeStackPool::make"
8b472233:"WinThread::WinThread: eventCreate() failed. Terminating.\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::WinThread::WinThread"
8b56a8d8:"Opening log file %s","RootTools/roottools/auf/src/log2.cpp";"auf::FileLogAppender::open"
8ba0f964:"TimerImpStateMachine::endDispatch: Illegal signal in CANCELED state","RootTools/roottools/auf/src/thread_pool_timer_sm.cpp";"auf::TimerImpStateMachine::endDispatch"
8ba8a9fd:"getCertHash: bad algorithm selected","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getCertHash"
8bf387b4:"Pinger::cancelSync took %s","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::cancelSync"
8c0cfc0a:"LoadLibrary returned %p for library \"%s\"","RootTools/roottools/spl/src/sysdeps/win/dynamic_library.cpp";"spl::LoadDynamicLibrary"
8c37bddc:"Sending request failed at hop %d","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::processPing"
8c570fb3:"splitRecordsToMTUSize:  cannot split DTLS packet to fit into MTU 2","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::splitRecordsToMTUSize"
8c84def2:"doLoad: Success to read file %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::doLoad"
8c86819e:"%016I64x:%s\n","RootTools/roottools/spl/src/debug.cpp";"spl::logStackStartingFrom"
8c975d27:"RQ%u: Redirect %d %s, switching to GET","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::DetectRedirect"
8cb223e6:"Format strings are different for same key: %u. \'%s\' vs \'%s\'\n","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_check_add"
8cbfd259:"AppCapabilityMonitor setCachedResult: %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::CapabilityCheckBase::setCachedResult"
8d07992a:"Invalid priority","RootTools/roottools/auf/src/thread_pool.cpp";"auf::globalThreadPoolExecutorCore"
8d186e84:"%s ID%u: %p  (Sym: %s!%s %p) (<base>+%p)","RootTools/roottools/spl/include/rt/rt_object_registry.hpp";"rt::ObjectRegistry::stop"
8d643669:"CreateWindowEx failed; error=%lu","RootTools/roottools/auf/apal/sysdeps/win/message_window.cpp";"apal::MessageWindow::MessageWindow"
8e3a6139:"Stack trace triggered on log line %X: \'%s\' with args %s","RootTools/roottools/auf/src/log2.cpp";"auf::StackTracingLogAppender::ILogAppender_log"
8e4c650b:"Getting coordinates failed","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::GeoPositionAsyncOpHandler::Invoke"
8ee598e9:"Terminal state: completed and discovered %I64u prefixes","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::Nat64PrefixDiscoveryOperation::onTerminalStateReached"
8f04b6ef:"proxyCallbackFunc: reported success","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::proxyCallbackFunc"
8f099449:"WinAsymmCryptoCngImpl::verifySignature: BCryptVerifySignature failed with NTSTATUS %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::verifySignature"
8f0a3522:"getNetwork: CachedNetworkListOperation failed: op=%d, good=%d","RootTools/roottools/net/src/netmon.cpp";"rtnet::getNetworks"
8f2d66ef:"RQ%u: Cannot parse HTTP","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::ResponseChunkReceived"
8f3a5c40:"SCTP_ASSOC_CHANGE sac_flags=%d sac_state=%d sac_error=%d (state_=%d)","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::OnAssocChangeEvent"
8f6d336c:"LFSP dump path not set!","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::dumpCalls"
8f8ad715:"Handshake failed because of unexpected message type: 0x%hhx","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::IStreamSocketDelegate_firstAvailableBufferReceived"
8f9fb44d:"WlanRssiListener: registerDelegate: %p GUID: %s","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::registerDelegate"
8fcc5956:"Creation of worker failed","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::PingerNF"
8fee5dd4:"Bind socket for %s to port from range %d ... %d: %s","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::startWithAddressDeferred"
9007d2d4:"spl::socketSendTo: sendto: %s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketSendTo"
90127bbf:"doBind","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::doUdpBind"
906bfb5d:"SuspensionManager::strandRegisterMonitor","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandRegisterMonitor"
908e5ba7:"Windows ProductId: %ls","RootTools/roottools/spl/src/sysdeps/win/fingerprint_win.cpp";"spl::internal::appendProductId"
909fedcd:"QuerySecurityPackageInfo for %s failed: err=%x","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::authenticationMethodSupported"
90aefcb2:"Start %s:%d","RootTools/roottools/net/src/sysdeps/berkeley/happy_eyeballs_bsd_v2.cpp";"`anonymous-namespace\'::HappyEyeballsV2::start"
90c01878:"Header name check failed for %s","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::SetHeader"
90cde214:"WinKeyPairGenerationCngImpl::exportPrivateKey: encode PKCS_RSA_PRIVATE_KEY failed %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::exportPrivateKey"
90e79558:"TraceRouteOperation::~dtor","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::TraceRouteOperationNF::~TraceRouteOperationNF"
91207328:"Cookie value was replaced","RootTools/roottools/httpstack/src/sysdeps/rt/cookie_store.cpp";"http_stack::skypert::CookieStore::EmplaceCookie"
9163de94:"RQ%u: Ask for read %d bytes","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::Read"
91772978:"spl::switchRealtimeStreamingMode(): failed WlanEnumInterfaces(): %u (%s)\n","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::priv::switchRealtimeStreamingMode"
91be3d61:"pickInterface() type %d","RootTools/roottools/net/src/net_wakeup.cpp";"rtnet::NetworkToken::wakeupAsync::<lambda_...>::operator ()"
91c865c8:"RequestWlanAccessSync: %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::RequestWlanAccessSync"
92065633:"doStore: Failed to open %s: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::doStore"
92672895:"%sMark %#3x ContentPtr %p\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackBin::dumpCb"
927a5023:"CAT file is not signed","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
92820dd4:"RootTools is now shutting down logging and tracing. Goodbye, and have a nice day.","RootTools/roottools/auf/src/auf.cpp";"auf::stop"
92beecd2:"allocateCore: large LF allocation ~%d in bin %d!","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::allocateCore"
92fb656a:"Failed to get DxgiAdapter description, result = %x","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUAdapters"
931f4e6f:"NetworkMonitorOperation::handleChangeDeferred()","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::handleChangeDeferred"
936878b9:"Failed to get adapter by LUID, fnOpenAdapterFromLuid_ is unavailable","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetAdapterFromLUID"
93ce6b4d:"Failed to get gpu utilization [adapter = %d], D3DKMTQueryStatistics is not loaded","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUUtilization"
941d6c9a:"LockfreeStackPool (%p) AsyncCalls dump (%d):","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::dumpCalls"
941e2869:"LoadLibrary returned error %d trying to load library \"%s\"","RootTools/roottools/spl/src/sysdeps/win/dynamic_library.cpp";"spl::LoadDynamicLibrary"
94349a3f:"[time elapsed: %.3fms] Datagram socket binding to address %s:%d","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::doUdpBind"
944eba7e:"GetCategory failed. hr %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getNetworkParams"
948a01a1:"WinHttpGetProxyForUrl: error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
94a5b6b3:"SCTP: %s","RootTools/roottools/sctp/src/sctp_stack.cpp";"Printf"
94a70e0a:"DTLS handshake complete %c, DataMTU=%zu","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Handshake"
94ae5c5f:"RQ%u: Fresh connection %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::GetPromise"
9507ea70:"deferredConnectAsync: spl::socketConnect: %d(%s)","RootTools/roottools/net/src/sysdeps/berkeley/datagram_socket.cpp";"rtnet::internal::DatagramSocketImpl::deferredConnectAsync"
9532f6e0:"TarFile::GetFile(model.cat) failed with %d(%s)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
9582fd15:"getDtlsSrtpParameters: SRTP profile not found","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getDtlsSrtpParameters"
95b8db06:"LogTrigger %s: resetting conditions","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogTrigger::ILogAppender_log"
95c43086:"(%p) Start Pseudo-TLS","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::IStreamSocketDelegate_connected"
95c9ebfe:"MonitorOperation::start","RootTools/roottools/auf/src/background.cpp";"auf::internal::MonitorOperation::start"
960584a4:"appCapabilityStatics->Create failed\n","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::registerListener"
9608117b:"proxyCallbackFunc: unexpected async operation %p failed with error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::proxyCallbackFunc"
9623b1db:"createSingleThreadExecutor(): couldn\'t allocate memory for the thread object","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::createSingleThreadExecutor"
9624b9a3:"dtlsConnectAccept error %d %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::doDtlsConnectAccept"
96290c34:"Access is allowed to GetLanIdentifiers (wiFiControl)","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::requestAccessAndShowHint"
963d5bf4:"getResult6(): Icmp6ParseReplies failed: %u","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::getResult6"
963f65c8:"DTLS handshake completed %c","RootTools/roottools/sctp/src/dtls_pipe.cpp";"`anonymous-namespace\'::DTLSPipe::HousekeepDtls"
96513964:"tlsConnect: write error %s(%d)","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsConnect"
9680e78b:"RequestAccessAsync set completion failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WiFiAdapterImpl_RequestAccessAsync"
96981869:"exit GeoLocationCapabilityChecker","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocationCapabilityChecker::GeoLocationCapabilityChecker"
96c00316:"%s","RootTools/roottools/auf/src/async_op.cpp";"auf::AsyncOperation::endProgress"
9705d74e:"generateEcdsaKey: EC curve support not implemented","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::generateEcdsaKey"
976039d3:"Failed to get adapter by LUID, invalid adapter handle.","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetAdapterFromLUID"
9784384f:"threadWinEntry: could not set priority to %d\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::threadWinEntry"
97c8fec1:"Handshake failed because of unexpected server ServerHelloDone: 0x%hhx 0x%hhx 0x%hhx 0x%hhx","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::IStreamSocketDelegate_firstAvailableBufferReceived"
97d53cf4:"logReadLogmap()","RootTools/roottools/auf/src/logmap.cpp";"auf::logReadLogmap"
9825d5b6:"reporting error [%d:%s] all failures: [%s]","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::onTerminalStateReached"
9844dbaa:"Thread pool manager: couldn\'t allocate memory for the thread object","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::ThreadPoolManager::ThreadPoolManager"
984a9e87:"AsyncOperation::complete: Invalid to attempt endProgress() in status %u","RootTools/roottools/auf/src/async_op.cpp";"auf::AsyncOperation::endProgress"
98c2fe0b:"async send(%u)","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::sendNextPacket"
98d0066c:"proxyCallbackFunc: reported success","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::proxyCallbackFunc"
98e11dbf:"GeoLocationCapabilityChecker (value=%d) cached","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocationCapabilityChecker::RequestAccessAsync"
9905bb0f:"Bad thread pool id encountered","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replayOne2"
9955a6ae:"generateResponse: no proxy host name given","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::generateResponse"
995a41c2:"exit ~WlanCapabilityChecker","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanCapabilityChecker::~WlanCapabilityChecker"
997ea40d:"Loading of %s failed during reading, %s (%d)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::loadArchiveFile"
99800df3:"SRMWFifo@%p:\n","RootTools/roottools/auf/src/transport/srmw.cpp";"auf::SRMWFifo::dump"
998a4439:"FinalizationTask::dispatchExpired","RootTools/roottools/auf/src/background.cpp";"auf::internal::FinalizationTask::dispatchExpired"
999bb9cb:"makeAuthProvider: created for method: %s","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::createTopMethod"
99c56bfe:"Triggers updated, removing trigger %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogTriggers"
9a423561:"exit join()","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::`anonymous-namespace\'::ThreadExec::join"
9a926fc2:"%ls\t%016I64x","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logStackBackTraceManualCore"
9b120922:"packetReceived(%u, %u)","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::packetReceived"
9b9a4402:"CPU%u: { %u MHz, NUMA node: %u, type: %s}","RootTools/roottools/spl/src/spl_common.cpp";"spl::sysInfoLogDetails"
9b9f8a12:"getSelfSignedEcdsaCert: generating certificate/key failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getSelfSignedEcdsaCert"
9ba1740d:"NativeThreadPoolExecutor: safe allocation failed\n","RootTools/roottools/auf/src/thread_pool_native.cpp";"auf::NativeThreadPoolExecutor::allocMsgMem"
9bf1cd1a:"(%p) Connected %s, local %s","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::IStreamSocketDelegate_connected"
9c02a01c:"socketBindPortRange %d-%d: error: %s","RootTools/roottools/spl/src/bindportrange.cpp";"spl::socketBindPortRange"
9c241166:"=================================================================\n","RootTools/roottools/spl/src/file.cpp";"spl::priv::FileHandlesTracker::insert"
9cdc1c16:"AppCapabilityMonitor<>::Invoke called","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::Invoke"
9cea8d16:"Unexpected DATA_CHANNEL_ACK @%d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OnDataChannelAck"
9cf26d7e:"ONNX warning: %s (%s)","RootTools/roottools/inference/inference_engine/src/onnx_engine.cpp";"`anonymous-namespace\'::OnnxLog"
9cff06c6:"LogmapFilter loaded %I64u format strings","RootTools/roottools/auf/src/logmap.cpp";"auf::LogmapFilter::LogmapFilter"
9d1c44c3:"GeoPosition callback invoked with status %d","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::GeoPositionAsyncOpHandler::Invoke"
9d44438f:"onTerminalStateReached","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::UdpBindOperationWinsock::onTerminalStateReached"
9d4e2ecf:"Certificate signer verification failed","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
9da51e30:"Using HTTP proxy override, %s:%u, user=%s","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::getSystemProxyList"
9dc47391:"spl::priv::setWindowsQwaveQOSSocketProperty(): socket %p failed, because qwave.dll loading failed","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::setWindowsQwaveQOSSocketProperty"
9e2dc0df:"auf::Mutex possible deadlock: Thread %u waited %I64u ms for mutex %s (%p), owned by thread %u count: %u\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::MutexDeadlockMonitor::deadLckPossibilityWarning"
9e33c753:"SuspensionManager::suspend","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::suspend"
9e3af451:"finish","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::Nat64PrefixDiscoveryOperation::finish"
9ebe1991:"[time elapsed: %.3fms] Datagram socket creation, family=%d","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::doUdpBind"
9ec7ded2:"CoCreateInstance of NLM failed with HRESULT=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
9ed43a78:"%s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::getWsaHandle"
9ed8c96e:"ResolverOperation::ctor","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::ResolverOperationNF::ResolverOperationNF"
9edcccd4:"OUT_RESET_RECEIVED %s@%d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OnStreamResetNotification"
9eee7781:"WindowsReactorImpl::handleRegisterHandle","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::handleRegisterHandle"
9ef926ba:"STATE: S_SUSPENDING","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandSuspend"
9ef9477b:"S.%I64u Strand retiring","RootTools/roottools/auf/src/thread_pool_strand.cpp";"auf::StrandExecutorImp::~StrandExecutorImp"
9f3c9772:"%016I64x: | %s| %s\n","RootTools/roottools/spl/src/debug.cpp";"spl::internal::dumpMemory"
9f8ad697:"RQ%u: Connected %s -> %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IStreamSocketDelegate_connected"
9f9ceaef:"WinKeyPairGenerationCngImpl::WinKeyPairGenerationCngImpl: no crypto algorithm provider\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::WinKeyPairGenerationCngImpl"
9fb5f7ef:"(%p) Connect %s -> %s:%d via %s proxy at %s:%u","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::startProxyConnection"
9fd360f8:"proxyCallbackFunc: unexpected async operation %p failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::proxyCallbackFunc"
a0436d7a:"auf::ThreadRef backed by compatibility thread pool (P.%s)","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
a051eeae:"generateResponse: re-entry with context","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::generateResponse"
a056f313:"AcquireCredentialsHandle success! %x","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::initIdentity"
a1284983:"Failed to create IOCP thread","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"rtnet::internal::SingleThreadIOCP::SingleThreadIOCP"
a12a89b4:"Log levels updated, no change","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::updateLogLevelConfig"
a174438f:"pathInitFromLocation: SHGetFolderPath failed, no PL_APP_DATA_DIR","RootTools/roottools/spl/src/sysdeps/win/path_location_win.cpp";"spl::pathInitFromLocation"
a1b5c3e1:"Timed out","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::timeOut"
a1ca604a:"getResult: Wait timeout","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::getResult"
a1fa27fa:"\n","RootTools/roottools/spl/src/file.cpp";"spl::priv::FileHandlesTracker::log"
a2168357:"packetSent(%u, %u)","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::onPacketSent"
a224ee24:"receiveFirstAvailableBufferAsync(%I64u)","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::receiveFirstAvailableBufferAsync"
a2a56fa1:"RegisterFromBuffer: registered model %s from buffer sized %u","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromBuffer"
a2ae90e0:"dispatchPrivacyChange(%d) skipped (%d)","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::dispatchPrivacyChange"
a2de98df:"allocateCore: suspicious amount of 1kB+ allocations: %d","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::allocateCore"
a2e01b9a:"tlsDecrypt: Impossible branch. Buf types: %ul %ul %ul %ul","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsDecrypt"
a2e7e80f:"initCngSymmCryptoProvider: BCryptSetProperty BCRYPT_CHAINING_MODE failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::CngCryptData::initCngSymmCryptoProvider"
a2efde8f:"FindConnectionPoint of INetworkCostManagerEvents failed with HRESULT=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
a332e0da:"MonitorOperation::initialize_v6","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::initialize_v6"
a3563026:"Unable to unregister ETW trace handle. Error: %u","RootTools/roottools/auf/src/log2_wpp_appender.cpp";"auf::EtlLogAppender::~EtlLogAppender"
a35aa922:"exit removeDelegate","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::removeDelegate"
a38317db:"dtor","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::~IOCPSslWrap"
a39fecdb:"SslWrap::ITlsIO_write: Socket reset","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::ITlsIO_write"
a3bd4ecc:"RQ%u: No network %d","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::TerminateAt_OnFailure"
a3e6e6f6:"spl::fileOpen() ID [%u]\n","RootTools/roottools/spl/src/file.cpp";"spl::priv::FileHandlesTracker::insert"
a400e32f:"GetThreadContext() failed (%lx)\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_context_native.cpp";"spl::priv::captureContextForThreadCoreBegin"
a454fdbb:"Shutdown","RootTools/roottools/httpstack/src/httpstack_impl.cpp";"http_stack::HttpStack::Shutdown"
a468a89d:"spl::priv::captureBackTraceInfoCore(): StackWalk64() error\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::_captureStackBackTraceDbgHelpCore"
a46b0d8d:"generateResponse: no authentication methods remaining","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
a492b125:"Tearing down RootTools in stopCore()\n","RootTools/roottools/auf/src/auf.cpp";"auf::stop"
a4ddcf6a:"Successfully created ONNX inference session %s","RootTools/roottools/inference/inference_engine/src/onnx_engine.cpp";"`anonymous-namespace\'::OnnxInferenceEngine::CreateSession"
a510883a:"spl::priv::setWindowsQwaveQOSSocketProperty(): socket %I64u, QOSAddSocketToFlow(): %lx (%s)\n","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::priv::setWindowsQwaveQOSSocketProperty"
a5536ad6:"Cannot parse line %s\n","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_read_file"
a569a01e:"DROP SCTP PACKET for network, SCTP type %02x, packet len %I64u","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssocProxy::OnPacketFromSctpToNetwork"
a58aee6a:"spl::switchRealtimeStreamingMode(): failed to create string from GUID\n","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::priv::switchRealtimeStreamingMode"
a5bd8a9e:"Count increased to %u, because object ID%u (%p, type=%d) was created from:","RootTools/roottools/spl/include/rt/rt_object_registry.hpp";"rt::ObjectRegistry::add"
a5d96980:"NAT64 prefix discovered: %s/%I64u","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INat64PrefixResolveDelegate_prefix"
a63f2028:"After queue processing ready:%d active:%d delayed:%d","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::Strand_ProcessQueues"
a666a0b6:"%s","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::ResponseChunkReceived"
a66e9270:"Handshake failed because of unexpected server reply: 0x%hhx 0x%hhx 0x%hhx 0x%hx","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::IStreamSocketDelegate_firstAvailableBufferReceived"
a6a27a2d:"RQ%u: Body encoding %s","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::analyzeHeaders"
a7469bfb:"WinHttpOpen: error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
a75fecb8:"ul.conf too big, ignoring","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readFile"
a7739c83:"listenAppCapabilityAsync: !monitor","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::listenAppCapabilityAsync"
a777e622:"tlsCreate: AcquireCredentialsHandle failed, status %u","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsCreate"
a78f928e:"MonitorOperation::relisten_v4","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::relisten_v4"
a7bd58c0:"(While checking LockfreeStackPool 0x%I64x which extends to 0x%I64x; bin storage 1st chunk: %I64u, current marker chunk: %I64u, end chunk: %I64u)\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::checkConsistency"
a7d8fe8e:"generateResponse: no common method found!","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
a806a115:"Exiting due to deadlock in thread %u\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::ThreadDumper::dumpThreadsIfCollected"
a8228aeb:"::PdhOpenQueryW failed, status %d","RootTools/roottools/spl/src/sysdeps/win/system_resources_pdh_win.cpp";"`anonymous-namespace\'::CpuStatsQueryState::init"
a83426e9:"RQ%u: Cannot create timer","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Launch"
a84e910b:"RQ%u: Abort called","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Abort"
a84f65d6:"MonitorOperation::ctor","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::MonitorOperation"
a883a586:"AsyncTraceThread::AsyncTraceThread: could unfortunately not create thread sys\n","RootTools/roottools/auf/src/log2.cpp";"auf::AsyncTraceThread::AsyncTraceThread"
a8cd9f14:"LockfreeStackPoolImp: setDebugOptions=%x","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::setDebugOptions"
a9281417:"MFCreateAsyncResult() failed, err = %x\n","RootTools/roottools/spl/src/sysdeps/win/thread_pool_win.cpp";"spl::priv::NativeThreadPoolManager::dispatch"
a93226fa:"WinSymmCryptoCngImpl::doOp: no IV (initialization vector) provided but one is needed; call setIV before calling setKey\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::doOp"
a96f7002:"Bad HRESULT %lx (from expression %s at %s:%d)\n","RootTools/roottools/spl/private/spl_string.hpp";"spl::loggedBadHRESULT"
a9846318:"Could not write to ul.conf","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::writeFile"
a987e91e:"storeKey: Key serialization failed","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::storeKey"
a99ce3de:"Mutex %s/%p was deallocated while being held","RootTools/roottools/auf/src/mutex_orderer_tree.cpp";"auf::internal::MutexOrdererTree::deleteNode"
a9b62015:"exit CapabilityMonitor","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::CapabilityMonitor"
a9c97ee8:"%sNULL\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackBin::dumpCb"
a9cac10b:"WinKeyPairGenerationCngImpl::exportPublicKey: BCryptExportKey failed","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::exportPublicKey"
a9cb152e:"RQ%u: OnBackendResponse %s","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::OnBackendResponse"
a9d79153:"HAMCWinCngImpl::finalize: BCryptFinishHash failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::HMACWinCngImpl::finalize"
a9ef8a05:"GeoPosition operation timeout","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::GeoPositionAsyncOpHandler::Invoke"
aa1fa00a:"RQ%u: Cannot create HTTPResponse: %s","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::HTTPRequestSent"
aa5afb2b:"Resolver operation returned no valid results","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::ResolverOperationNF::IDnsResolveDelegate_address"
aa6c277a:"Connection Quality: Phy Type = %u, Link Quality = %u, RxRate = %u, TxRate = %u, Is MLO Connection = %u, Num Links = %u","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::LogConnectionQuality"
ab00a240:"ConnectionDied %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::ConnectionDied"
ab06fa40:"DestroyWindow failed; error=%lu","RootTools/roottools/auf/apal/sysdeps/win/message_window.cpp";"apal::MessageWindow::~MessageWindow"
ab1db82a:"TraceRouteOperation::ctor","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::TraceRouteOperation::TraceRouteOperation"
ab72709b:"tlsConnect: server disconnected","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsConnect"
abb3a946:"Starting reactor","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::start"
abb8709c:"%s\n","RootTools/roottools/spl/src/spl_common.cpp";"spl::sysInfoLogDetails"
abdc405f:"%s in a LockfreeStackPool, see log messages above in this thread\'s context!\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::checkConsistency"
ac275145:"INetworkInfoDelegate_interfaces","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INetworkInfoDelegate_interfaces"
ac2fd955:"model.json not found","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::getMetadata"
ac32c356:"MonitorOperation::IWindowsReactorDelegate_handle","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::IWindowsReactorDelegate_handle"
ac355710:"RegisterFromBuffer: cannot register model %s, empty or no buffer provided","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromBuffer"
ac36d6e5:"Warning: can\'t create TCP socket for loading extension functions","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"rtnet::internal::platformNetStart"
ac4386bf:"bssInfo cache expired","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::wlanBssInfo"
ac7af15a:"start","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::ResolverOperationNF::start"
ac99912b:"dtlsFindSplitLen: Not a valid record header type: %u","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::dtlsFindSplitLen"
acbeb165:"spl::socketRecv: recv: %s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketRecv"
acfaf705:"SuspensionManager::strandSuspend","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandSuspend"
ad1635c8:"verifyServerCert: Error: certificate info not available","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::verifyServerCert"
ad5dad49:"WlanRssiListener::start(): not starting due to privacy flag","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::start"
ad6e46db:"LogMap filter search paths updated, no change","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::updateLogMapFilterConfig"
ad6e5847:"unable to get ClientMetdata","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Check::<lambda_...>::operator ()"
ad71b62f:"RQ%u: Reusable connection has expired","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::GetPromise"
ad889509:"RQ%u: Request timed out","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Timeout"
addd0bc3:"RoGate Init failed","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::getAppCapabilityStatusSync"
ae40f95f:"Unexpected error (%x): %s","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
ae5a5ec6:"auf::Mutex::tryLock(): Thread %u is trying to lock %s (%p), which is owned by thread %u, which is waiting for %s (%p), which is owned by thread %u\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::MutexDeadlockMonitor::deadLckDetectedWarning"
ae68fbdd:"(While checking space for bin %u, chunk size bytes %I64u)\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::checkConsistency"
ae84825f:"[time elapsed: %.3fms] Socket binding to port %d","RootTools/roottools/spl/src/bindportrange.cpp";"spl::socketBindPortRange"
ae883dcc:"Expected m_logBufferOptions.get() != nullptr","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
aef5474b:"Failed to register log file %s with Windows Error Reporting : %x","RootTools/roottools/auf/src/log2.cpp";"auf::FileLogAppender::open"
af04ff98:"RQ%u: Switch to fallback decompression scheme","RootTools/roottools/httpstack/src/sysdeps/rt/decompressor.cpp";"http_stack::skypert::Decompressor::Decompress"
af0564a2:"MonitorOperation::dispatchSuspended","RootTools/roottools/auf/src/background.cpp";"auf::internal::MonitorOperation::dispatchSuspended"
af0643aa:"tlsWriteAlert: sending Alert: %s","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsWriteAlert"
af13c76f:"(%p) Connect to %s:%d via %s proxy at %s:%u","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::startProxyConnectionsV1"
af4d0c25:"New worker is created","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::Worker::Worker"
af82d593:"Bad encryption header in binary log","RootTools/roottools/auf/src/log2.cpp";"auf::DecryptReaderImpl::DecryptReaderImpl"
afaa117e:"Awaiting all Objects to be removed... ","RootTools/roottools/spl/src/spl_common.cpp";"spl::stop"
afb937be:"Pinger::ctor","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::Pinger"
afc3dc9b:"RQ%u: Enqueued","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::EnqueueRequest"
afc5c002:"RQ%u: Set KeepAlive time=%u, interval=%u, probes=%u","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IntroduceSender"
affa1371:"dtlsCreate: failed creating ssl","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::dtlsCreate"
b00b1adc:"deleteFromStore: Failed to delete key: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::deleteFromStore"
b09d01a6:"Session %s (%p) created","RootTools/roottools/inference/inference_engine/src/onnx_session.cpp";"inference::onnx::OnnxInferenceSession::OnnxInferenceSession"
b0bfda0e:"LogTrigger not propagating the event due to disabled anonymization","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogTrigger::ILogAppender_log"
b0c0ef12:"RQ%u: Pipeline on connection %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::GetPromise"
b114ee61:"spl::socketConnect: connect(%d): %s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketConnect"
b11a19ad:"Buffer updated, removing existing buffer","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogBuffer"
b1327233:"Log levels:","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::dumpLogLevels"
b140c15e:"entry ~WlanCapabilityChecker","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanCapabilityChecker::~WlanCapabilityChecker"
b1817b6e:"tryToResolve","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::Nat64PrefixDiscoveryOperation::tryToResolve"
b1a3b9cb:"Backtrace:","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::logStackBackTraceDbgHelpCore"
b1c720c4:"DNS request succeded","RootTools/roottools/net/src/sysdeps/berkeley/resolver_bsd.cpp";"`anonymous-namespace\'::AddressResolverOperation::Completed"
b1cc8cd4:"DtlsKeyCertManager::threadEntry: cert almost expired","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::DtlsKeyCertManager::threadEntry"
b1ccf26c:"WinHttpGetProxyForUrl unicode conversion failed","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutoProxyLegacy"
b1d35827:"Stack dump:\n","RootTools/roottools/spl/src/debug.cpp";"spl::logStackStartingFrom"
b1dc6feb:"inplaceConvert: failed to convert: %lx.","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::inplaceConvert"
b21cd89e:"ConsumeReceivedFromPeer: received packet size %zu exceeds link MTU %zu","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::ConsumeReceivedFromPeer"
b24aa467:"entry unregisterListener","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::unregisterListener"
b2b904ef:"WlanRegisterNotification result: %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanEventMetricsOperation::start"
b2b97b71:"WlanAccessAsync callback invoked with status %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanAccessStatusAsyncOpHandler::Invoke"
b2c7386d:"Binaries not found in metadata","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::getBinaries"
b2dd3094:"Bad file signature","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay2"
b2ec14a2:"Getting Geolocation access response took %lld ms","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeolocationAccessAsyncOpHandler::Invoke"
b2f8e660:"atStop queue draining failed during spl::stop, this is fatal","RootTools/roottools/spl/src/spl_common.cpp";"spl::stop"
b310e8da:"MFThreadPool (ctor): MFLockSharedWorkQueue() failed (taskName = %ls, basePrio = %ld) err = %x\n","RootTools/roottools/spl/src/sysdeps/win/thread_pool_win.cpp";"`anonymous-namespace\'::MFThreadPool::MFThreadPool"
b3571418:"createTempFile(model.dat) failed with %d(%s)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
b359207a:"ONNX error: %s (%s)","RootTools/roottools/inference/inference_engine/src/onnx_engine.cpp";"`anonymous-namespace\'::OnnxLog"
b37a3f97:"Port range %d-%d is exhausted, fallback to ephemeral ports","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::doConnectSingle"
b3c157bd:"%s","RootTools/roottools/net/src/tls.cpp";"rtnet::internal::getTlsBackend"
b3e3288f:"End of AUF information","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
b3f6afe8:"RQ%u: Buffer sent","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IStreamSocketDelegate_bufferSent"
b45efa33:"Pinger::~dtor","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::~Pinger"
b47087e8:"exit run","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::`anonymous-namespace\'::ThreadExec::run"
b4933dc2:"CryptEncodeObjectEx (get blob size) failed with error 0x%lx\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt.cpp";"spl::internal::CryptEncodeObjectExWrap"
b4ae8436:"MFPutWorkItemEx2() failed, err = %x","RootTools/roottools/spl/src/sysdeps/win/thread_pool_win.cpp";"spl::priv::NativeThreadPoolManager::dispatch"
b4bbfae8:"RQ%u: Body compression %s","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::analyzeHeaders"
b4e6985c:"#%02u\t%016I64x\t%s+0x%x\n","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logStackBackTraceManualCore"
b5034b56:"doHashInit: BCryptCreateHash failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::doHashInit"
b503ff79:"Connected to %s:%u, local %s","RootTools/roottools/net/src/proxy_tcp_connect_v2.cpp";"`anonymous-namespace\'::HttpSerializerV2::IStreamSocketDelegate_connected"
b5174e65:"Missing handle wlanHnd=%p wlanDll=%p","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::getVPNName"
b51d352e:"Expected m_logFileOptions.get() != nullptr","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
b541be10:"MonitorOperation::relisten_v4: failed with err = %lx","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::relisten_v4"
b568898a:"Time out is %.3fs","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::deferredStart"
b5729fa4:"HMACWinCngImpl::HMACWinCngImpl: source hash is not good\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::HMACWinCngImpl::HMACWinCngImpl"
b57e0cdf:"auf::stopInternal() stop called with unknown consumerName %s","RootTools/roottools/auf/src/auf.cpp";"AufInitializationRegistry::addStop"
b5836529:"SuspensionManager::strandUpdateTaskTimer","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandUpdateTaskTimer"
b5850b43:"Getting model.dat from TarFile","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
b5a85eec:"generateResponse: CompleteAuthToken: err=%x","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::generateResponse"
b5cf9c9c:"Buffer updated, removing existing buffer appender","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogBuffer"
b5ea5ef8:"MonitorOperation::dispatchError","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::dispatchError"
b5f00ce4:"INetworkCostManager::GetCost failed with hr=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::getNetworkCost"
b5fc9683:"DnsResolve GetAddrInfoW(%s) AF%d SOCK%d FLAGS%d: error %d: %s","RootTools/roottools/net/src/dns.cpp";"callGetAddrInfo"
b6146c7e:"DtlsKeyCertManager::threadEntry: cert serialNumber == 0","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::DtlsKeyCertManager::threadEntry"
b68acc91:"Expected m_logFiles.back().config.get() != nullptr","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
b6a3bd2d:"conversionLength: failed to convert: %lx.","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::conversionLength"
b6c671ed:"Found %I64u valid manual proxies","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getHttpProxies"
b70b9918:"Could not open ul.conf","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::writeFile"
b71f9b80:"MMCSS thread characteristics requested but AvSetMmThreadCharacteristicsA failed unfortunately.\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::threadWinEntry"
b73fbcd4:"Dns reverse request for %s","RootTools/roottools/net/src/sysdeps/berkeley/resolver_bsd.cpp";"`anonymous-namespace\'::NameResolverOperation::deferredLookup"
b741b21b:"Lockfree space depletion in BufferQueue::enqueueDesc(). Terminating","RootTools/roottools/net/private/net/common.hpp";"rtnet::BufferQueue<struct rtnet::DatagramInputBuffer>::enqueueDesc"
b765dfb6:"RQ%u: Will send %d bytes of body stream","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Launch"
b778fb03:"unable to get TeamMetdata","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Check::<lambda_...>::operator ()"
b7806c34:"proxyCallbackFunc: unexpected status %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::proxyCallbackFunc"
b7aa9687:"%u log line messages couldn\'t be allocated recently\n","RootTools/roottools/auf/src/log2.cpp";"auf::AsyncTraceThread::processTrace1"
b816442b:"SSL_new: failed creating ssl","RootTools/roottools/auf/src/tls_endpoint.cpp";"auf::`anonymous-namespace\'::TlsEndpoint::init"
b83698a9:"SslWrap(): broken socket provided, sock=%p, opt=%p","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::IOCPSslWrap"
b84a10af:"Failed to read the logmap json file: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_read_file"
b87842b5:"INetwork::GetCategory failed with hr=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::constructNetwork"
b8a86781:"AppCapabilityMonitor getCachedResult: %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::CapabilityCheckBase::getCachedResult"
b8adc082:"purgeExpired()","RootTools/roottools/auf/private/misc.hpp";"auf::Cache<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,bool,struct std::chrono::steady_clock>::purgeExpired"
b8bfbbfb:"Bad log component id encountered","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay1"
b90f1f64:"No threads alive in the thread pool","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::ThreadPoolExecutorImp::completeSpawn"
b9a4236a:"WinSymmCryptoCngImpl::setKey: BCryptGenerateSymmetricKey failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::setKey"
b9c3409b:"Unsupported prepareMethod: %u -> %s","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::AuthenticationHandlerImpl"
ba14ad88:"tlsRead: recv canceled","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsRead"
bad4a726:"Signature verification failed with %d(%s)","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
bad758f9:"MakeAgileCallbackNoThrow failed\n","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::registerListener"
bb279fe7:"entry CapabilityListener registerListener: %s","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::registerListener"
bb50b690:"Resolver operation failed %d","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::ResolverOperation::IDnsResolveDelegate_address"
bb8b14e6:"usrsctp_setsockopt SCTP_NODELAY: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
bbee70d6:"Attempt to create Proxy AUTH Cache during shutdown, failing","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::getProxyAuthCache"
bbf1aea2:"WindowsReactorImpl::stop","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::stop"
bc0ee1fe:"verifyCookieCallback: Session not found","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::verifyCookieCallback"
bc16a357:"getResult4(): status %lu","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::getResult4"
bc195241:"exit registerListener","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::registerListener"
bc81dca9:"Used %d retries to bind %d in %d-%d","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::socketConnected"
bc99a2ac:"Creating DTLS OpenSSL backend. OpenSSL 3.0.13 30 Jan 2024","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::DtlsBackendOpenssl"
bcb2872c:"WinAsymmCryptoCngImpl::decrypt: dst size too small\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::decrypt"
bce1284d:"Unable to parse <addr>:<port> string \"%s\"","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::parseProxyEntry"
bce80e90:"(%p) Connect %s -> %s:%d via %s proxy at %s:%u","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::startProxyConnectionsV2"
bd04536e:"Time out is %.3fs","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::start"
bd13c722:"doTLSConnect(): triggering async op for socket to become ready","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::doTLSConnect"
bd17d75e:"(%p) Start Pseudo-TLS","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::IStreamSocketDelegate_connected"
bd3915cb:"send4: GetLastError: %d","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::send4"
bd41ad05:"logPeerCert: summary: %s","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::logPeerCert"
bd4e71d1:"ONNX runtime is present, but is incompatible: no GetVersionString available.","RootTools/roottools/inference/inference_engine/src/onnx_dll_helper.cpp";"inference::onnx::DllHelper::DllHelper"
bd50b30d:"BCryptExportKey (get blob size) failed with NTSTATUS 0x%x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::BCryptExportKeyWrap"
bd51cd16:"Created","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::Connection"
bd5ab883:"SetupDiEnumDeviceInterfaces failed with code %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
bd9b2e82:"spl::socketError: getsockopt(%d): %s","RootTools/roottools/spl/src/sysdeps/win/socket_win.cpp";"spl::socketError"
be0f3755:"TlsEndpoint: SSL_write SYSCALL errno=%d, %s","RootTools/roottools/auf/src/tls_endpoint.cpp";"auf::`anonymous-namespace\'::TlsEndpoint::encrypt"
becd49ee:"Skipping network %p, because it has no interfaces","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::gatherNetworks"
bed71ca5:"Cannot load ecs.conf: %s","RootTools/roottools/spl/src/ecs.cpp";"`anonymous-namespace\'::EcsConfig::EcsConfig"
beeb02eb:"Geolocation executed","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::realReadGeoPositionAsync"
bef0637e:"verifyCert: X509_verify failed: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::verifyCert"
bf5ed218:"Encrypt: SSL_write: err %d, rc %d, %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Encrypt"
bf7eaa2b:"dltsFindSplitLen: Record at offset %lu and length %u would exceed buffer size %lu","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::dtlsFindSplitLen"
bfaa04a2:"(%p) Connected via proxy","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::completeOps"
bfb174d2:"WindowsReactorImpl::registerMessage","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::registerMessage"
bff74cca:"HTTP Response: %d %s","RootTools/roottools/net/src/proxy_tcp_connect_v2.cpp";"`anonymous-namespace\'::ProxyOperationV2::HTTPResponseReceived"
c049968e:"WARNING: Unexpected end of file encountered","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay"
c0bf716d:"Failed to get DXGIAdapter description [index = %d], error = %x","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::EnumerateAdapters"
c11bd7a3:"Log trigger telemetry: log upload size %u","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::logBufferVisitStats"
c131b366:"Failed to initialize mutex deadlock monitor\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::MutexDeadlockMonitor::start"
c138bfb9:"RQ%u: Fast retry after %lld ms","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::INetworkInfoDelegate_connectivityChange"
c14bbbb1:"proxyCallbackFunc: unexpected status %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::proxyCallbackFunc"
c15bb53a:"threadWinEntry: Thread is at Win32 priority %d.\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::threadWinEntry"
c1f426c4:"entry ~GeoLocationCapabilityChecker","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocationCapabilityChecker::~GeoLocationCapabilityChecker"
c1f55d8d:"Unexpected end of file in binary log","RootTools/roottools/auf/src/log2.cpp";"auf::DecryptReaderImpl::DecryptReaderImpl"
c2542e4f:"NAT64 prefix discovery succeeded","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INat64PrefixResolveDelegate_prefix"
c25e126e:"WinAsymmCryptoCngImpl::setPublicKey: decodePemPrivateKey failed","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::setPrivateKey"
c27d5a5e:"generateResponse: usableList/filtered after offer: %s","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
c27e691c:"packetReceived(%u, %u)","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::onPacketReceived"
c2b97017:"generateResponse: InitializeSecurityContext: err=%x","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::generateResponse"
c2f72794:"%s","RootTools/roottools/spl/include/rt/rt_object_registry.hpp";"rt::ObjectRegistry::get"
c31ed078:"WinAsymmCryptoCngImpl::encrypt: dst size too small\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::encrypt"
c35b83da:"SuspensionManager::strandTaskTimerExpired","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandTaskTimerExpired"
c3d49095:"InternetConnectivityManager::INetworkInfoDelegate_connectivityChange","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::INetworkInfoDelegate_connectivityChange"
c412458d:"Ignored exception and continuing\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::threadWinDispatch"
c4353c93:"Buffer updated, no change","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::updateLogBufferConfig"
c4374f0e:"TimerHandler(%p)::purge: no action","RootTools/roottools/auf/src/thread_pool_timer.cpp";"auf::TimerHandler::purgeCore"
c44a6b52:"halting triggerUpscaling(), g_threadPoolUpscalingDisabled = true)","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::ThreadPoolExecutorImp::triggerUpscaling"
c4a2b974:"createConvertedString: failed to get conversion length.","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::createConvertedStringAndCapacity"
c4a3e9d5:"Session %s (%p) outputs:\n%s","RootTools/roottools/inference/inference_engine/src/onnx_session.cpp";"inference::onnx::OnnxInferenceSession::CreateOutputPlaceholder"
c4cb6ef7:"Well-known addresses:","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::checkV4Addresses"
c4d60e89:"Obtaining app data path from SLIMCORE_APP_DATA_PATHW env variable, value=%s","RootTools/roottools/spl/src/path_location.cpp";"spl::internal::getCustomAppDataDir"
c50553a9:"logPeerCert: cert: %s","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::logPeerCert"
c510fa3d:"cancelNetworkWakeup() type %d","RootTools/roottools/net/src/net_wakeup.cpp";"rtnet::NetworkTokenCache::cancelNetworkWakeup"
c57a4da5:"Reporting battery info: state=%d, charge=%u%%, full_capacity=%umWh","RootTools/roottools/auf/src/power.cpp";"auf::getBatteryInfo"
c58c11fb:"(%p) TLS established, connected %s","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::IStreamSocketDelegate_tlsEstablished"
c6035e3b:"Timer started","RootTools/roottools/auf/private/misc.hpp";"auf::Cache<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,bool,struct std::chrono::steady_clock>::timerStartIfNotEmpty"
c606325b:"%s:%d: failed assertion \'%s\'\n","RootTools/roottools/spl/src/debug.cpp";"splAssertFailure"
c634624b:"IN_RESET_RECEIVED %s@%d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OnStreamResetNotification"
c64d82d3:"CachedInterfaceListOperation::start(): %d","RootTools/roottools/net/src/netinfo.cpp";"rtnet::CachedInterfaceListOperation::start"
c6650d59:"stopProcessing, started waiting for thread pool termination, time elapsed since the begining %lld ms (last worker took %lld ms), workers count = %u, lock free stack is %s","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::WorkStable::stopProcessing"
c6732084:"getResult: Wait error: %d","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::getResult"
c6a4e1bf:"unable to get metadata from json","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Check::<lambda_...>::operator ()"
c6fef5a5:"find()","RootTools/roottools/auf/private/misc.hpp";"auf::Cache<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,bool,struct std::chrono::steady_clock>::find"
c70300b5:"InternetConnectivityManager created","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::InternetConnectivityManager"
c72f4f47:"RQ%u: Response timed out","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::Timeout"
c76c7950:"tlsWrite: tried to write Alert, rc: %s","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsWrite"
c7924384:"generateResponse: auth_failed, removing method %s from list","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
c7b4a35a:"rtnet::priv::WlanApiHandle::~WlanApiHandle(): No wlan dll handle","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanApiHandle::~WlanApiHandle"
c8149538:"dropInterface() type %d","RootTools/roottools/net/src/net_wakeup.cpp";"rtnet::NetworkToken::cancel"
c84e0f80:"Log console updated, removing existing log console","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogConsole"
c88c8a98:"Size = %u Heads = { r = %u a = %u }\n","RootTools/roottools/auf/src/transport/srmw.cpp";"auf::SRMWFifo::dump"
c89cce7c:"rtnet::internal::InterfaceOperation::getWlanInfos(): WlanEnumInterfaces() call failed, error %lx","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getWlanInfos"
c90460a9:"OS proxy query successful: no proxy set","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::queryProxySettings"
c941ef67:"WinHttpGetProxyResult unicode conversion failed","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutoProxyEx"
c9ca8816:"CreateSession: cannot find registered model named %s","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::CreateSession"
c9ff951a:"Lockfree space depletion in BufferQueue::enqueueDesc(). Terminating","RootTools/roottools/net/private/net/common.hpp";"rtnet::BufferQueue<struct rtnet::StreamInputBuffer>::enqueueDesc"
ca186d42:"HousekeepDtls: BUG: unhandled return from endpoint: %d","RootTools/roottools/sctp/src/dtls_pipe.cpp";"`anonymous-namespace\'::DTLSPipe::HousekeepDtls"
ca271787:"usrsctp_socket: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
ca37e12c:"Failed to load Default provider, %s","RootTools/roottools/spl/src/spl_common.cpp";"`anonymous-namespace\'::OpensslProviders::load"
ca3872e1:"doDtlsConnectAccept: session or ssl not set","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::doDtlsConnectAccept"
ca42704b:"getTransport: illegal transport key (%u)","RootTools/roottools/auf/src/thread.cpp";"auf::ThreadRef::getTransport"
ca6e7b49:"RQ%u: Invalid URL %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::GetPromise"
ca78ccac:"Failed to create dump log file","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::dumpLogBuffer"
cacb6568:"WinAsymmCryptoCngImpl::keyLength: BCryptGetProperty(BCRYPT_KEY_STRENGTH) failed with NTSTATUS %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::keyLength"
caf1b514:"socketBindPortRange: used %d retries to bind %d in %d-%d","RootTools/roottools/spl/src/bindportrange.cpp";"spl::socketBindPortRange"
cb1dc4e7:"m_listener == nullptr: init failed, not requesting access","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanCapabilityChecker::RequestAccessAsync"
cb8e03ec:"Bad key used to decrypt binary log","RootTools/roottools/auf/src/log2.cpp";"auf::DecryptReaderImpl::DecryptReaderImpl"
cbe465e5:"CoInitializeEx (%s) failed with code 0x%08x","RootTools/roottools/auxd/skype/src/win/skype_win.cpp";"auf::threadSchedHintApply"
cbe8729d:"Fatal error: ThreadPoolManager did not succeed in allocating msg mem.\n","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::ThreadPoolManager::allocMsgMem"
cbf50ec2:"[reg] rip    %016llx [reg] eflags %08x [reg] cs     %08x [reg] ss     %08x [reg] ds     %08x [reg] es     %08x [reg] fs     %08x [reg] gs     %08x\n","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logRegistersFromContext"
cc0ab065:"Log file updated, no change","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::updateLogFileConfig"
cc1124c6:"(%p) TLS established","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::IStreamSocketDelegate_tlsEstablished"
cc84d931:"Setup SSL context...","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Setup"
cc9c1396:"  %s: %s","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::dumpLogLevels"
ccb20acd:"End of SPL info.\n","RootTools/roottools/spl/src/spl_common.cpp";"spl::sysInfoLogDetails"
ccb2b1bc:"Failed to create WSAEvent","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::WindowsReactorImpl"
ccdc6e65:"rtnet::internal::InterfaceImpl::getWlanInfo(): WlanQueryInterface(%d) call failed, error=0x%lx, size=%lu, data=%p","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::getWlanInfo"
cd398506:"Start","RootTools/roottools/net/src/dns_cache_v2.cpp";"`anonymous-namespace\'::DNSCache::DNSCache"
cd610242:"Verifying model.cat signature","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
cd86bdb6:"Fatal error: ioEventSem == 0\n","RootTools/roottools/spl/src/sysdeps/win/event_win.cpp";"spl::eventPost"
cd98d588:"Failed to get GPU adapters, no enumeration","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetGPUAdapters"
cda503a6:"Format strings are different for same key: %u. \'%s\' vs \'%s\'\n","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_merge_infos"
cdb1afd6:"dtlsCreate: SSL_CTX_set_cipher_list() failed, no cipher could be selected","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::dtlsCreate"
ce3c3268:"success=%d","RootTools/roottools/net/src/geolocation.cpp";"rtnet::GeoPositionOperation::GeoPositionCallbackReceived"
ce931be3:"%u messages decoded","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay"
ce94f8a6:"(%p) Connected %s, local %s","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::IStreamSocketDelegate_connected"
cec8672a:"CpuTimeMonitor: system CPU usage %.2f, process CPU usage %.2f, number of cores %u","RootTools/roottools/auf/src/cputime_monitor.cpp";"auf::internal::CpuTimeMonitor::logCpuTime"
ceeb867b:"spl::switchRealtimeStreamingMode(): WlanOpenHandle error 0x%x","RootTools/roottools/spl/src/sysdeps/win/platform.cpp";"spl::priv::wlanApiInit"
ceef67c7:"deferredPacketSent: WSASend%s returned unexpected number of bytes sent: asked %zu, but sent %u","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::DatagramSocketImpl::packetSent"
cef90faf:"MRMWTransport: safe allocation failed\n","RootTools/roottools/auf/src/transport/thread_default_transport.cpp";"auf::priv::MRMWTransport::allocMsgMem"
ceff9b70:"spl::pathCreateFromLocation(): Could not add path component %s","RootTools/roottools/spl/src/path_location.cpp";"spl::pathCreateFromLocation"
cf0f584d:"Received power broadcast event %I64u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::IWindowsReactorDelegate_message"
cf33adcc:"Thread pool manager: couldn\'t create SPL thread","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::ThreadPoolManager::ThreadPoolManager"
cf895e65:"ResolverOperation::~dtor","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::ResolverOperationNF::~ResolverOperationNF"
cfc1d3d8:"InterfaceImpl: Rssi_Changed: rssi: %d -> %d; bssInfoUpdated: %s; guid: %s","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::IWlanRssiListenerDelegate_Rssi_Changed"
cfe95133:"OS proxy query failed: fatal error, no more retries","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::queryProxySettings"
d00540bd:"Failed to open LFSP dump file %s : %s","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::dumpCalls"
d0256203:"Failed to read from ul.conf","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readFile"
d05278f3:"ONNX trace: %s (%s)","RootTools/roottools/inference/inference_engine/src/onnx_engine.cpp";"`anonymous-namespace\'::OnnxLog"
d05f12c5:"spl::priv::captureBackTraceInfoCore(): %s. Windows error Message: (0x%x) %ls\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::traceLastError"
d0613f71:"startWithAddress: setStreamSocketOptions()","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::doConnectSingle"
d0719a1f:"(%p) Connect %s -> %s:%d via proxy %s:%u","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::startProxyOps"
d089bc22:"Warning: leaving hinted thread (hint = %p) without special sheduling","RootTools/roottools/auxd/skype/src/win/skype_win.cpp";"auf::threadSchedHintApply"
d0a38188:"toRtnetErrorCode: error: 0x%x","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::toHandshakeResult"
d0cc8b0a:"auf::init() from %s g_aufUp=%d","RootTools/roottools/auf/src/auf.cpp";"auf::internal::init"
d0cf1997:"noPingCallback: Pinger failed","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::TraceRouteOperation::noPingCallback"
d0e54cbf:"ctor","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::TcpConnectOperation"
d0eaa2ac:"getBestRoute() GetBestRoute2 failed, err=%u","RootTools/roottools/net/src/sysdeps/win/route_win.cpp";"rtnet::localAddressForDestination"
d104dff7:"Invoke: set result to %d, Wlan accessStatus %d","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanAccessStatusAsyncOpHandler::Invoke"
d11869ee:"generateResponse: failure","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
d1636c62:"Certificate signer verification failed with error(%u): %s","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
d17d0310:"DefaultStandardTransport: allocMsgMem failed","RootTools/roottools/auf/src/transport/thread_default_transport.cpp";"auf::priv::DefaultStandardTransport::allocMsgMem"
d18a1bde:"deferredPacketSent: WSASend status %u\n","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::packetSent"
d19d8c80:"Log sending disabled due to PII unsafe logs included in buffer or trigger config, buffer %d, config %d","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::triggeredDefered"
d1c72d3a:"WinKeyPairGenerationCngImpl::exportPrivateKey: BCryptExportKey failed","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinKeyPairGenerationCngImpl::exportPrivateKey"
d1e4630a:"threadRevokeMmCharacteristics failed with error = %lx\n","RootTools/roottools/spl/src/sysdeps/win/thread_win.cpp";"spl::win::threadRevokeMmCharacteristics"
d1ef0873:"#%02u\t%016I64x\t%ls!%s+0x%I64x\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::logStackBackTraceDbgHelpCore"
d24991c5:"HMACWinCngImpl::HMACWinCngImpl: no hash provider\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::HMACWinCngImpl::HMACWinCngImpl"
d25db4a5:"Corrupted persistent cache data","RootTools/roottools/net/src/dns_cache_v2.cpp";"`anonymous-namespace\'::DNSCache::LoadPersistent"
d2642df1:"ONNX inference session %s (%p) run failed: %s (%d)","RootTools/roottools/inference/inference_engine/src/onnx_session.cpp";"inference::onnx::OnnxInferenceSession::Run"
d2680d49:"Fatal error: ThreadPoolExecutorImp did not succeed in allocating msg mem.\n","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::ThreadPoolExecutorImp::allocMsgMem"
d29a6a54:"exit spl.CapabilityMonitor.atStop","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::instance::<lambda_...>::operator ()"
d2d4d7d8:"Could not suspend the thread, probably because the thread has terminated (%lx)\n","RootTools/roottools/spl/src/sysdeps/win/debug_win_context_native.cpp";"spl::priv::captureContextForThreadCoreBegin"
d348ef38:"IDs of spl::fileOpen() calls that leaked file handles:\n","RootTools/roottools/spl/src/file.cpp";"spl::priv::FileHandlesTracker::log"
d3761104:"WinAsymmCryptoCngImpl::encrypt: BCryptEncrypt failed with NTSTATUS %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::encrypt"
d3a2973a:"Start %s:%d","RootTools/roottools/net/src/sysdeps/berkeley/happy_eyeballs_bsd_v3.cpp";"`anonymous-namespace\'::HappyEyeballsV3::start"
d3cbeae7:"CreateFileW failed with code %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
d3d9f7ff:"DtlsKeyCertManager::init encKey is empty!","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::DtlsKeyCertManager::init"
d4191295:"RQ%u: Rewritten url with reverse proxy \"%s\"","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Open"
d45166a2:"WinHttpGetProxyResult unicode conversion failed","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
d48b1603:"InternetConnectivityOperation::onTerminalStateReached()","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityOperation::onTerminalStateReached"
d494f7b6:"GeoLocationAccessResponseReceived: got answer: %s","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::ChainedGeoLocationOperation::{ctor}::<lambda_...>::operator ()"
d498f872:"WinHttpOpen failed with error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
d49d99e9:"send(%p, %I64u) -> connection closed","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::ITlsIO_write"
d501bb4a:"Reading LLDP info","RootTools/roottools/net/src/lldp.cpp";"rtnet::ReadLLDPinfo"
d50401ea:"dtor","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::~TcpConnectOperation"
d509abde:"WinSymmCryptoCngImpl::doAEOp: BCryptEncrypt failed with NTSTATUS %d","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::doAEOp"
d50bbc73:"FinalizationTask::dispatchCreated","RootTools/roottools/auf/src/background.cpp";"auf::internal::FinalizationTask::dispatchCreated"
d536642a:"Stopped in a dirty state","RootTools/roottools/spl/src/rt_persistent.cpp";"`anonymous-namespace\'::PersistentStorage::~PersistentStorage"
d53c9a8f:"Bad message id encountered: %d, last log Id: %x","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replayOne2"
d5593398:"%s","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::GetPromise"
d56e0fe1:"entry WlanCapabilityChecker","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanCapabilityChecker::WlanCapabilityChecker"
d5b25840:"Failed to read file: %s","RootTools/roottools/auf/src/log2.cpp";"auf::CompressReader::read"
d5e32800:"WlanRssiListener OnWLANCallback: GUID %s rssi %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::OnWLANCallback"
d5ecfedf:"MonitorOperation::cleanup_v6","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::cleanup_v6"
d62667aa:"!m_op","RootTools/roottools/net/src/geolocation.cpp";"rtnet::GeoPositionOperation::deferredStart"
d651b076:"Address::Address: sockAddrFromPresentationString() failed for address %s","RootTools/roottools/net/src/address.cpp";"rtnet::Address::fromString"
d69037e7:"Stop, init_count=%u stop_count=%u","RootTools/roottools/httpstack/src/stack_init.cpp";"http_stack::stop"
d69fd357:"InterfaceImpl::recvRate(): off due to privacy flag","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::recvRate"
d73161a1:"OS proxy query failed: %d","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::getSystemProxyListForUrl"
d76340b4:"startTlsAsync() %s","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::startTlsAsync"
d772924a:"LockfreeStackPool %p: Size %I64u FreeOffs %u (%#x)\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::dump"
d7815035:"Start %s:%d; max attempts %u delay %u","RootTools/roottools/net/src/connect_tcp_n.cpp";"`anonymous-namespace\'::TCP_N_Operation::Start"
d78788a1:"Cannot shutdown in 3 attempts","RootTools/roottools/httpstack/src/stack_init.cpp";"SendShutdownSignal"
d7f9a8c1:"DATA_CHANNEL_OPEN on already assgned sid %s@%d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OnDataChannelOpen"
d7fbee0e:"Lock stack of %s on thread %u:\n","RootTools/roottools/auf/src/mutex_orderer_tree.cpp";"auf::internal::MutexOrdererTree::checkForLockInversions"
d7fecb99:"entry ~CapabilityMonitor","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityMonitor::~CapabilityMonitor"
d82cebb5:"firing: %s","RootTools/roottools/auf/src/power.cpp";"auf::PowerEventManager::notifyPowerEvent"
d870e5d3:"(%p) TLS established, connected %s","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::IStreamSocketDelegate_tlsEstablished"
d8cdccf9:"INetworkInfoDelegate_internetConnectivityChange: interfaces size=%d","RootTools/roottools/net/src/netinfo.cpp";"rtnet::CachedInterfaceListOperation::INetworkInfoDelegate_internetConnectivityChange"
d8d662f1:"ONNX runtime initialization fatal failure: %s (%d)","RootTools/roottools/inference/inference_engine/src/onnx_engine.cpp";"`anonymous-namespace\'::OnnxRuntime::Make"
d8e2bc9b:"Cannot parse DATA_CHANNEL_OPEN @%d","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OnDataChannelOpen"
d8ef605c:"  <root>: %s","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::dumpLogLevels"
d8f84a4c:"Unable to get signer data","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
d9481583:"appCapability->CheckAccess failed","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::getAppCapabilityStatusSync"
d949a8a9:"Access is denied to GetLanIdentifiers (wiFiControl)","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::requestAccessAndShowHint"
d9633cda:"fileOpen: !foaIsValid, foa=%u","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::WinSplFileImpl::fileOpen"
d9729bfc:"Cannot register for Connected Standby power notifications","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::start"
d9b7ee9b:"Out of strand IDs! This is fatal.","RootTools/roottools/auf/src/thread_pool_strand.cpp";"auf::strandAllocId"
d9f78d42:"SuspensionManager::strandSuspended","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandSuspended"
da0aef71:"attachTransport: illegal transport key (%u)","RootTools/roottools/auf/src/thread.cpp";"auf::ThreadRef::attachTransport"
da367219:"send(%u)","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::sendNextPacket"
da407384:"Store cookie %s domain=%s path=%s secure=%d","RootTools/roottools/httpstack/src/sysdeps/rt/cookie_store.cpp";"http_stack::skypert::CookieStore::DrainSetCookieHeader"
da9142d4:"Pinger::cancelSync took %s","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::cancelSync"
daaf8f86:"ProxyOnly policy is set, but no proxies are found","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::deferredStart"
dac9cc80:"verifyCookieCallback: invalid cookie length","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::verifyCookieCallback"
db1be54a:"RQ%u: Set header %s: \"%s\"","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::SetHeader"
db20f368:"Getting model.json from TarFile","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::verifyCatalog"
db23472b:"Failed to read the logmap json file: %s error: %s","RootTools/roottools/auf/src/logmap.cpp";"auf::internal::logmap_read_file"
db3b6fad:"socketBindPortRange: port range %d-%d is exhausted","RootTools/roottools/spl/src/bindportrange.cpp";"spl::socketBindPortRange"
db8c314e:"Missing handle m_wlanHnd=%p wlanDll=%p","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanRssiListener::onTerminalStateReached"
dbf8d2fe:"Manual proxy is not enabled","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getManualProxy"
dc2308ca:"dtlsCreate: certificate not provided","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::dtlsCreate"
dc49c82f:"Bad config in ul.conf: %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
dc5c9088:"Backtrace:","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logStackBackTraceManualCore"
dc68c012:"List of loaded modules space exceeded while trying to load symbols for module %s","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::DbgHelpFunctions::enumerateLoadedModules"
dc8352a9:"Using HTTP proxy override: no proxy","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::getSystemProxyList"
dc90bf42:"spl::isWritable(): AccessCheck() failed, error code %lx\n","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::pathIsReadWritable"
dcc82671:"getKeyCertFileNames: key/cert store not supported 1: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::getKeyCertFileNames"
dcd111a8:"recv(%p, %I64u) -> 0, connection closed","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::ITlsIO_read"
dceed92a:"Pinger::ctor","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::PingerNF::PingerNF"
dcfaeb2b:"MonitorOperation::dispatchChange","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::dispatchChange"
dd131c14:"start","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::ResolverOperation::start"
dd2d7932:"[reg] r8     %016llx [reg] r9     %016llx [reg] r10    %016llx [reg] r11    %016llx [reg] r12    %016llx [reg] r13    %016llx [reg] r14    %016llx [reg] r15    %016llx\n","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logRegistersFromContext"
dd43fc1d:"File not found: %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readFile"
dd57639a:"Socket error during handshake: %s (%d)%s","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::IStreamSocketDelegate_error"
dd78e99d:"Path::appendComp: Unable to convert component %s","RootTools/roottools/spl/src/sysdeps/win/file_win.cpp";"spl::Path::appendComp"
ddb2d7e1:"Resolver operation failed %d","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::ResolverOperationNF::IDnsResolveDelegate_address"
ddf4a8de:"RQ%u: Retry after %lld ms","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::RetryRequest"
de0316d1:"Setup: Failed to decode DER encoded X509Cert. %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Setup"
de33cd7d:"socketBindPortRange %d-%d: retry %d after: (%d) %s","RootTools/roottools/spl/src/bindportrange.cpp";"spl::socketBindPortRange"
de4dd8aa:"Created compatibility RootTools thread pool (prio P.%s, id 0x%I64x, thread min %u max %u)\n","RootTools/roottools/auf/src/thread_pool.cpp";"auf::createCompatThreadPoolExecutor"
de4e3ef7:"rtnet::priv::WlanApiWrapper::WlanApiWrapper(): Loading Rasapi32.dll failed","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::WlanApiWrapper::WlanApiWrapper"
de5cd90d:"GetLanIdentifiers allowed, initialization for LLDP","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::LLDPreader::LLDPreader"
de7fb425:"verifyServerCert: Error 0x%x querying remote certificate","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::verifyServerCert"
de8a925f:"RQ%u: Set MaxRT=%u, accepted MaxRT=%u","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IntroduceSender"
debc2656:"DiskController Identifier: %ls","RootTools/roottools/spl/src/sysdeps/win/fingerprint_win.cpp";"spl::internal::appendDiscId"
df274b03:"%s","RootTools/roottools/auf/src/mutex_orderer_tree.cpp";"auf::internal::MutexOrdererTree::checkForLockInversions"
df2bed22:"WinHttpGetProxyForUrl for [%s]: %u %s","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
df47bdc8:"WinHttpOpen failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutomaticProxy"
df86077e:"WinHashCngImpl::clear: BCryptCreateHash failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinHashCngImpl::clear"
dfb75e6e:"usrsctp_sendv: %d %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::FlushWriteToSctpQueue"
dfe15ad4:"WinHashCngImpl::update: BCryptHashData failed with NTSTATUS %d\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinHashCngImpl::update"
dfec918d:"RQ%u: Cannot open request, state=%d","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Open"
e01d7afb:"ONNX runtime version: %s","RootTools/roottools/inference/inference_engine/src/onnx_dll_helper.cpp";"inference::onnx::DllHelper::DllHelper"
e029b4bc:"tlsDecrypt: Context expired. cPtr %p, cLen %I64u","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsDecrypt"
e02e5ab8:"SplOpaqueUpperLayerThread: join event creation failed\n","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::SplOpaqueUpperLayerThread"
e0378b1f:"The signature is present, but specifically disallowed","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
e05376be:"Purging value","RootTools/roottools/auf/private/misc.hpp";"auf::Cache<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,bool,struct std::chrono::steady_clock>::purgeExpired"
e0586989:"getResult: invalid handle","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::getResult"
e06b294d:"InternetConnectivityManager::deferredRegister: added %p to m_listeners","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::deferredRegister"
e074a13e:"RQ%u: Acquired chunk %d bytes","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Complete"
e086b51d:"WinAsymmCryptoCngImpl::WinAsymmCryptoCngImpl: no crypto algorithm provider\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::WinAsymmCryptoCngImpl"
e0970c23:"Effective BufferedWriter buffer size: %zu","RootTools/roottools/auf/src/log2.cpp";"auf::BufferedWriter::BufferedWriter"
e09e1352:"MonitorOperation::initialize_v4only: failed to register address handle with reactor","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::initialize_v4only"
e0ae1e2a:"RQ%u: Open %s \"%s\"","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::Open"
e0f1086b:"WorkStable %p: Unable to allocate WorkStableItem.","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::WorkStable::createWorkStableItem"
e0f1b5a8:"(%p) Connected directly","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::completeOps"
e1089a46:"Setup: force auf::%s = %d","RootTools/roottools/auf/src/setup.cpp";"importIntConfig"
e119e646:"Failed to get DxgiAdapter LUID, incorrect adapter index = %d","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::GetAdapterLUID"
e18c6fa1:"WinHttpCreateProxyResolver failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutoProxyEx"
e1a4a566:"Registry size %u","RootTools/roottools/spl/src/rt_custom_storage.cpp";"`anonymous-namespace\'::Registry::~Registry"
e1d03de0:"InternetConnectivityManager::deferredRegister: cache invalidated, triggering refresh","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::deferredRegister"
e1e5577b:"Uhm. Crazy bin requested to allocateBin, check calls to binForSize.\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::allocateBin"
e1f362e0:"  %s (%08x) %d","RootTools/roottools/net/src/resolver_nat64.cpp";"rtnet::priv::checkV4Addresses"
e2319e36:"entry ~CapabilityListener()","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::~CapabilityListener"
e249728e:"usrsctp_bind %d: OK","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
e2547ead:"AUF information:","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
e26b68d9:"clearTransport: attempted to clear transport to standard key (%u)","RootTools/roottools/auf/src/thread.cpp";"auf::ThreadRef::clearTransport"
e27c8520:"Handshake: SSL_connect/SSL_accept: rc %d, err %d, %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Handshake"
e27fd6e1:"generateResponse: no common method found! Resetting list.","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
e2b41600:"Visiting log buffer reverse order","RootTools/roottools/auf/src/log2.cpp";"auf::LogBuffer::deferredVisit"
e35cb2ba:"LockfreeStackPoolImp: safe allocation failed! Dumping %u bytes of pool contents:\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::internal::LockfreeStackPool::allocateCore"
e3625cd6:"MAC: %s","RootTools/roottools/spl/src/sysdeps/win/fingerprint_win.cpp";"spl::internal::appendMACHash"
e3b79088:"TlsEndpoint: SSL_read SYSCALL, errno=%d, %s","RootTools/roottools/auf/src/tls_endpoint.cpp";"auf::`anonymous-namespace\'::TlsEndpoint::decrypt"
e3d30773:"SslWrap(): unable to allocate SSL object","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::IOCPSslWrap"
e3dab3c1:"RQ%u: firstAvailableBufferReceived %d","RootTools/roottools/httpstack/src/sysdeps/rt/connection.cpp";"http_stack::skypert::Connection::IStreamSocketDelegate_firstAvailableBufferReceived"
e3ed742f:"WindowsReactorImpl::handleUnregisterMessage","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::handleUnregisterMessage"
e4337fd5:"InterfaceImpl::deferredStart()","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::deferredStart"
e43e91a0:"startWithAddress","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::startWithAddressDeferred"
e44738fa:"Bind socket for %s:%d: %s","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::startWithAddressDeferred"
e48c4e77:"generateResponse: headerValues %s (%zu bytes)","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
e48d79e9:"WinAsymmCryptoCngImpl::setPublicKey: CryptDecodeObjectEx failed with error %x\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinAsymmCryptoCngImpl::setPublicKey"
e52dd36e:"%s","RootTools/roottools/httpstack/src/backend.cpp";"http_stack::CreateHttpStack"
e5563da1:"deleteFromStore: Failed to delete cert: %s","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::DtlsKeyCertPersistent::deleteFromStore"
e5bd3732:"Unknown type in binary log: %u","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::LogArgsFromBinary::LogArgsFromBinary"
e639e21a:"generateResponse: malformed response from proxy","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
e63ce3ce:"Timed out","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::timeOut"
e6679573:"generateResponse: %s returned unhandled error code %s","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
e6bbfbdc:"generateResponse: proxy authentication is disabled","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
e6cb7e34:"RQ%u: Restart retry delay sequence","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::PooledRequest::RestartRetryDelaySequence"
e6f3f18f:"OS proxy query successful: found %u proxies","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::getSystemProxyListForUrl"
e71e3366:"ThreadPoolExecutorImp: %p unfortunately detected that an allocation failed. This is an unrecoverable error. Bye.\n","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::ThreadPoolExecutorImp::workLoop"
e72cd12d:"Total of %u NAT64 prefixes %s","RootTools/roottools/net/src/netinfo.cpp";"rtnet::InternetConnectivityManager::dispatchNat64Prefixes"
e7656111:"  %p/%s\n","RootTools/roottools/auf/src/mutex_orderer_tree.cpp";"auf::internal::MutexOrdererTree::checkForLockInversions"
e7947881:"Destroyed","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::PooledRequest::~PooledRequest"
e7978382:"WinHttpSetStatusCallback failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutoProxyEx"
e7aad654:"CreateInstance (NetworkListManager) failed. hr=%d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getNetworkParams"
e7e36510:"Bad thread pool id encountered","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replay1"
e80c9fcf:"HMACWinCngImpl::clone: source hash is not good\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::HMACWinCngImpl::clone"
e80dcea6:"Clearing cached log files, logfile with future timestamp found","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::createDumpLogFileName"
e8248244:"Resolver operation failed","RootTools/roottools/net/src/traceroutenf.cpp";"rtnet::internal::ResolverOperationNF::start"
e8861c8e:"MFThreadPool (ctor): (taskName = %ls, basePrio = %ld)\n","RootTools/roottools/spl/src/sysdeps/win/thread_pool_win.cpp";"`anonymous-namespace\'::MFThreadPool::MFThreadPool"
e89c7c0b:"ResolverOperation::ctor","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::ResolverOperation::ResolverOperation"
e8ecb0ab:"TLS %s Alert: %s %s","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::logIfAlert"
e90087c1:"Failed to get path info with error %d","RootTools/roottools/auf/src/logmap.cpp";"auf::logReadLogmap"
e9025bbd:"Network connectivity changes too fast, ignored","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::INetworkInfoDelegate_connectivityChange"
e921bf53:"InterfaceImpl::sendRate(): off due to privacy flag","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::sendRate"
e92836df:"Buffer not enabled, log not dumped","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::mergeAndDumpLogBuffer"
e978c2e0:"Privacy API not enabled","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::getAppCapabilityStatusSync"
ea019c5e:"getResult: Ping was cancelled","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::getResult"
ea138a2d:"sendBufferAsync(%I64u)","RootTools/roottools/net/src/sysdeps/win/stream_socket_iocp.cpp";"rtnet::internal::IOCPStreamSocket::sendBufferAsync"
ea180b55:"RQ%u: Cannot create timer","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::Launch"
ea4f0284:"MonitorOperation::onTerminalReached","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::onTerminalStateReached"
eac1b8dc:"External log buffer size: %I64u","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::mergeAndDumpLogBuffer"
eafaae28:"Unable to create rtnet::Factory. This is fatal, bye.\n","RootTools/roottools/net/src/common.cpp";"rtnet::getFactory"
eb55ddf2:"Unable to find valid HTTP(S) proxy information from \"%s\"","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::parseProxyList"
eb596e46:"(%p) Connected, local %s","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::IStreamSocketDelegate_connected"
eba7aa8c:"(%p) Start TLS, local %s","RootTools/roottools/net/src/generic_tcp_connect_v2.cpp";"rtnet::GenericConnectTCPOperationV2::IStreamSocketDelegate_connected"
ebc10456:"Creating TLS SChannel backend","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::createTlsBackend"
ebf41cef:"Skipping Teredo interface","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::deferredStart"
ec518ffb:"RQ%u: No Location header with %d redirect","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::DetectRedirect"
ec7228fd:"cancel called","RootTools/roottools/net/src/sysdeps/win/ping.cpp";"rtnet::internal::WinPingRequest::cancel"
ec9097c5:"entry join()","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::`anonymous-namespace\'::ThreadExec::join"
ec95f3a9:"send(%p, %I64u) -> RTNET_TLS_RETRY_WRITE","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::ITlsIO_write"
ec9be6dc:"WinHttpCreateProxyResolver failed with error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
ecabe158:"RoGate init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::CapabilityCheckBase::PreCheck"
ecc72071:"RQ%u: Request launch","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Launch"
ecc923bd:"auf::Mutex::lock(): Thread %u is trying to lock %s (%p), which is owned by thread %u, which is waiting for %s (%p), which owned by thread %u\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::MutexDeadlockMonitor::deadLckDetectedExiting"
ecd0d28e:"realReadGeoPositionAsync","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::realReadGeoPositionAsync"
ece1af55:"GetActivationFactory failed\n","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::registerListener"
ecefbdae:"firing: %s","RootTools/roottools/auf/src/power.cpp";"auf::PowerEventManager::notifyPowerSourceEvent"
ed02d98d:"SslWrap::ITlsIO_read: Socket reset","RootTools/roottools/net/src/sysdeps/win/iocp_ssl_wrap.cpp";"rtnet::internal::IOCPSslWrap::ITlsIO_read"
ed1719a6:"WinHttpGetProxyForUrlEx failed with error %u","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutoProxyEx"
ed1e7e8e:"Registers dumping not supported on this platform.\n","RootTools/roottools/spl/src/debug.cpp";"splLogRegisters"
ed98f720:"tlsRead: decrpytion finished","RootTools/roottools/net/src/sysdeps/win/tls_schannel.cpp";"rtnet::internal::TlsBackendSChannel::tlsRead"
ee31d53e:"GetNetworkConnections failed. hr=%d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceOperation::getNetworkParams"
ee49b84e:"getSelfSignedEcdsaCert: OPENSSL_malloc failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::getSelfSignedEcdsaCert"
ee52ccaf:"QB build number/id: %s","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
ee893d1f:"MutexDeadlockMonitor periodic check done\n","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::MutexDeadlockMonitor::runCore"
ee89559c:"Stopping CPU time monitor","RootTools/roottools/auf/src/cputime_monitor.cpp";"auf::internal::CpuTimeMonitor::updateInterval"
eec46443:"::PdhAddEnglishCounterW for CPU performance failed, status %d","RootTools/roottools/spl/src/sysdeps/win/system_resources_pdh_win.cpp";"`anonymous-namespace\'::CpuStatsQueryState::init"
eec51e0a:"DATA_CHANNEL_OPEN enqueued %s@%d %s","RootTools/roottools/sctp/src/peer_connection.cpp";"rtsctp::priv::PeerConnection::OpenClientDataChannel"
eee86d77:"ComNetworkListener failed to return its IUnknown interface, HRESULT=0x%08x","RootTools/roottools/net/src/sysdeps/win/netmon_winclassic.cpp";"rtnet::internal::win::NetworkMonitorOperation::startDeferred"
ef299ce5:"Loading of %s failed during opening, %s (%d)","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile::Read::<lambda_...>::operator ()"
ef3ebfd7:"SuspensionManager::strandUnregisterMonitor","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandUnregisterMonitor"
ef45ac8d:"SetupDiGetDeviceInterfaceDetailW failed with code %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
efa8da6d:"iocpLoop: ovrlapped operation completed with error","RootTools/roottools/net/src/sysdeps/win/factory_iocp.cpp";"rtnet::internal::SingleThreadIOCP::iocpLoop"
efe2e4e3:"Invalid ECS value GenericTcpConnect_Version=%u","RootTools/roottools/net/src/generic_tcp_connect_factory.cpp";"rtnet::connectTCPHostAsync"
f023622f:"%s","RootTools/roottools/spl/src/sysdeps/win/debug_win_dbghelp.cpp";"spl::priv::loadDebugInfoDbgHelp"
f02d9185:"createSingleThreadExecutor(): couldn\'t create SPL thread","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::createSingleThreadExecutor"
f05d8dbe:"Triggers updated, keeping trigger %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogTriggers"
f0602af4:"SuspensionManager::suspendable","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::suspendable"
f0acfe6e:"SuspensionManager::registerMonitor","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::registerMonitor"
f0b02d81:"MutexDeadlockMonitor thread running at interval: %I64u","RootTools/roottools/auf/src/mutexdeadlockmonitor.cpp";"auf::internal::MutexDeadlockMonitor::run"
f0b04376:"Bad marker byte encountered","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::replayOne2"
f0c0fdec:"Expected !m_logFiles.back().config","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
f0cdf61e:"generateEcdsaKey: completed %lldns","RootTools/roottools/auf/src/dtls_key_cert_manager.cpp";"auf::internal::`anonymous-namespace\'::generateEcdsaKey"
f1008018:"An exception occurred: %s (%X) at address %p\n","RootTools/roottools/spl/src/sysdeps/win/exception_filter.cpp";"spl::priv::winSehLoggingExceptionFilter"
f122f4ff:"Log console updated, no change","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::updateLogConsoleConfig"
f12e5a81:"Manual proxy information is not set","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getManualProxy"
f1405f0d:"Timed out","RootTools/roottools/net/src/generic_tcp_connect_v3.cpp";"rtnet::GenericConnectTCPOperationV3::timeOut"
f1691c92:"result=timed_out, retrying...","RootTools/roottools/net/src/geolocation.cpp";"rtnet::GeoPositionOperation::deferredCallback"
f1ae7603:"InterfaceImpl dtor GUID %s","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::~InterfaceImpl"
f1b84182:"usrsctp_setsockopt pathmtu=%d: %s","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::SCTPConnect"
f1c44234:"doConnectSingle: could not create a nonblocking socket of family %d","RootTools/roottools/net/src/sysdeps/win/connect_iocp.cpp";"rtnet::internal::TcpConnectOperation::doConnectSingle"
f1e08e5c:"SuspensionManager::unregisterMonitor","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::unregisterMonitor"
f1f4f74f:"The signature is present, but not trusted","RootTools/roottools/spl/src/sysdeps/win/catalog_win.cpp";"spl::verifyCatalogSignature"
f20c35f4:"TimerImpStateMachine::endDispatch: Illegal call - no concurrency","RootTools/roottools/auf/src/thread_pool_timer_sm.cpp";"auf::TimerImpStateMachine::endDispatch"
f22903da:"recvRate cache expired","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::InterfaceImpl::recvRate"
f23250a4:"Successfully received a valid fake ServerHello","RootTools/roottools/net/src/pseudo_tls.cpp";"`anonymous-namespace\'::PseudoTlsOperation::IStreamSocketDelegate_firstAvailableBufferReceived"
f23c697a:"Avoiding proxy path, because we can successfully connect to this hostname directly","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::startProxyOps"
f23ecb06:"OS proxy querying not implemented, no retries","RootTools/roottools/net/src/proxy_manager.cpp";"rtnet::internal::SystemProxyManager::queryProxySettings"
f2564137:"LogTrigger %s: maximum number of uploads exceeded, disabling","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogTrigger::ILogAppender_log"
f2915097:"MonitorOperation::dtor","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::~MonitorOperation"
f2a6fabd:"WinHttpSetStatusCallback failed with error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
f2b6012d:"Failure setting datagram socket options for %s:%d","RootTools/roottools/net/src/sysdeps/win/datagram_socket_iocp.cpp";"rtnet::internal::doUdpBind"
f2bf0622:"SuspensionManager::ctor","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::SuspensionManager"
f2d4e8f2:"WindowsReactorImpl::ping","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::ping"
f2f634b7:"GetActivationFactory failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::DisplayAccessHint"
f32c6732:"Unknown dest IP","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::Pinger::Pinger"
f338fdbc:"accessRequestHandler init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocatorImpl_RequestAccessAsync"
f373c75c:"SuspensionManager::dtor","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::~SuspensionManager"
f39acfe3:"Hash not found in metadata","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::getHash"
f3e4fcf3:"initIdentity: digest data extraction failure","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::initIdentity"
f3ebe3d0:"startWithAddress: setStreamSocketOptions()","RootTools/roottools/net/src/sysdeps/win/tcp_listen_win.cpp";"rtnet::priv::TcpListenOperationWin::startWithAddressDeferred"
f4007f65:"SuspensionManager::strandResume","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandResume"
f41bb797:"STATE: S_SUSPENDABLE","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandSuspendable"
f447f825:"Network status: (%s) %s","RootTools/roottools/net/src/netmon.cpp";"rtnet::priv::NetworkMonitor::dispatchChange"
f4657cc4:"RQ%u: Has HTTP data leftover %u","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::ResponseChunkReceived"
f4691f59:"createPool: ret=%p size=%d","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::ILockfreeStackPool::make"
f4fea11b:"DeviceIoControl(IOCTL_BATTERY_QUERY_TAG) failed with code %u","RootTools/roottools/auf/apal/sysdeps/win/power_win.cpp";"apal::WindowsPowerEventManager::collectStaticBatteryInfo"
f5033f25:"Appender added, detached=%s","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::addAppender"
f54c9b95:"RegisterFromFile: cannot register model %s, empty path provided","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromFile"
f57505a9:"Form factor: %s\n","RootTools/roottools/spl/src/spl_common.cpp";"spl::sysInfoLogDetails"
f576d4a0:"Created version %s","RootTools/roottools/httpstack/src/sysdeps/rt/backend_impl.cpp";"http_stack::skypert::BackendImpl::BackendImpl"
f5b9c25a:"createPool: Out of memory, %u bytes requested\n","RootTools/roottools/auf/src/lockfree_stack_pool_imp.cpp";"auf::ILockfreeStackPool::make"
f63c4af4:"No string terminator found","RootTools/roottools/auf/src/log2.cpp";"auf::BinaryFormatReader::readstring"
f6ed3988:"ONNX inference session %s (%p), loaded metadata keys: %s","RootTools/roottools/inference/inference_engine/src/onnx_session.cpp";"inference::onnx::OnnxInferenceSession::OnnxInferenceSession"
f6f5ffdb:"RoGate init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::realReadGeoPositionAsync"
f6f72f40:"(%p) Start TLS, local %s","RootTools/roottools/net/src/generic_tcp_connect_v1.cpp";"GenericConnectTCPOperation::IStreamSocketDelegate_connected"
f713bb73:"auf::stopInternal() stop called with unknown tag %p %s","RootTools/roottools/auf/src/auf.cpp";"AufInitializationRegistry::addStop"
f7a0f422:"RQ%u: Input stream error: %s","RootTools/roottools/httpstack/src/sysdeps/rt/http_request.cpp";"http_stack::skypert::HTTPRequest::Fail"
f7aaafb8:"WinHttpGetProxyForUrl returned empty proxy info","RootTools/roottools/spl/src/sysdeps/win/osproxy_win.cpp";"spl::priv::WinProxyProvider::getAutoProxyLegacy"
f7b15294:"Failed to inflate stream: %d","RootTools/roottools/auf/src/log2.cpp";"auf::CompressReader::read"
f7b36e45:"~unregisterListener: remove_AccessChanged handler (%s)","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::unregisterListener"
f7ee1fae:"Triggers updated, adding trigger %s","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::applyLogTriggers"
f81d0632:"appCapability.As failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::DisplayAccessHint"
f82690e1:"WinHttpGetProxyForUrlEx failed with error %u","RootTools/roottools/net/src/sysdeps/win/win_proxy_manager_v2.cpp";"`anonymous-namespace\'::WinProxyMananagerV2::ProxyListForURL"
f842f4eb:"MonitorOperation::initialize_v4only: failed to register route handle with reactor","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::initialize_v4only"
f878c672:"wakeupNetwork() type %d","RootTools/roottools/net/src/net_wakeup.cpp";"rtnet::NetworkTokenCache::wakeupNetwork"
f89774aa:"Getting WlanAccessAsyncResponse took %lld ms","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanAccessStatusAsyncOpHandler::Invoke"
f8af2262:"Engine count is not available, GPU adapter index = %d","RootTools/roottools/spl/src/sysdeps/win/sysinfo_gpu_win.cpp";"spl::priv::GPUInfo::ResetRuntimeMetrics"
f8f1f09b:"Reinit association on SCTP_CANT_STR_ASSOC, attempt %u, dropped %u","RootTools/roottools/sctp/src/sctp_assoc.cpp";"rtsctp::priv::SCTPAssoc::OnAssocChangeEvent"
f91f2c09:"STATE: S_SUSPENDED","RootTools/roottools/auf/src/background.cpp";"auf::internal::SuspensionManager::strandSuspended"
f931ee8b:"spl::pathCreateFromFixed(): Could not initialize tmp to %s","RootTools/roottools/spl/src/path_location.cpp";"spl::pathCreateFromFixed"
f93ea9bc:"Expected m_triggerFileOptions.get() != nullptr","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
f95df939:"Setup: no private key in provided X509Cert. %s","RootTools/roottools/net/src/sysdeps/openssl/dtls_v2_openssl.cpp";"`anonymous-namespace\'::DtlsEndpointOpenssl::Setup"
f972c244:"lldp.readPortId: index=%d","RootTools/roottools/spl/src/sysdeps/win/lldp.cpp";"spl::priv::LLDPreader::readPortId"
f9e4e000:"entry CapabilityListener()","RootTools/roottools/spl/src/sysdeps/win/app_capability.cpp";"spl::CapabilityListener::CapabilityListener"
f9f4f917:"model.dat not found","RootTools/roottools/auf/src/referenced_file.cpp";"`anonymous-namespace\'::ReferencedFile2::Read::<lambda_...>::operator ()"
fa1831f0:"generateResponse: no authentication header values provided or unsupported authentication method provided in prepareMethod","RootTools/roottools/net/src/http_auth.cpp";"rtnet::internal::AuthenticationHandlerImpl::generateResponse"
fa23c98e:"Start network connectivity listening","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::Strand_SetTimerAfter"
fa72b26b:"PersistentStorage save: %zu bytes","RootTools/roottools/spl/src/rt_persistent.cpp";"`anonymous-namespace\'::PersistentStorage::Flush"
fa97cb2d:"exit WlanCapabilityChecker","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::WlanCapabilityChecker::WlanCapabilityChecker"
fa98c2bf:"... (same contents as last displayed byte)\n","RootTools/roottools/spl/src/debug.cpp";"spl::internal::dumpMemory"
fb087efa:"Setup: Creating DTLS endpoint failed: %s","RootTools/roottools/sctp/src/dtls_pipe.cpp";"`anonymous-namespace\'::DTLSPipe::Setup"
fb15d68e:"Stop network connectivity listening","RootTools/roottools/httpstack/src/requestpool_impl.cpp";"http_stack::RequestPool::Strand_ProcessQueues"
fb1bd418:"SplOpaqueUpperLayerThread::start: Funky behaviour, thread already runs and still being asked to start()","RootTools/roottools/auf/src/thread_imp.cpp";"SplOpaqueUpperLayerThread::start"
fb89301e:"deferredLookup","RootTools/roottools/net/src/sysdeps/berkeley/resolver_bsd.cpp";"`anonymous-namespace\'::NameResolverOperation::deferredLookup"
fba9b212:"WindowsReactorImpl::registerHandle","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::registerHandle"
fbb2249c:"TimerImpStateMachine::endDispatch: Illegal state","RootTools/roottools/auf/src/thread_pool_timer_sm.cpp";"auf::TimerImpStateMachine::endDispatch"
fbe921ec:"Failed to open log file %s : %s","RootTools/roottools/auf/src/log2.cpp";"auf::FileLogAppender::open"
fc084946:"PII unsafe logs included in log buffer, log not dumped","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::dumpLogBuffer"
fc3fdb36:"WindowsReactorImpl::eventLoop","RootTools/roottools/auf/apal/sysdeps/win/reactor_msg.cpp";"apal::internal::WindowsReactorImpl::eventLoop"
fc60b62d:"RQ%u: Max redirects (%d) limit reached","RootTools/roottools/httpstack/src/request_impl.cpp";"http_stack::Request::DetectRedirect"
fca7df30:"#%02u\t%016I64x\t%ls!%s+0x%I64x\n","RootTools/roottools/spl/src/sysdeps/win/debug_win.cpp";"spl::priv::logStackBackTraceManualCore"
fcb87504:"WinSymmCryptoCngImpl::WinSymmCryptoCngImpl: no crypto algorithm provider\n","RootTools/roottools/spl/src/sysdeps/wincrypt/crypto_wincrypt_cng.cpp";"spl::internal::WinSymmCryptoCngImpl::WinSymmCryptoCngImpl"
fccd1c12:"RealtimeStandardTransport: allocMsgMem failed","RootTools/roottools/auf/src/transport/thread_default_transport.cpp";"auf::priv::RealtimeStandardTransport::allocMsgMem"
fcdf917d:"ONNX runtime is missing","RootTools/roottools/inference/inference_engine/src/onnx_dll_helper.cpp";"inference::onnx::DllHelper::DllHelper"
fd29a8bb:"ChainedGeoLocationOperation executed: %p","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::ReadGeoPositionAsync"
fd3705aa:"Unexpected security package chosen while using AuthenticationMethod::NEGOTIATE: %ls","RootTools/roottools/net/src/sysdeps/win/http_auth_sspi.cpp";"rtnet::internal::WinAuthProvider::generateResponse"
fd4778be:"Expected !m_logFiles.empty()","RootTools/roottools/auf/src/log2_config.cpp";"auf::LogConfig::readConfig"
fd66db6e:"Statics init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation_access.cpp";"spl::GeoLocatorImpl_RequestAccessAsync"
fd9941dd:"RQ%u: Body size is unknown","RootTools/roottools/httpstack/src/sysdeps/rt/http_response.cpp";"http_stack::skypert::HTTPResponse::analyzeHeaders"
fe13e91e:"RootTools build information:","RootTools/roottools/auf/src/auf.cpp";"auf::logInfo"
fe21aea2:"Unable to decrypt binary log","RootTools/roottools/auf/src/log2.cpp";"auf::DecryptReaderImpl::DecryptReaderImpl"
fe4974c7:"RQ%u: Reuse connection %s","RootTools/roottools/httpstack/src/sysdeps/rt/connection_pool.cpp";"http_stack::skypert::ConnectionPool::GetPromise"
fe50cc9b:"ResolverOperation::~dtor","RootTools/roottools/net/src/traceroute.cpp";"rtnet::internal::ResolverOperation::~ResolverOperation"
fe5f0d7a:"No file name for logging: %s","RootTools/roottools/auf/src/log2.cpp";"auf::FileLogAppender::init"
fe866871:"RQ%u: HTTP request sent","RootTools/roottools/httpstack/src/sysdeps/rt/requestop.cpp";"http_stack::skypert::RequestOp::HTTPRequestSent"
fe922d99:"Init failed","RootTools/roottools/spl/src/sysdeps/win/geolocation.cpp";"spl::realReadGeoPositionAsync"
fe97ff9a:"Unable to get security product, returning defaults hr=0x%08x, %ls","RootTools/roottools/net/src/sysdeps/win/sec_product_win.cpp";"rtnet::GetSecurityProductsState"
feb578fa:"resetLogLevels()","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::resetLogLevels"
feb7e075:"Summarizing object leaks...","RootTools/roottools/spl/include/rt/rt_object_registry.hpp";"rt::ObjectRegistry::stop"
fedc755e:"Worker is terminated, thread ID = %u","RootTools/roottools/auf/src/thread_pool_imp.cpp";"auf::Worker::~Worker"
fee79c11:"Unicast notification arrived: %d","RootTools/roottools/net/src/sysdeps/win/netinfo_win.cpp";"rtnet::internal::win::MonitorOperation::unicastChanged"
fee948e3:"RegisterFromIReferencedFile: cannot register model %s, since nullptr is provided","RootTools/roottools/inference/inference_engine/src/registry.cpp";"`anonymous-namespace\'::Registry::RegisterFromIReferencedFile"
feed4c9a:"Cannot save ecs.conf: %s","RootTools/roottools/spl/src/ecs.cpp";"`anonymous-namespace\'::EcsConfig::saveData"
ff714620:"dtlsCreate: private key check failed","RootTools/roottools/net/src/sysdeps/openssl/dtls_openssl.cpp";"rtnet::internal::DtlsBackendOpenssl::dtlsCreate"
ff885d77:"Flush from AsyncTraceThread ignored, since it would deadlock","RootTools/roottools/auf/src/log2.cpp";"auf::LogFactory::flush"
ff8c5dff:"DNS result IPv4: %s","RootTools/roottools/net/src/sysdeps/berkeley/happy_eyeballs_bsd_v3.cpp";"`anonymous-namespace\'::HappyEyeballsV3::IDnsResolveDelegate_address"
ffddead5:"Unable to parse model.json","RootTools/roottools/auf/src/referenced_file.cpp";"auf::priv::getMetadata"
