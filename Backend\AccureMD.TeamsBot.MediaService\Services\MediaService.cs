using AccureMD.TeamsBot.MediaService.Models;
using Microsoft.Identity.Client;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace AccureMD.TeamsBot.MediaService.Services;

public class MediaService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<MediaService> _logger;
    private readonly CallStateService _callStateService;

    public MediaService(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<MediaService> logger,
        CallStateService callStateService)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
        _callStateService = callStateService;
    }

    public async Task<MediaServiceResponse> JoinMeetingWithMediaAsync(string meetingUrl, string? tenantId = null, string? displayName = null)
    {
        try
        {
            _logger.LogInformation("Attempting to join meeting with media: {MeetingUrl}", meetingUrl);

            // Extract meeting information
            var extractedTenantId = ExtractTenantIdFromUrl(meetingUrl);
            // Organizer flow does not require joinMeetingId validation

            // Use provided tenant ID or extracted one
            var effectiveTenantId = tenantId ?? extractedTenantId ?? _configuration["Teams:TenantId"];

            if (string.IsNullOrEmpty(effectiveTenantId))
            {
                return new MediaServiceResponse
                {
                    Success = false,
                    Message = "Could not determine tenant ID"
                };
            }

            // Acquire access token
            var token = await AcquireTokenAsync(effectiveTenantId);
            if (string.IsNullOrEmpty(token))
            {
                return new MediaServiceResponse
                {
                    Success = false,
                    Message = "Failed to acquire access token"
                };
            }

            // Create the call (organizerMeetingInfo) — matches the approach that succeeded in your logs
            // First preference: joinWebUrlMeetingInfo to actually join the specific meeting
            var callResult = await CreateJoinWebUrlCallAsync(token, meetingUrl, displayName);

            // Fallback: organizerMeetingInfo if joinWebUrl fails (as seen successful previously)
            if (!callResult.Success)
            {
                _logger.LogWarning("joinWebUrl flow failed. Falling back to organizerMeetingInfo...");
                callResult = await CreateOrganizerCallAsync(token, meetingUrl, effectiveTenantId, displayName);
            }

            if (callResult.Success && !string.IsNullOrEmpty(callResult.CallId))
            {
                _callStateService.CreateCall(callResult.CallId, meetingUrl, effectiveTenantId);
            }

            return callResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting with media");
            return new MediaServiceResponse
            {
                Success = false,
                Message = $"Unexpected error: {ex.Message}"
            };
        }
    }

    private async Task<string?> AcquireTokenAsync(string tenantId)
    {
        try
        {
            var clientId = _configuration["Teams:AppId"];
            var clientSecret = _configuration["Teams:AppSecret"];

            if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret))
            {
                _logger.LogError("Missing Teams app configuration");
                return null;
            }

            var cca = ConfidentialClientApplicationBuilder
                .Create(clientId)
                .WithClientSecret(clientSecret)
                .WithAuthority($"https://login.microsoftonline.com/{tenantId}")
                .Build();

            var tokenResult = await cca
                .AcquireTokenForClient(new[] { "https://graph.microsoft.com/.default" })
                .ExecuteAsync();

            _logger.LogInformation("Successfully acquired token for tenant {TenantId}", tenantId);
            return tokenResult.AccessToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to acquire token for tenant {TenantId}", tenantId);
            return null;
        }
    }

    private async Task<MediaServiceResponse> CreateOrganizerCallAsync(
        string accessToken,
        string meetingUrl,
        string tenantId,
        string? displayName)
    {
        try
        {
            var client = _httpClientFactory.CreateClient();
            var baseUrl = _configuration["BaseUrl"];
            var callbackUri = $"{baseUrl}/api/calls/callback";

            var organizerOid = ExtractOidFromJoinUrl(meetingUrl);
            if (string.IsNullOrWhiteSpace(organizerOid))
            {
                return new MediaServiceResponse { Success = false, Message = "Organizer Oid not found in join URL context" };
            }

            var json = BuildOrganizerCallJson(
                callbackUri,
                subject: "AccureMD Media Join",
                organizerOid: organizerOid,
                tenantId: tenantId,
                displayName: displayName ?? "AccureMD Media Bot");
            _logger.LogInformation("Creating organizer call with payload: {Payload}", json);

            var request = new HttpRequestMessage(HttpMethod.Post, "https://graph.microsoft.com/v1.0/communications/calls");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.SendAsync(request);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var reqId = response.Headers.TryGetValues("request-id", out var reqIdVals) ? string.Join(",", reqIdVals) : null;
                var cliReqId = response.Headers.TryGetValues("client-request-id", out var cliReqVals) ? string.Join(",", cliReqVals) : null;
                _logger.LogError("Create organizer call failed. Status: {Status}, Reason={Reason}, RequestId={ReqId}, ClientRequestId={CliReqId}, Body: {Body}",
                    (int)response.StatusCode, response.ReasonPhrase, reqId, cliReqId, responseBody);
                return new MediaServiceResponse
                {
                    Success = false,
                    Message = $"Graph API call failed: {(int)response.StatusCode} {response.ReasonPhrase}. Body={responseBody}"
                };
            }

            var responseDoc = JsonDocument.Parse(responseBody);
            var callId = responseDoc.RootElement.GetProperty("id").GetString();

            _logger.LogInformation("Successfully created organizer call with ID: {CallId}", callId);

            return new MediaServiceResponse
            {
                Success = true,
                CallId = callId,
                Message = "Successfully joined meeting with media capabilities",
                JoinedAt = DateTime.UtcNow,
                MediaState = "Connecting"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating organizer call");
            return new MediaServiceResponse
            {
                Success = false,
                Message = $"Error creating call: {ex.Message}"
            };
        }
    }

    private string BuildOrganizerCallJson(string? callbackUri, string subject, string organizerOid, string tenantId, string displayName)
    {
        using var stream = new MemoryStream();
        using (var writer = new Utf8JsonWriter(stream))
        {
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.call");

            if (!string.IsNullOrEmpty(callbackUri))
                writer.WriteString("callbackUri", callbackUri);

            writer.WritePropertyName("requestedModalities");
            writer.WriteStartArray();
            writer.WriteStringValue("audio");
            writer.WriteEndArray();

            writer.WritePropertyName("mediaConfig");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.serviceHostedMediaConfig");
            writer.WriteEndObject();

            writer.WriteString("tenantId", tenantId);

            writer.WritePropertyName("meetingInfo");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.organizerMeetingInfo");
            writer.WritePropertyName("organizer");
            writer.WriteStartObject(); // identitySet
            writer.WritePropertyName("user");
            writer.WriteStartObject(); // identity
            writer.WriteString("@odata.type", "#microsoft.graph.identity");
            writer.WriteString("id", organizerOid);
            writer.WriteString("tenantId", tenantId);
            writer.WriteEndObject(); // identity
            writer.WriteEndObject(); // identitySet
            writer.WriteEndObject(); // meetingInfo

            writer.WritePropertyName("source");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.participantInfo");
            writer.WritePropertyName("identity");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.identitySet");
            writer.WritePropertyName("application");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.identity");
            writer.WriteString("displayName", displayName);
            writer.WriteString("id", _configuration["Teams:AppId"]);
            writer.WriteEndObject();
            writer.WriteEndObject();
            writer.WriteEndObject(); // source

            writer.WriteString("subject", subject);

            writer.WriteEndObject();
        }
        stream.Position = 0;
        return Encoding.UTF8.GetString(stream.ToArray());
    }

    private string? ExtractTenantIdFromUrl(string joinUrl)
    {
        // Prefer extracting Tid from the ?context payload like Backend does
        var contextMatch = Regex.Match(joinUrl, "[?&]context=([^&]+)");
        if (!contextMatch.Success) return null;
        var encoded = contextMatch.Groups[1].Value;
        try
        {
            var json = Uri.UnescapeDataString(encoded);
            using var doc = JsonDocument.Parse(json);
            if (doc.RootElement.TryGetProperty("Tid", out var tid))
                return tid.GetString();
        }
        catch { }
        return null;
    }

    private string? ExtractOidFromJoinUrl(string joinUrl)
    {
        var contextMatch = Regex.Match(joinUrl, "[?&]context=([^&]+)");
        if (!contextMatch.Success) return null;
        var encoded = contextMatch.Groups[1].Value;
        try
        {
            var json = Uri.UnescapeDataString(encoded);
            using var doc = JsonDocument.Parse(json);
            // Organizer identitySet shape
            if (doc.RootElement.TryGetProperty("Oid", out var oid))
                return oid.GetString();
        }
        catch { }
        return null;
    }

    private async Task<MediaServiceResponse> CreateJoinWebUrlCallAsync(string accessToken, string meetingUrl, string? displayName)
    {
        try
        {
            var client = _httpClientFactory.CreateClient();
            var baseUrl = _configuration["BaseUrl"];
            var callbackUri = $"{baseUrl}/api/calls/callback";

            var callPayload = new
            {
                odataType = "#microsoft.graph.call",
                callbackUri = callbackUri,
                requestedModalities = new[] { "audio" },
                mediaConfig = new { odataType = "#microsoft.graph.serviceHostedMediaConfig" },
                meetingInfo = new { odataType = "#microsoft.graph.joinWebUrlMeetingInfo", joinWebUrl = meetingUrl },
                source = new
                {
                    odataType = "#microsoft.graph.participantInfo",
                    identity = new
                    {
                        odataType = "#microsoft.graph.identitySet",
                        application = new { odataType = "#microsoft.graph.identity", displayName = displayName ?? "AccureMD Media Bot", id = _configuration["Teams:AppId"] }
                    }
                }
            };

            var json = JsonSerializer.Serialize(callPayload);
            var req = new HttpRequestMessage(HttpMethod.Post, "https://graph.microsoft.com/v1.0/communications/calls");
            req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            req.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var resp = await client.SendAsync(req);
            var body = await resp.Content.ReadAsStringAsync();
            if (!resp.IsSuccessStatusCode)
            {
                _logger.LogWarning("Fallback joinWebUrl failed. Status={Status}, Body={Body}", (int)resp.StatusCode, body);
                return new MediaServiceResponse { Success = false, Message = $"Graph API call failed: {(int)resp.StatusCode} {resp.ReasonPhrase}. Body={body}" };
            }

            using var doc = JsonDocument.Parse(body);
            var callId = doc.RootElement.TryGetProperty("id", out var id) ? id.GetString() : null;
            return new MediaServiceResponse { Success = true, CallId = callId, Message = "Joined via joinWebUrl", JoinedAt = DateTime.UtcNow, MediaState = "Connecting" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateJoinWebUrlCallAsync");
            return new MediaServiceResponse { Success = false, Message = ex.Message };
        }
    }
}
