using Microsoft.Graph;
using Microsoft.Identity.Client;
using Microsoft.EntityFrameworkCore;
using AccureMD.TeamsBot.Models;
using AccureMD.TeamsBot.Data;
using Azure.Identity;
using Azure.Core;

namespace AccureMD.TeamsBot.Services;

public class AuthenticationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthenticationService> _logger;
    private readonly ApplicationDbContext _dbContext;

    public AuthenticationService(IConfiguration configuration, ILogger<AuthenticationService> logger, ApplicationDbContext dbContext)
    {
        _configuration = configuration;
        _logger = logger;
        _dbContext = dbContext;
    }

    public async Task<AuthenticationResponse> InitiateAuthenticationAsync(string userId, string redirectUri)
    {
        try
        {
            _logger.LogInformation("Initiating authentication for user {UserId} with redirect URI: {RedirectUri}", userId, redirectUri);

            var appId = _configuration["Teams:AppId"];
            var appSecret = _configuration["Teams:AppSecret"];
            var tenantId = _configuration["Teams:TenantId"];

            _logger.LogInformation("Configuration check - AppId: {HasAppId}, AppSecret: {HasAppSecret}, TenantId: {HasTenantId}",
                !string.IsNullOrEmpty(appId), !string.IsNullOrEmpty(appSecret), !string.IsNullOrEmpty(tenantId));

            if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appSecret) || string.IsNullOrEmpty(tenantId))
            {
                _logger.LogError("Missing required configuration values for Teams authentication");
                throw new InvalidOperationException("Missing required configuration values for Teams authentication");
            }

            var authority = $"https://login.microsoftonline.com/{tenantId}";
            _logger.LogInformation("Building MSAL client with authority: {Authority}", authority);

            // Validate redirect URI format
            if (!Uri.TryCreate(redirectUri, UriKind.Absolute, out var redirectUriObj))
            {
                _logger.LogError("Invalid redirect URI format: {RedirectUri}", redirectUri);
                throw new ArgumentException($"Invalid redirect URI format: {redirectUri}");
            }

            _logger.LogInformation("Redirect URI validation - URI: {RedirectUri}, Scheme: {Scheme}, Host: {Host}",
                redirectUri, redirectUriObj.Scheme, redirectUriObj.Host);

            // Ensure redirect URI uses HTTPS in production (allow localhost for development)
            if (redirectUriObj.Scheme != "https" && !redirectUri.Contains("localhost") && !redirectUri.Contains("127.0.0.1"))
            {
                _logger.LogError("Redirect URI must use HTTPS in production: {RedirectUri}", redirectUri);
                throw new ArgumentException($"Redirect URI must use HTTPS in production: {redirectUri}");
            }

            try
            {
                var clientApp = ConfidentialClientApplicationBuilder
                    .Create(appId)
                    .WithClientSecret(appSecret)
                    .WithAuthority(authority)
                    .WithRedirectUri(redirectUri)
                    .Build();

                var scopes = new[]
                {
                    "https://graph.microsoft.com/User.Read",
                    "https://graph.microsoft.com/OnlineMeetings.ReadWrite",
                    "https://graph.microsoft.com/Calendars.Read"
                };

                _logger.LogInformation("Building auth URL with scopes: {Scopes}", string.Join(", ", scopes));

                var authUrl = await clientApp
                    .GetAuthorizationRequestUrl(scopes)
                    .WithExtraQueryParameters(new Dictionary<string, string> { { "state", userId } })
                    .ExecuteAsync();

                _logger.LogInformation("Generated auth URL for user {UserId}: {AuthUrl}", userId, authUrl.AbsoluteUri);

                return new AuthenticationResponse
                {
                    Success = true,
                    Message = "Authentication URL generated",
                    AuthUrl = authUrl.AbsoluteUri
                };
            }
            catch (Microsoft.Identity.Client.MsalException msalEx)
            {
                _logger.LogError(msalEx, "MSAL error during authentication initiation - Error: {Error}, ErrorCode: {ErrorCode}",
                    msalEx.Message, msalEx.ErrorCode);
                throw new InvalidOperationException($"MSAL authentication error: {msalEx.ErrorCode} - {msalEx.Message}", msalEx);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initiate authentication for user {UserId}", userId);
            return new AuthenticationResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<AuthenticationResponse> HandleAuthenticationCallbackAsync(string code, string state, string redirectUri)
    {
        try
        {
            _logger.LogInformation("Processing auth callback - Code: {CodePrefix}..., State: {State}, RedirectUri: {RedirectUri}",
                code?.Substring(0, Math.Min(10, code?.Length ?? 0)), state, redirectUri);

            var appId = _configuration["Teams:AppId"];
            var appSecret = _configuration["Teams:AppSecret"];
            var tenantId = _configuration["Teams:TenantId"];

            _logger.LogInformation("Using AppId: {AppId}, TenantId: {TenantId}", appId, tenantId);

            if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appSecret) || string.IsNullOrEmpty(tenantId))
            {
                _logger.LogError("Missing required configuration - AppId: {HasAppId}, AppSecret: {HasAppSecret}, TenantId: {HasTenantId}",
                    !string.IsNullOrEmpty(appId), !string.IsNullOrEmpty(appSecret), !string.IsNullOrEmpty(tenantId));
                throw new InvalidOperationException("Missing required configuration values for Teams authentication");
            }

            // Fallback to configured callback if redirectUri not provided
            if (string.IsNullOrWhiteSpace(redirectUri))
            {
                redirectUri = $"{_configuration["BaseUrl"]}/html/auth-callback.html";
                _logger.LogWarning("RedirectUri missing in callback request. Falling back to default: {RedirectUri}", redirectUri);
            }

            var clientApp = ConfidentialClientApplicationBuilder
                .Create(appId)
                .WithClientSecret(appSecret)
                .WithAuthority($"https://login.microsoftonline.com/{tenantId}")
                .WithRedirectUri(redirectUri)
                .Build();

            var scopes = new[]
            {
                "https://graph.microsoft.com/User.Read",
                "https://graph.microsoft.com/OnlineMeetings.ReadWrite",
                "https://graph.microsoft.com/Calendars.Read"
            };

            _logger.LogInformation("Attempting to acquire token with scopes: {Scopes}", string.Join(", ", scopes));

            var authResult = await clientApp
                .AcquireTokenByAuthorizationCode(scopes, code)
                .ExecuteAsync();

            _logger.LogInformation("Successfully acquired token. Token expires at: {ExpiresOn}", authResult.ExpiresOn);

            // Create a custom token credential that uses the user's access token
            var tokenCredential = new UserAccessTokenCredential(authResult.AccessToken);
            var graphClient = new GraphServiceClient(tokenCredential);

            _logger.LogInformation("Created GraphServiceClient, attempting to get user info");
            var user = await graphClient.Me.GetAsync();

            var authModel = new AuthenticationModel
            {
                UserId = state, // This contains the original userId
                UserName = user?.DisplayName ?? "",
                Email = user?.Mail ?? user?.UserPrincipalName ?? "",
                AccessToken = authResult.AccessToken,
                RefreshToken = authResult.Account?.HomeAccountId?.ToString() ?? "",
                TokenExpiry = authResult.ExpiresOn.UtcDateTime,
                IsAuthenticated = true,
                Permissions = scopes.ToArray()
            };

            // Store user session in database
            await SaveUserToDatabase(authModel);

            _logger.LogInformation("Successfully authenticated user {UserName}", authModel.UserName);

            return new AuthenticationResponse
            {
                Success = true,
                Message = "Authentication successful",
                User = authModel
            };
        }
        catch (MsalServiceException msalEx)
        {
            // This is the most important log. It will contain the REAL error from Microsoft.
            _logger.LogError(msalEx,
                "MSAL SERVICE ERROR - Code: {ErrorCode}, Status: {StatusCode}, CorrelationId: {CorrelationId}, Claims: {Claims}, Response Body: {ResponseBody}",
                msalEx.ErrorCode,
                msalEx.StatusCode,
                msalEx.CorrelationId,
                msalEx.Claims,
                msalEx.ResponseBody);

            return new AuthenticationResponse
            {
                Success = false,
                Message = $"Authentication failed: {msalEx.ErrorCode} - {msalEx.Message}"
            };
        }
        catch (MsalException msalEx)
        {
            // Other MSAL exceptions (client errors, parsing, etc.)
            _logger.LogError(msalEx, "MSAL ERROR - Code: {ErrorCode}", msalEx.ErrorCode);
            return new AuthenticationResponse
            {
                Success = false,
                Message = $"Authentication failed: {msalEx.ErrorCode} - {msalEx.Message}"
            };
        }
        catch (DbUpdateException dbEx)
        {
            var inner = dbEx.InnerException?.Message ?? dbEx.Message;
            _logger.LogError(dbEx, "Database error while handling auth callback. Inner: {Inner}", inner);
            return new AuthenticationResponse
            {
                Success = false,
                Message = $"Authentication failed due to database error. {inner}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle authentication callback");
            return new AuthenticationResponse
            {
                Success = false,
                Message = $"Authentication failed: {ex.Message}"
            };
        }
    }

    public async Task<AuthenticationResponse> VerifyAuthenticationAsync(string magicCode)
    {
        try
        {
            // This would typically verify against Microsoft Teams authentication
            // For now, we'll simulate a successful verification
            _logger.LogInformation($"Verifying magic code: {magicCode}");

            // Simulate async operation
            await Task.Delay(100);

            // In a real implementation, you would validate the magic code with Microsoft
            var isValid = !string.IsNullOrEmpty(magicCode);

            return new AuthenticationResponse
            {
                Success = isValid,
                Message = isValid ? "Verification successful" : "Invalid verification code"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify authentication");
            return new AuthenticationResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<AuthenticationResponse> EstablishSessionWithTokenAsync(string accessToken, string state)
    {
        try
        {
            _logger.LogInformation("Establishing session with access token - State: {State}", state);

            if (string.IsNullOrEmpty(accessToken))
            {
                return new AuthenticationResponse
                {
                    Success = false,
                    Message = "Access token is required"
                };
            }

            // Check if we're in development mode or if the token is a test token
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            var isTestToken = accessToken.StartsWith("test-token");
            // Enable development mode if: 1) Environment is Development, 2) Environment is null/empty, or 3) Token starts with "test-token"
            var isDevelopmentMode = environment == "Development" || string.IsNullOrEmpty(environment) || isTestToken;

            _logger.LogInformation("Development mode check - Environment: {Environment}, IsTestToken: {IsTestToken}, IsDevelopmentMode: {IsDevelopmentMode}",
                environment ?? "(null)", isTestToken, isDevelopmentMode);

            AuthenticationModel authModel;

            if (isDevelopmentMode)
            {
                _logger.LogInformation("Development mode detected - bypassing Microsoft Graph API validation");

                // Create authentication model with mock data for development/testing
                authModel = new AuthenticationModel
                {
                    UserId = state ?? "dev-user-" + DateTime.UtcNow.Ticks,
                    UserName = "Development User",
                    Email = "<EMAIL>",
                    AccessToken = accessToken,
                    RefreshToken = "", // Not available in implicit flow
                    TokenExpiry = DateTime.UtcNow.AddHours(1), // Default expiry
                    IsAuthenticated = true,
                    Permissions = new string[] { "User.Read" } // Basic permissions
                };

                _logger.LogInformation("Created development session for user {UserId} - {UserName}", authModel.UserId, authModel.UserName);
            }
            else
            {
                _logger.LogInformation("Production mode - validating token with Microsoft Graph API");

                // Use the access token to get user information from Microsoft Graph
                var tokenCredential = new UserAccessTokenCredential(accessToken);
                var graphClient = new GraphServiceClient(tokenCredential);

                var user = await graphClient.Me.GetAsync();
                if (user == null)
                {
                    return new AuthenticationResponse
                    {
                        Success = false,
                        Message = "Failed to retrieve user information"
                    };
                }

                _logger.LogInformation("Retrieved user info from Graph API - Name: {UserName}, Email: {Email}", user.DisplayName, user.Mail);

                // Create authentication model with real user data
                authModel = new AuthenticationModel
                {
                    UserId = state ?? user.Id ?? "unknown",
                    UserName = user.DisplayName ?? "",
                    Email = user.Mail ?? user.UserPrincipalName ?? "",
                    AccessToken = accessToken,
                    RefreshToken = "", // Not available in implicit flow
                    TokenExpiry = DateTime.UtcNow.AddHours(1), // Default expiry
                    IsAuthenticated = true,
                    Permissions = new string[] { "User.Read" } // Basic permissions
                };
            }

            // Store user session in database (works in both development and production)
            await SaveUserToDatabase(authModel);

            _logger.LogInformation("Successfully established session for user {UserId} - {UserName}", authModel.UserId, authModel.UserName);

            return new AuthenticationResponse
            {
                Success = true,
                Message = "Session established successfully",
                User = authModel
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to establish session with access token");
            return new AuthenticationResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<AuthenticationModel> GetAuthenticationStatusAsync(string userId)
    {
        _logger.LogInformation("GetAuthenticationStatusAsync: Checking status for user {UserId}", userId);

        // Check database for user session
        var authModel = await GetUserFromDatabase(userId);
        if (authModel != null)
        {
            _logger.LogInformation("GetAuthenticationStatusAsync: User {UserId} found in database. TokenExpiry={TokenExpiry}, Current time={CurrentTime}",
                userId, authModel.TokenExpiry, DateTime.UtcNow);

            // Check if token is still valid
            if (authModel.TokenExpiry > DateTime.UtcNow.AddMinutes(-5))
            {
                _logger.LogInformation("GetAuthenticationStatusAsync: Token is valid for user {UserId}. Returning authenticated status.", userId);
                return authModel;
            }
            else
            {
                _logger.LogWarning("GetAuthenticationStatusAsync: Token expired for user {UserId}. Marking as not authenticated.", userId);
                // Token expired, mark as not authenticated
                authModel.IsAuthenticated = false;

                // Defensive coding: Ensure the DateTime Kind is UTC before saving back to the database.
                if (authModel.TokenExpiry.HasValue)
                {
                    authModel.TokenExpiry = DateTime.SpecifyKind(authModel.TokenExpiry.Value, DateTimeKind.Utc);
                }

                await SaveUserToDatabase(authModel);
                return authModel;
            }
        }

        _logger.LogWarning("GetAuthenticationStatusAsync: User {UserId} not found in database. Returning default unauthenticated model.", userId);

        return new AuthenticationModel
        {
            UserId = userId,
            IsAuthenticated = false
        };
    }

    public async Task<bool> RefreshTokenAsync(string userId)
    {
        try
        {
            var authModel = await GetUserFromDatabase(userId);
            if (authModel == null || string.IsNullOrEmpty(authModel.RefreshToken))
                return false;

            _logger.LogInformation($"Attempting to refresh token for user {userId}");

            // Simulate async token refresh operation
            await Task.Delay(100);

            // Implement token refresh logic here
            // This would use the refresh token to get a new access token
            _logger.LogInformation($"Refreshing token for user {userId}");

            // Update token expiry
            authModel.TokenExpiry = DateTime.UtcNow.AddHours(1);

            // Save updated token to database
            await SaveUserToDatabase(authModel);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to refresh token for user {userId}");
            return false;
        }
    }

    public async Task<GraphServiceClient?> GetGraphClientForUserAsync(string userId)
    {
        var authModel = await GetAuthenticationStatusAsync(userId);

        if (!authModel.IsAuthenticated || string.IsNullOrEmpty(authModel.AccessToken))
        {
            _logger.LogWarning("User {UserId} is not authenticated or missing access token", userId);
            return null;
        }

        // Create Graph client using the stored user access token
        var tokenCredential = new UserAccessTokenCredential(authModel.AccessToken);
        return new GraphServiceClient(tokenCredential);
    }

    public async Task ClearUserSessionAsync(string userId)
    {
        try
        {
            var authModel = await GetUserFromDatabase(userId);
            if (authModel != null)
            {
                authModel.IsAuthenticated = false;
                authModel.AccessToken = "";
                authModel.RefreshToken = "";
                await SaveUserToDatabase(authModel);
            }
            _logger.LogInformation("Cleared session for user {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear session for user {UserId}", userId);
        }
    }

    // Database helper methods
    public async Task SaveUserToDatabase(AuthenticationModel authModel)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(authModel.UserId))
            {
                throw new ArgumentException("UserId is required to persist authentication");
            }

            _logger.LogInformation("SaveUserToDatabase: Starting save for UserId={UserId}, UserName={UserName}, Email={Email}, IsAuthenticated={IsAuthenticated}",
                authModel.UserId, authModel.UserName, authModel.Email, authModel.IsAuthenticated);

            // Try by primary key first
            var existingUser = await _dbContext.Users.FirstOrDefaultAsync(u => u.UserId == authModel.UserId);

            if (existingUser != null)
            {
                _logger.LogInformation("SaveUserToDatabase: Updating existing user {UserId}", authModel.UserId);

                // Update existing user
                existingUser.UserName = authModel.UserName;
                existingUser.Email = authModel.Email;
                existingUser.AccessToken = authModel.AccessToken;
                existingUser.RefreshToken = authModel.RefreshToken;
                existingUser.TokenExpiry = authModel.TokenExpiry;
                existingUser.IsAuthenticated = authModel.IsAuthenticated;
                existingUser.Permissions = authModel.Permissions;

                _dbContext.Users.Update(existingUser);
                _logger.LogInformation("SaveUserToDatabase: Updated user properties for {UserId}", authModel.UserId);
            }
            else
            {
                // If no record by UserId, check if we already have a record with the same email (unique index in DB)
                AuthenticationModel? existingByEmail = null;
                if (!string.IsNullOrWhiteSpace(authModel.Email))
                {
                    existingByEmail = await _dbContext.Users.FirstOrDefaultAsync(u => u.Email == authModel.Email);
                }

                if (existingByEmail != null)
                {
                    _logger.LogWarning("SaveUserToDatabase: Found existing record by Email for {Email}. Reconciling with new UserId {NewUserId} to avoid unique constraint violations.", authModel.Email, authModel.UserId);

                    // If the existing row has a different primary key, replace it with the new one to keep canonical mapping by Teams UserId
                    if (!string.Equals(existingByEmail.UserId, authModel.UserId, StringComparison.Ordinal))
                    {
                        // Remove the old row and add the new one atomically
                        using var tx = await _dbContext.Database.BeginTransactionAsync();
                        try
                        {
                            _dbContext.Users.Remove(existingByEmail);
                            await _dbContext.SaveChangesAsync();

                            _dbContext.Users.Add(authModel);
                            await _dbContext.SaveChangesAsync();

                            await tx.CommitAsync();
                            _logger.LogInformation("SaveUserToDatabase: Replaced existing email record with new UserId {UserId}", authModel.UserId);
                            return;
                        }
                        catch (Exception innerEx)
                        {
                            await tx.RollbackAsync();
                            _logger.LogError(innerEx, "SaveUserToDatabase: Failed while reconciling email conflict for {Email}", authModel.Email);
                            throw;
                        }
                    }
                    else
                    {
                        // Same key somehow, just ensure properties are up to date
                        existingByEmail.UserName = authModel.UserName;
                        existingByEmail.AccessToken = authModel.AccessToken;
                        existingByEmail.RefreshToken = authModel.RefreshToken;
                        existingByEmail.TokenExpiry = authModel.TokenExpiry;
                        existingByEmail.IsAuthenticated = authModel.IsAuthenticated;
                        existingByEmail.Permissions = authModel.Permissions;
                        _dbContext.Users.Update(existingByEmail);
                    }
                }
                else
                {
                    _logger.LogInformation("SaveUserToDatabase: Adding new user {UserId}", authModel.UserId);
                    _dbContext.Users.Add(authModel);
                }
            }

            var changeCount = await _dbContext.SaveChangesAsync();
            _logger.LogInformation("SaveUserToDatabase: Successfully saved user {UserId} to database. Changes: {ChangeCount}",
                authModel.UserId, changeCount);
        }
        catch (DbUpdateException dbEx)
        {
            // Surface more context for client and logs
            var innerMsg = dbEx.InnerException?.Message ?? dbEx.Message;
            _logger.LogError(dbEx, "SaveUserToDatabase: DbUpdateException while saving user {UserId}. Inner: {Inner}", authModel.UserId, innerMsg);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SaveUserToDatabase: Failed to save user {UserId} to database. UserName={UserName}, IsAuthenticated={IsAuthenticated}",
                authModel.UserId, authModel.UserName, authModel.IsAuthenticated);
            throw; // Re-throw to ensure calling code knows about the failure
        }
    }

    private async Task<AuthenticationModel?> GetUserFromDatabase(string userId)
    {
        try
        {
            _logger.LogInformation("GetUserFromDatabase: Looking up user {UserId}", userId);

            var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);

            if (user != null)
            {
                _logger.LogInformation("GetUserFromDatabase: Found user {UserId} - UserName={UserName}, IsAuthenticated={IsAuthenticated}, TokenExpiry={TokenExpiry}",
                    user.UserId, user.UserName, user.IsAuthenticated, user.TokenExpiry);
            }
            else
            {
                _logger.LogWarning("GetUserFromDatabase: User {UserId} not found in database", userId);
            }

            return user;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GetUserFromDatabase: Failed to get user {UserId} from database", userId);
            return null;
        }
    }
}

/// <summary>
/// Custom token credential that uses a user's access token for Microsoft Graph
/// </summary>
public class UserAccessTokenCredential : TokenCredential
{
    private readonly string _accessToken;

    public UserAccessTokenCredential(string accessToken)
    {
        _accessToken = accessToken ?? throw new ArgumentNullException(nameof(accessToken));
    }

    public override AccessToken GetToken(TokenRequestContext requestContext, CancellationToken cancellationToken)
    {
        // Return the access token with a far future expiry since we don't track the actual expiry here
        return new AccessToken(_accessToken, DateTimeOffset.UtcNow.AddHours(1));
    }

    public override ValueTask<AccessToken> GetTokenAsync(TokenRequestContext requestContext, CancellationToken cancellationToken)
    {
        return new ValueTask<AccessToken>(GetToken(requestContext, cancellationToken));
    }
}
