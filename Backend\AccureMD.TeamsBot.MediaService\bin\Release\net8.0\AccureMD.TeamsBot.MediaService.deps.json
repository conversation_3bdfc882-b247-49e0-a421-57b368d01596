{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AccureMD.TeamsBot.MediaService/1.0.0": {"dependencies": {"Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "Microsoft.Identity.Client": "4.64.1", "Swashbuckle.AspNetCore": "6.8.1", "System.Text.Json": "8.0.5"}, "runtime": {"AccureMD.TeamsBot.MediaService.dll": {}}}, "Microsoft.ApplicationInsights/2.22.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.1"}}, "Microsoft.Extensions.Configuration.Abstractions/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "2.1.1"}}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {}, "Microsoft.Extensions.Logging/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Options": "2.1.1"}}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {}, "Microsoft.Extensions.Logging.ApplicationInsights/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.Extensions.Options/2.1.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.1", "Microsoft.Extensions.Primitives": "2.1.1"}}, "Microsoft.Extensions.Primitives/2.1.1": {"dependencies": {"System.Memory": "4.5.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Identity.Client/4.64.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/6.8.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.8.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.8.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.8.1"}}, "Swashbuckle.AspNetCore.Swagger/6.8.1": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.8.1"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.1": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Memory/4.5.1": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Json/8.0.5": {}}}, "libraries": {"AccureMD.TeamsBot.MediaService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "path": "microsoft.applicationinsights/2.22.0", "hashPath": "microsoft.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LjVKO6P2y52c5ZhTLX/w8zc5H4Y3J/LJsgqTBj49TtFq/hAtVNue/WA0F6/7GMY90xhD7K0MDZ4qpOeWXbLvzg==", "path": "microsoft.extensions.configuration/2.1.1", "hashPath": "microsoft.extensions.configuration.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VfuZJNa0WUshZ/+8BFZAhwFKiKuu/qOUCFntfdLpHj7vcRnsGHqd3G2Hse78DM+pgozczGM63lGPRLmy+uhUOA==", "path": "microsoft.extensions.configuration.abstractions/2.1.1", "hashPath": "microsoft.extensions.configuration.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fcLCTS03poWE4v9tSNBr3pWn0QwGgAn1vzqHXlXgvqZeOc7LvQNzaWcKRQZTdEc3+YhQKwMsOtm3VKSA2aWQ8w==", "path": "microsoft.extensions.configuration.binder/2.1.1", "hashPath": "microsoft.extensions.configuration.binder.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MgYpU5cwZohUMKKg3sbPhvGG+eAZ/59E9UwPwlrUkyXU+PGzqwZg9yyQNjhxuAWmoNoFReoemeCku50prYSGzA==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.1.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-hh+mkOAQDTp6XH80xJt3+wwYVzkbwYQl9XZRCz4Um0JjP/o7N9vHM3rZ6wwwtr+BBe/L6iBO2sz0px6OWBzqZQ==", "path": "microsoft.extensions.logging/2.1.1", "hashPath": "microsoft.extensions.logging.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-XRzK7ZF+O6FzdfWrlFTi1Rgj2080ZDsd46vzOjadHUB0Cz5kOvDG8vI7caa5YFrsHQpcfn0DxtjS4E46N4FZsA==", "path": "microsoft.extensions.logging.abstractions/2.1.1", "hashPath": "microsoft.extensions.logging.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-5OmXub+9MyX8FbqgO+hBJRHk1iJ+UZUU20oIU3wo+RbmH6Jtsja79rriHLlzlrkMzWbpCkCzF6f4Yb6iGbsDag==", "path": "microsoft.extensions.logging.applicationinsights/2.22.0", "hashPath": "microsoft.extensions.logging.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.Extensions.Options/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-V7lXCU78lAbzaulCGFKojcCyG8RTJicEbiBkPJjFqiqXwndEBBIehdXRMWEVU3UtzQ1yDvphiWUL9th6/4gJ7w==", "path": "microsoft.extensions.options/2.1.1", "hashPath": "microsoft.extensions.options.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-scJ1GZNIxMmjpENh0UZ8XCQ6vzr/LzeF9WvEA51Ix2OQGAs9WPgPu8ABVUdvpKPLuor/t05gm6menJK3PwqOXg==", "path": "microsoft.extensions.primitives/2.1.1", "hashPath": "microsoft.extensions.primitives.2.1.1.nupkg.sha512"}, "Microsoft.Identity.Client/4.64.1": {"type": "package", "serviceable": true, "sha512": "sha512-xiPBlQ/aRc1MRTOyTonQF9mwFfGQc/zDWEG1zlBopOubCXKpUWg91QeQNYKYnn/PGUnv1ivDyb84vuu5DsTlpw==", "path": "microsoft.identity.client/4.64.1", "hashPath": "microsoft.identity.client.4.64.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-JN6ccH37QKtNOwBrvSxc+jBYIB+cw6RlZie2IKoJhjjf6HzBH+2kPJCpxmJ5EHIqmxvq6aQG+0A8XklGx9rAxA==", "path": "swashbuckle.aspnetcore/6.8.1", "hashPath": "swashbuckle.aspnetcore.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-eOkdM4bsWBU5Ty3kWbyq5O9L+05kZT0vOdGh4a92vIb/LLQGQTPLRHXuJdnUBNIPNC8XfKWfSbtRfqzI6nnbqw==", "path": "swashbuckle.aspnetcore.swagger/6.8.1", "hashPath": "swashbuckle.aspnetcore.swagger.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-TjBPxsN0HeJzxEXZYeDXBNNMSyhg+TYXtkbwX+Cn8GH/y5ZeoB/chw0p71kRo5tR2sNshbKwL24T6f9pTF9PHg==", "path": "swashbuckle.aspnetcore.swaggergen/6.8.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-lpEszYJ7vZaTTE5Dp8MrsbSHrgDfjhDMjzW1qOA1Xs1Dnj3ZRBJAcPZUTsa5Bva+nLaw91JJ8OI8FkSg8hhIyA==", "path": "swashbuckle.aspnetcore.swaggerui/6.8.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.8.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Memory/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-sDJYJpGtTgx+23Ayu5euxG5mAXWdkDb4+b0rD0Cab0M1oQS9H0HXGPriKcqpXuiJDTV7fTp/d+fMDJmnr6sNvA==", "path": "system.memory/4.5.1", "hashPath": "system.memory.4.5.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}}}