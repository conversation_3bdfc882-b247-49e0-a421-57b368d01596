namespace AccureMD.TeamsBot.MediaService.Models;

public class CallState
{
    public string CallId { get; set; } = string.Empty;
    public string MeetingUrl { get; set; } = string.Empty;
    public string? TenantId { get; set; }
    public CallStatus Status { get; set; } = CallStatus.Connecting;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? EstablishedAt { get; set; }
    public DateTime? EndedAt { get; set; }
    public List<string> Participants { get; set; } = new();
    public Dictionary<string, MediaStreamInfo> MediaStreams { get; set; } = new();
    public List<CallbackEvent> Events { get; set; } = new();
}

public enum CallStatus
{
    Connecting,
    Established,
    MediaActive,
    Ended,
    Failed
}

public class MediaStreamInfo
{
    public string ParticipantId { get; set; } = string.Empty;
    public string MediaType { get; set; } = string.Empty; // "audio", "video"
    public bool IsActive { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }
}

public class CallbackEvent
{
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string EventType { get; set; } = string.Empty;
    public string RawData { get; set; } = string.Empty;
    public bool Processed { get; set; }
}
