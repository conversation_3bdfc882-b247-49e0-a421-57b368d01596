using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Graph.Communications.Client;
using Microsoft.Graph.Communications.Calls.Media;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Graph.Communications.Common;
using Microsoft.Skype.Bots.Media;

namespace AccureMD.MediaBot.Worker
{
    public class CommunicationsHost
    {
        public ICommunicationsClient Client { get; private set; }

        public void Initialize()
        {
            var appId = Get("AppId");
            var appSecret = Get("AppSecret");
            var tenantId = Get("TenantId");
            var serviceFqdn = Get("ServiceFqdn");
            var certificateThumbprint = Get("CertificateThumbprint");
            var internalPort = int.Parse(Get("InternalPort") ?? "8445");
            var publicPort = int.Parse(Get("PublicPort") ?? "8445");
            var botBaseUrl = Get("BotBaseUrl").TrimEnd('/') + "/mediabot";

            var logger = new GraphLogger(typeof(CommunicationsHost).Assembly.GetName().Name);
            var authProvider = new Authentication.MsalApplicationAuthProvider(appId, appSecret, tenantId, logger);

            var name = typeof(CommunicationsHost).Assembly.GetName().Name;
            var builder = new CommunicationsClientBuilder(name, appId, logger);
            builder.SetAuthenticationProvider(authProvider);
            builder.SetNotificationUrl(new Uri(botBaseUrl + "/notifications"));
            // Important: include API version in base URL (v1.0 or beta)
            builder.SetServiceBaseUrl(new Uri("https://graph.microsoft.com/v1.0"));

            var mediaSettings = new MediaPlatformSettings
            {
                ApplicationId = appId,
                MediaPlatformInstanceSettings = new MediaPlatformInstanceSettings
                {
                    CertificateThumbprint = certificateThumbprint,
                    InstanceInternalPort = internalPort,
                    InstancePublicIPAddress = IPAddress.Any,
                    InstancePublicPort = publicPort,
                    ServiceFqdn = serviceFqdn
                }
            };
            builder.SetMediaPlatformSettings(mediaSettings);

            Client = builder.Build();
        }

        private static string Get(string key) => System.Configuration.ConfigurationManager.AppSettings[key];
    }
}

