using System;
using System.Threading.Tasks;
using Microsoft.Graph.Communications.Client.Authentication;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Identity.Client;
using System.Net.Http;
using System.Net.Http.Headers;

namespace AccureMD.MediaBot.Worker.Authentication
{
    // MSAL-based auth provider implementing IRequestAuthenticationProvider for Graph Comms SDK
    public class MsalApplicationAuthProvider : Microsoft.Graph.Communications.Client.Authentication.IRequestAuthenticationProvider
    {
        private readonly string _appId;
        private readonly string _appSecret;
        private readonly string _tenantId;
        private readonly IConfidentialClientApplication _msalApp;
        private readonly IGraphLogger _logger;

        public MsalApplicationAuthProvider(string appId, string appSecret, string tenantId, IGraphLogger logger)
        {
            _appId = appId ?? throw new ArgumentNullException(nameof(appId));
            _appSecret = appSecret ?? throw new ArgumentNullException(nameof(appSecret));
            _tenantId = tenantId ?? throw new ArgumentNullException(nameof(tenantId));
            _logger = logger;

            _msalApp = ConfidentialClientApplicationBuilder
                .Create(_appId)
                .WithClientSecret(_appSecret)
                .WithAuthority($"https://login.microsoftonline.com/{_tenantId}")
                .Build();
        }

        // Stamp outbound requests with a Bearer token
        public async Task AuthenticateOutboundRequestAsync(HttpRequestMessage request, string tenant)
        {
            var effectiveTenant = !string.IsNullOrWhiteSpace(tenant) ? tenant : (TenantContext.CurrentTenant ?? _tenantId);
            var token = await AcquireTokenAsync(effectiveTenant);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }

        // For now, accept inbound notifications (you can harden this by validating tokens against OIDC config)
        public Task<Microsoft.Graph.Communications.Client.Authentication.RequestValidationResult> ValidateInboundRequestAsync(HttpRequestMessage request)
        {
            return Task.FromResult(new Microsoft.Graph.Communications.Client.Authentication.RequestValidationResult { IsValid = true, TenantId = _tenantId });
        }

        private async Task<string> AcquireTokenAsync(string tenant)
        {
            var authority = string.IsNullOrWhiteSpace(tenant) ? $"https://login.microsoftonline.com/{_tenantId}" : $"https://login.microsoftonline.com/{tenant}";
            var app = ConfidentialClientApplicationBuilder.Create(_appId)
                .WithClientSecret(_appSecret)
                .WithAuthority(authority)
                .Build();
            var tr = await app.AcquireTokenForClient(new[] { "https://graph.microsoft.com/.default" }).ExecuteAsync();
            return tr.AccessToken;
        }
    }
}

