# Complete Windows VM Deployment Guide for AccureMD Teams Bot Media Streaming

## Step 1: Create New .NET 8 Project for Media Service

### 1.1 Create Project Structure
```bash
mkdir AccureMD.TeamsBot.MediaService
cd AccureMD.TeamsBot.MediaService
dotnet new webapi --framework net8.0
```

### 1.2 Required NuGet Packages
Add to your `.csproj` file:

```xml
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Graph.Communications.Calls" Version="1.31.0.225-preview" />
    <PackageReference Include="Microsoft.Graph.Communications.Calls.Media" Version="1.31.0.225-preview" />
    <PackageReference Include="Microsoft.Skype.Bots.Media" Version="1.31.0.225-preview" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.64.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8" />
    <PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.22.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.1" />
  </ItemGroup>
</Project>
```

## Step 2: Create Controllers

### 2.1 MediaCallbackController.cs
```csharp
using Microsoft.AspNetCore.Mvc;
using AccureMD.TeamsBot.Services;

namespace AccureMD.TeamsBot.Controllers;

[ApiController]
[Route("api/media")]
public class MediaCallbackController : ControllerBase
{
    private readonly ILogger<MediaCallbackController> _logger;
    private readonly CallStateService _callStateService;
    private readonly MediaGraphCommunicationsService _mediaService;

    public MediaCallbackController(
        ILogger<MediaCallbackController> logger,
        CallStateService callStateService,
        MediaGraphCommunicationsService mediaService)
    {
        _logger = logger;
        _callStateService = callStateService;
        _mediaService = mediaService;
    }

    [HttpPost("callback")]
    public async Task<IActionResult> CallbackAsync([FromBody] JsonElement body)
    {
        try
        {
            var bodyText = body.GetRawText();
            _logger.LogInformation("Received Graph callback: {Body}", bodyText);
            
            // Record callback for tracking
            _callStateService.RecordCallbackBody(bodyText);
            
            // Process the callback (handle call state changes, media events, etc.)
            await ProcessCallbackAsync(bodyText);
            
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Graph callback");
            return StatusCode(500);
        }
    }

    [HttpPost("join")]
    public async Task<IActionResult> JoinMeetingAsync([FromBody] JoinMeetingRequest request)
    {
        try
        {
            _logger.LogInformation("Joining meeting: {Url}", request.MeetingUrl);
            
            var result = await _mediaService.JoinMeetingWithMediaAsync(
                request.MeetingUrl, 
                request.TenantId);
            
            if (result.Success)
            {
                return Ok(new { success = true, callId = result.CallId, message = result.Message });
            }
            
            return BadRequest(new { success = false, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting");
            return StatusCode(500, new { success = false, message = ex.Message });
        }
    }

    private async Task ProcessCallbackAsync(string callbackBody)
    {
        // Handle different types of callbacks (call state changes, media events, etc.)
        // This is where you'd process incoming call notifications from Microsoft Graph
        await Task.CompletedTask; // Placeholder for actual implementation
    }
}

public class JoinMeetingRequest
{
    public string MeetingUrl { get; set; } = "";
    public string? TenantId { get; set; }
}
```

## Step 3: Configuration Files

### 3.1 appsettings.json (Use placeholders as requested)
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  
  "Media": {
    "AppId": "YOUR_BOT_APP_ID_HERE",
    "AppSecret": "YOUR_BOT_APP_SECRET_HERE",
    "TenantId": "YOUR_TENANT_ID_HERE",
    "ServiceFqdn": "accuremd-media.eastus.cloudapp.azure.com",
    "PublicIPAddress": "YOUR_VM_PUBLIC_IP_HERE",
    "InstanceInternalPort": "8445",
    "InstancePublicPort": "20000",
    "CertificateThumbprint": "YOUR_SSL_CERTIFICATE_THUMBPRINT_HERE"
  },
  
  "ASR": {
    "ApiUrl": "YOUR_ASR_SERVICE_API_URL_HERE",
    "ApiKey": "YOUR_ASR_API_KEY_HERE",
    "Language": "en-US"
  },
  
  "BotUI": {
    "TranscriptionWebhookUrl": "https://accuremd.azurewebsites.net/api/transcription/realtime"
  },
  
  "ConnectionStrings": {
    "DefaultConnection": "YOUR_DATABASE_CONNECTION_STRING_HERE",
    "TranscriptionDb": "YOUR_TRANSCRIPTION_DATABASE_CONNECTION_STRING_HERE"
  },
  
  "ApplicationInsights": {
    "ConnectionString": "YOUR_APP_INSIGHTS_CONNECTION_STRING_HERE"
  }
}
```

### 3.2 appsettings.Production.json
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning",
      "AccureMD": "Information"
    }
  },
  
  "Media": {
    "ServiceFqdn": "accuremd-media-prod.eastus.cloudapp.azure.com"
  }
}
```

## Step 4: Startup.cs / Program.cs Configuration

### 4.1 Program.cs
```csharp
using AccureMD.TeamsBot.Services;
using AccureMD.TeamsBot.Data;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddControllers();
builder.Services.AddHttpClient();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add Entity Framework
builder.Services.AddDbContext<TranscriptionDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("TranscriptionDb")));

// Add custom services
builder.Services.AddSingleton<CallStateService>();
builder.Services.AddScoped<MediaGraphCommunicationsService>();
builder.Services.AddScoped<MediaStreamingService>();
builder.Services.AddScoped<TranscriptionService>();

// Add Application Insights
builder.Services.AddApplicationInsightsTelemetry(builder.Configuration["ApplicationInsights:ConnectionString"]);

// Add CORS for your Linux App Service
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins("https://accuremd.azurewebsites.net")
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors();
app.UseAuthorization();
app.MapControllers();

// Initialize media service on startup
using (var scope = app.Services.CreateScope())
{
    var mediaService = scope.ServiceProvider.GetRequiredService<MediaGraphCommunicationsService>();
    await mediaService.InitializeAsync();
    
    // Run database migrations
    var dbContext = scope.ServiceProvider.GetRequiredService<TranscriptionDbContext>();
    await dbContext.Database.MigrateAsync();
}

app.Run();
```

## Step 5: Windows VM Setup Script

### 5.1 setup-windows-vm.ps1
```powershell
# AccureMD Teams Bot Media Service - Windows VM Setup Script

Write-Host "Setting up Windows VM for AccureMD Teams Bot Media Service..." -ForegroundColor Green

# Variables (Update these with your values)
$resourceGroupName = "accuremd-rg"
$vmName = "accuremd-media-vm"
$location = "East US"
$vmSize = "Standard_D4s_v3"  # 4 vCPU, 16GB RAM - recommended for media processing
$adminUsername = "accuremdadmin"
$dnsLabel = "accuremd-media"

# Create Resource Group if it doesn't exist
Write-Host "Creating resource group..." -ForegroundColor Yellow
az group create --name $resourceGroupName --location $location

# Create Virtual Network
Write-Host "Creating virtual network..." -ForegroundColor Yellow
az network vnet create `
    --resource-group $resourceGroupName `
    --name accuremd-vnet `
    --address-prefix 10.0.0.0/16 `
    --subnet-name default `
    --subnet-prefix 10.0.0.0/24

# Create Network Security Group with required ports
Write-Host "Creating network security group..." -ForegroundColor Yellow
az network nsg create --resource-group $resourceGroupName --name accuremd-media-nsg

# Add security rules for Teams calling bot
az network nsg rule create `
    --resource-group $resourceGroupName `
    --nsg-name accuremd-media-nsg `
    --name AllowHTTPS `
    --protocol tcp `
    --priority 1000 `
    --destination-port-range 443 `
    --access allow

az network nsg rule create `
    --resource-group $resourceGroupName `
    --nsg-name accuremd-media-nsg `
    --name AllowMediaSignaling `
    --protocol tcp `
    --priority 1001 `
    --destination-port-range 8445 `
    --access allow

az network nsg rule create `
    --resource-group $resourceGroupName `
    --nsg-name accuremd-media-nsg `
    --name AllowMediaUDP `
    --protocol udp `
    --priority 1002 `
    --destination-port-range 20000-21000 `
    --access allow

az network nsg rule create `
    --resource-group $resourceGroupName `
    --nsg-name accuremd-media-nsg `
    --name AllowRDP `
    --protocol tcp `
    --priority 1003 `
    --destination-port-range 3389 `
    --access allow

# Create Public IP with static allocation
Write-Host "Creating public IP..." -ForegroundColor Yellow
az network public-ip create `
    --resource-group $resourceGroupName `
    --name accuremd-media-pip `
    --dns-name $dnsLabel `
    --allocation-method Static `
    --sku Standard

# Create VM
Write-Host "Creating Windows VM..." -ForegroundColor Yellow
az vm create `
    --resource-group $resourceGroupName `
    --name $vmName `
    --image Win2019Datacenter `
    --size $vmSize `
    --admin-username $adminUsername `
    --generate-ssh-keys `
    --public-ip-address accuremd-media-pip `
    --nsg accuremd-media-nsg `
    --vnet-name accuremd-vnet `
    --subnet default

# Get the public IP for reference
$publicIP = az network public-ip show --resource-group $resourceGroupName --name accuremd-media-pip --query ipAddress --output tsv

Write-Host "VM created successfully!" -ForegroundColor Green
Write-Host "Public IP: $publicIP" -ForegroundColor Cyan
Write-Host "FQDN: $dnsLabel.$location.cloudapp.azure.com" -ForegroundColor Cyan
Write-Host "Please update your appsettings.json with these values:" -ForegroundColor Yellow
Write-Host "  Media:PublicIPAddress = $publicIP" -ForegroundColor White
Write-Host "  Media:ServiceFqdn = $dnsLabel.$location.cloudapp.azure.com" -ForegroundColor White
```

## Step 6: Windows VM Software Installation

### 6.1 install-software.ps1 (Run on Windows VM)
```powershell
# Run this script on the Windows VM after RDP connection

Write-Host "Installing required software on Windows VM..." -ForegroundColor Green

# Install Chocolatey
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install .NET 8 Runtime and SDK
Write-Host "Installing .NET 8..." -ForegroundColor Yellow
choco install dotnet-8.0-runtime -y
choco install dotnet-8.0-sdk -y

# Install IIS and ASP.NET Core Hosting Bundle
Write-Host "Installing IIS and ASP.NET Core Hosting Bundle..." -ForegroundColor Yellow
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-HttpErrors, IIS-HttpLogging, IIS-RequestFiltering, IIS-StaticContent, IIS-DefaultDocument, IIS-DirectoryBrowsing, IIS-ASPNET45, IIS-NetFxExtensibility45, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-NetFxExtensibility, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-ASPNET, IIS-NetFxExtensibility, IIS-ISAPIExtensions, IIS-ISAPIFilter

# Download and install ASP.NET Core Hosting Bundle
$hostingBundleUrl = "https://download.visualstudio.microsoft.com/download/pr/9c4b8dfc-c9b5-4b24-9a99-ae0a5bb51b8a/9c6b80b839bb45db6f1aa7cee8b21676/dotnet-hosting-8.0.8-win.exe"
Invoke-WebRequest -Uri $hostingBundleUrl -OutFile "dotnet-hosting-bundle.exe"
Start-Process -FilePath ".\dotnet-hosting-bundle.exe" -ArgumentList "/quiet" -Wait

# Install Git for deployment
choco install git -y

# Install Visual C++ Redistributables (required for media processing)
choco install vcredist2022 -y

Write-Host "Software installation completed!" -ForegroundColor Green
Write-Host "Please reboot the VM and then proceed with SSL certificate setup." -ForegroundColor Yellow
```

## Step 7: SSL Certificate Setup

### 7.1 setup-ssl-certificate.ps1
```powershell
# SSL Certificate Setup for AccureMD Media Service

Write-Host "Setting up SSL Certificate..." -ForegroundColor Green

# Option 1: Self-signed certificate for development/testing
Write-Host "Creating self-signed certificate..." -ForegroundColor Yellow
$cert = New-SelfSignedCertificate -DnsName "accuremd-media.eastus.cloudapp.azure.com" -CertStoreLocation "cert:\LocalMachine\My" -NotAfter (Get-Date).AddYears(1)

# Export certificate thumbprint (needed for configuration)
$thumbprint = $cert.Thumbprint
Write-Host "Certificate Thumbprint: $thumbprint" -ForegroundColor Cyan
Write-Host "Update your appsettings.json: Media:CertificateThumbprint = $thumbprint" -ForegroundColor Yellow

# Bind certificate to HTTPS port 443
Import-Module WebAdministration
New-WebBinding -Name "Default Web Site" -Protocol https -Port 443 -SslFlags 0
$binding = Get-WebBinding -Name "Default Web Site" -Protocol https
$binding.AddSslCertificate($thumbprint, "my")

Write-Host "SSL Certificate configured!" -ForegroundColor Green
Write-Host "For production, please replace with a proper SSL certificate from a trusted CA." -ForegroundColor Yellow
```

## Step 8: Database Setup

### 8.1 Create Database Migration
Run these commands in your project directory:

```bash
# Add Entity Framework migration
dotnet ef migrations add InitialCreate

# Update database (run this on your production database)
dotnet ef database update
```

### 8.2 Database Connection String Example
```
Server=YOUR_SQL_SERVER.database.windows.net;Database=AccureMDTranscriptions;User Id=YOUR_USERNAME;Password=YOUR_PASSWORD;Encrypt=true;TrustServerCertificate=false;
```

## Step 9: Deployment Script

### 9.1 deploy-to-vm.ps1
```powershell
# Deployment script for AccureMD Media Service

param(
    [Parameter(Mandatory=$true)]
    [string]$VMAddress,
    [Parameter(Mandatory=$true)]
    [string]$Username,
    [Parameter(Mandatory=$true)]
    [string]$Password
)

Write-Host "Deploying AccureMD Media Service..." -ForegroundColor Green

# Build the application
Write-Host "Building application..." -ForegroundColor Yellow
dotnet publish -c Release -o ./publish

# Create deployment package
Compress-Archive -Path ./publish/* -DestinationPath deployment.zip -Force

# Copy to VM (you'll need to set up file sharing or use other transfer method)
Write-Host "Copying files to VM..." -ForegroundColor Yellow
# Use your preferred method (SCP, file share, etc.)

Write-Host "Deployment package ready: deployment.zip" -ForegroundColor Green
Write-Host "Please copy this file to your Windows VM and extract to C:\inetpub\wwwroot\AccureMDMediaService" -ForegroundColor Yellow
```

## Step 10: Integration with Your Existing Linux App Service

### 10.1 Add to your existing bot service (Linux App Service)
```csharp
// Add this to your existing bot controllers
public class TranscriptionController : ControllerBase
{
    [HttpPost("api/transcription/realtime")]
    public async Task<IActionResult> ReceiveTranscription([FromBody] TranscriptionWebhookPayload payload)
    {
        // Process real-time transcription from Windows VM
        // Update your bot UI, send to users, etc.
        
        return Ok();
    }
}

public class TranscriptionWebhookPayload
{
    public string CallId { get; set; } = "";
    public string Text { get; set; } = "";
    public double Confidence { get; set; }
    public DateTimeOffset Timestamp { get; set; }
    public string Language { get; set; } = "";
}
```

## Step 11: Monitoring and Logging

### 11.1 Add Application Insights configuration
Your Windows VM service will send logs and metrics to Application Insights, which you can monitor alongside your Linux App Service.

## Step 12: Testing the Setup

### 12.1 Health Check Endpoint
```csharp
[ApiController]
[Route("api/health")]
public class HealthController : ControllerBase
{
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
    }
}
```

## Deployment Checklist

- [ ] Windows VM created with required ports open
- [ ] .NET 8 and ASP.NET Core Hosting Bundle installed
- [ ] SSL certificate configured
- [ ] Database connection string updated
- [ ] All placeholder configuration values filled in
- [ ] Application deployed to IIS
- [ ] Health check endpoint accessible
- [ ] Integration with Linux App Service tested
- [ ] Transcription flow working end-to-end

## Architecture Overview

```
Teams Meeting → Windows VM (Media Service) → ASR API → Database
                      ↓
              Linux App Service (Bot UI) ← Real-time transcriptions
```

This setup gives you:
- **Windows VM**: Handles real-time media streaming and transcription
- **Linux App Service**: Continues to handle bot messaging and UI
- **Database**: Stores all transcriptions
- **Real-time Integration**: Immediate transcription updates to bot interface

The Windows VM service will resolve your subcode 2203 error and provide the media streaming capabilities you need for real-time transcription.