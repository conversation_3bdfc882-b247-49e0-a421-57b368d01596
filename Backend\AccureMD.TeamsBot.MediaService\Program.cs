using AccureMD.TeamsBot.MediaService.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddHttpClient();

// Add Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "AccureMD Teams Media Service",
        Version = "v1",
        Description = "Media streaming service for Teams meeting integration"
    });
});

// Add logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// Register custom services
builder.Services.AddSingleton<CallStateService>();
builder.Services.AddScoped<MediaService>();
builder.Services.AddScoped<CallbackProcessingService>();
builder.Services.AddScoped<AudioProcessingService>();
builder.Services.AddSingleton<MediaPlatformService>();

// Add CORS for development
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "AccureMD Teams Media Service v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}

app.UseHttpsRedirection();
app.UseCors();
app.UseRouting();

app.MapControllers();

// Initialize Media Platform on startup
var mediaPlatformService = app.Services.GetRequiredService<MediaPlatformService>();
var logger = app.Services.GetRequiredService<ILogger<Program>>();

try
{
    logger.LogInformation("Initializing Media Platform...");
    var initialized = await mediaPlatformService.InitializeAsync();

    if (initialized)
    {
        var started = await mediaPlatformService.StartAsync();
        if (started)
        {
            logger.LogInformation("Media Platform started successfully");
        }
        else
        {
            logger.LogWarning("Media Platform failed to start");
        }
    }
    else
    {
        logger.LogWarning("Media Platform failed to initialize");
    }
}
catch (Exception ex)
{
    logger.LogError(ex, "Error during Media Platform initialization");
}

// Graceful shutdown
var lifetime = app.Services.GetRequiredService<IHostApplicationLifetime>();
lifetime.ApplicationStopping.Register(async () =>
{
    logger.LogInformation("Application is shutting down...");
    try
    {
        await mediaPlatformService.StopAsync();
        mediaPlatformService.Dispose();
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error during shutdown");
    }
});

logger.LogInformation("AccureMD Teams Media Service starting...");
app.Run();
