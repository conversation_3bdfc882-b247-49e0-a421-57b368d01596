<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>AccureMD.TeamsBot</AssemblyName>
    <RootNamespace>AccureMD.TeamsBot</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Bot.Builder" Version="4.21.2" />
    <PackageReference Include="Microsoft.Bot.Builder.Integration.AspNet.Core" Version="4.21.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0" />
    <PackageReference Include="Microsoft.Graph" Version="5.89.0" />
    <PackageReference Include="Azure.Identity" Version="1.13.1" />
    <!-- Removed Microsoft.Graph.Authentication -->
    <PackageReference Include="Microsoft.Identity.Client" Version="4.74.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.18.0" />
    <PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.34.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="TeamsAppManifest\**" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <!-- Exclude the MediaService and MediaBot.Worker project files from this web app build -->
  <ItemGroup>
    <!-- Exclude MediaService -->
    <Compile Remove="AccureMD.TeamsBot.MediaService\**\*.cs" />
    <Content Remove="AccureMD.TeamsBot.MediaService\**\*" />
    <EmbeddedResource Remove="AccureMD.TeamsBot.MediaService\**\*" />
    <None Remove="AccureMD.TeamsBot.MediaService\**\*" />

    <!-- Exclude MediaBot Worker (classic .NET Framework) -->
    <Compile Remove="AccureMD.MediaBot.Worker\**\*.cs" />
    <Content Remove="AccureMD.MediaBot.Worker\**\*" />
    <EmbeddedResource Remove="AccureMD.MediaBot.Worker\**\*" />
    <None Remove="AccureMD.MediaBot.Worker\**\*" />
  </ItemGroup>

</Project>