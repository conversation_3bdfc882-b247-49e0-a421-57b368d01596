@AccureMD.TeamsBot.MediaService_HostAddress = https://localhost:7042

### Health Check
GET {{AccureMD.TeamsBot.MediaService_HostAddress}}/api/meeting/health

###

### Join Meeting
POST {{AccureMD.TeamsBot.MediaService_HostAddress}}/api/meeting/join
Content-Type: application/json

{
  "meetingUrl": "https://teams.microsoft.com/l/meetup-join/19%3ameeting_example%40thread.v2/0?context=%7b%22Tid%22%3a%22tenant-id%22%2c%22Oid%22%3a%22organizer-id%22%7d",
  "tenantId": "653f2dfa-0545-4c9f-be75-7c35da274877",
  "displayName": "AccureMD Media Bot"
}

###

### Get Active Calls
GET {{AccureMD.TeamsBot.MediaService_HostAddress}}/api/meeting/calls/active

###

### Get Call State (replace {callId} with actual call ID)
GET {{AccureMD.TeamsBot.MediaService_HostAddress}}/api/meeting/call/example-call-id/state

###

### Test Callback Validation (for webhook setup)
GET {{AccureMD.TeamsBot.MediaService_HostAddress}}/api/calls/callback?validationToken=test-validation-token

###

### Test Callback Processing (simulate Graph notification)
POST {{AccureMD.TeamsBot.MediaService_HostAddress}}/api/calls/callback
Content-Type: application/json

{
  "changeType": "updated",
  "resourceData": {
    "id": "example-call-id",
    "state": "established",
    "participants": [
      {
        "info": {
          "identity": {
            "user": {
              "id": "user-id-1",
              "displayName": "Test User"
            }
          }
        },
        "isInLobby": false,
        "isMuted": false
      }
    ],
    "mediaState": {
      "audio": "active"
    }
  }
}

###
