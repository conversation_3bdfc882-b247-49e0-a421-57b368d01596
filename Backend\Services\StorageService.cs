using AccureMD.TeamsBot.Models;
using AccureMD.TeamsBot.Data;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace AccureMD.TeamsBot.Services;

public class StorageService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<StorageService> _logger;
    private readonly ApplicationDbContext _dbContext;

    public StorageService(IConfiguration configuration, ILogger<StorageService> logger, ApplicationDbContext dbContext)
    {
        _configuration = configuration;
        _logger = logger;
        _dbContext = dbContext;
    }

    public async Task<bool> SaveRecordingMetadataAsync(MeetingModel meeting)
    {
        try
        {
            var existingMeeting = await _dbContext.Meetings.FirstOrDefaultAsync(m => m.Id == meeting.Id);

            if (existingMeeting != null)
            {
                // Update existing meeting
                existingMeeting.RecordingPath = meeting.RecordingPath;
                existingMeeting.IsRecording = meeting.IsRecording;
                existingMeeting.Status = meeting.Status;
                existingMeeting.EndTime = meeting.EndTime;

                _dbContext.Meetings.Update(existingMeeting);
            }
            else
            {
                // Add new meeting
                _dbContext.Meetings.Add(meeting);
            }

            await _dbContext.SaveChangesAsync();
            _logger.LogInformation("Recording metadata saved to database for meeting {MeetingId}", meeting.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save recording metadata for meeting {MeetingId}", meeting.Id);
            return false;
        }
    }

    public async Task<bool> SaveTranscriptAsync(TranscriptModel transcript)
    {
        try
        {
            _dbContext.Transcripts.Add(transcript);
            await _dbContext.SaveChangesAsync();
            _logger.LogInformation("Transcript saved to database for meeting {MeetingId}", transcript.MeetingId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save transcript for meeting {MeetingId}", transcript.MeetingId);
            return false;
        }
    }

    public async Task<bool> SaveTranscriptsAsync(IEnumerable<TranscriptModel> transcripts)
    {
        try
        {
            _dbContext.Transcripts.AddRange(transcripts);
            await _dbContext.SaveChangesAsync();
            _logger.LogInformation("Batch of {Count} transcripts saved to database", transcripts.Count());
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save batch of transcripts");
            return false;
        }
    }

    public async Task<List<MeetingModel>> GetUserMeetingsAsync(string userId)
    {
        try
        {
            var meetings = await _dbContext.Meetings
                .Where(m => m.OrganizerId == userId)
                .Include(m => m.Transcripts)
                .OrderByDescending(m => m.StartTime)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} meetings for user {UserId}", meetings.Count, userId);
            return meetings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get meetings for user {UserId}", userId);
            return new List<MeetingModel>();
        }
    }

    public async Task<MeetingModel?> GetMeetingAsync(string meetingId)
    {
        try
        {
            var meeting = await _dbContext.Meetings
                .Include(m => m.Transcripts)
                .Include(m => m.Organizer)
                .FirstOrDefaultAsync(m => m.Id == meetingId);

            return meeting;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get meeting {MeetingId}", meetingId);
            return null;
        }
    }

    public async Task<List<TranscriptModel>> GetMeetingTranscriptsAsync(string meetingId)
    {
        try
        {
            var transcripts = await _dbContext.Transcripts
                .Where(t => t.MeetingId == meetingId)
                .OrderBy(t => t.Timestamp)
                .ToListAsync();

            return transcripts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get transcripts for meeting {MeetingId}", meetingId);
            return new List<TranscriptModel>();
        }
    }

    public async Task<bool> DeleteMeetingDataAsync(string meetingId)
    {
        try
        {
            var meeting = await _dbContext.Meetings
                .Include(m => m.Transcripts)
                .FirstOrDefaultAsync(m => m.Id == meetingId);

            if (meeting != null)
            {
                // Delete transcripts first (due to foreign key constraint)
                _dbContext.Transcripts.RemoveRange(meeting.Transcripts);

                // Delete meeting
                _dbContext.Meetings.Remove(meeting);

                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Meeting data deleted for meeting {MeetingId}", meetingId);
                return true;
            }
            else
            {
                _logger.LogWarning("Meeting {MeetingId} not found for deletion", meetingId);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete meeting data for {MeetingId}", meetingId);
            return false;
        }
    }

    private async Task<bool> SaveToLocalStorageAsync(string type, dynamic data)
    {
        try
        {
            var storageDir = Path.Combine("./storage", type);
            if (!Directory.Exists(storageDir))
            {
                Directory.CreateDirectory(storageDir);
            }

            var fileName = $"{type}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json";
            var filePath = Path.Combine(storageDir, fileName);

            var json = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(filePath, json);

            _logger.LogInformation($"Data saved to local storage: {filePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save to local storage");
            return false;
        }
    }
}