using System.Web.Http;
using Owin;

namespace AccureMD.MediaBot.Worker
{
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            var config = new HttpConfiguration();

            // Routes
            config.MapHttpAttributeRoutes();
            config.Routes.MapHttpRoute(
                name: "Default<PERSON><PERSON>",
                routeTemplate: "mediabot/{controller}/{action}",
                defaults: new { action = RouteParameter.Optional }
            );

            // DI singletons (simplified)
            var host = new CommunicationsHost();
            host.Initialize();
            config.Properties[typeof(CommunicationsHost)] = host;

            app.UseWebApi(config);
        }
    }
}

