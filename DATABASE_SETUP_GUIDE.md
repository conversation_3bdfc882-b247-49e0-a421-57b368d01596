# Database Setup Guide for AccureMD Teams Bot

## Overview
This guide explains how to set up and configure the PostgreSQL database for the AccureMD Teams Bot application.

## Database Schema
The application uses the following tables that match your provided schema:

### Users Table
```sql
CREATE TABLE users (
    user_id TEXT PRIMARY KEY,
    user_name TEXT,
    email TEXT UNIQUE,
    access_token TEXT,
    refresh_token TEXT,
    token_expiry TIMESTAMP,
    is_authenticated <PERSON><PERSON><PERSON>EA<PERSON>,
    permissions TEXT[]
);
```

### Meetings Table
```sql
CREATE TABLE meetings (
    id TEXT PRIMARY KEY,
    meeting_url TEXT,
    title TEXT,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status TEXT,
    organizer_id TEXT REFERENCES users(user_id),
    participants TEXT[],
    recording_path TEXT,
    transcript_path TEXT,
    is_recording BOOLEAN,
    is_transcribing BOOLEAN
);
```

### Transcripts Table
```sql
CREATE TABLE transcripts (
    id TEXT PRIMARY KEY,
    meeting_id TEXT REFERENCES meetings(id),
    "timestamp" TIMESTAMP,
    speaker_id TEXT,
    speaker_name TEXT,
    text TEXT,
    confidence DOUBLE PRECISION,
    offset_from_start INTERVAL
);
```

### Indexes
```sql
CREATE INDEX idx_meetings_organizer_id ON meetings(organizer_id);
CREATE INDEX idx_transcripts_meeting_id ON transcripts(meeting_id);
```

## Configuration

### Connection String
The connection string is already configured in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "postgresql://postgres:[YOUR-PASSWORD]@db.msnqzbbgpsyxjmywllkb.supabase.co:5432/postgres"
  }
}
```

## Entity Framework Models

### AuthenticationModel (Users)
- Maps to `users` table
- Stores user authentication data, tokens, and permissions
- Includes navigation property for organized meetings

### MeetingModel (Meetings)
- Maps to `meetings` table
- Stores meeting metadata, recording status, and participant information
- Includes navigation properties for organizer and transcripts

### TranscriptModel (Transcripts)
- Maps to `transcripts` table
- Stores individual transcript entries with timing and speaker information
- Includes navigation property for the associated meeting

## Database Services

### DatabaseInitializationService
- Ensures database exists and is accessible
- Provides connection testing functionality
- Logs database statistics

### StorageService
- Handles CRUD operations for meetings and transcripts
- Provides methods for saving/retrieving user meetings
- Manages transcript batch operations

### AuthenticationService
- Stores and retrieves user authentication data
- Manages user sessions with database persistence
- Handles token refresh and validation

## API Endpoints for Testing

### Database Connection Test
```
GET /api/database/test-connection
```
Returns database connection status and table counts.

### Schema Information
```
GET /api/database/schema-info
```
Returns information about table existence.

### Manual Database Initialization
```
POST /api/database/initialize
```
Manually triggers database initialization.

## Setup Steps

1. **Verify Connection String**: Ensure the PostgreSQL connection string in `appsettings.json` is correct.

2. **Run the Application**: The database will be automatically initialized on startup.

3. **Test Connection**: Visit `/api/database/test-connection` to verify the database is working.

4. **Check Schema**: Visit `/api/database/schema-info` to verify tables exist.

## Data Flow

### User Authentication
1. User authenticates via Teams
2. `AuthenticationService` creates/updates user record in database
3. User session cached in memory for performance
4. Database serves as persistent storage

### Meeting Management
1. User joins meeting via Teams bot
2. `MeetingService` creates meeting record in database
3. Meeting metadata stored including participants and status
4. Active meetings cached for real-time operations

### Transcript Storage
1. Real-time transcription generates transcript entries
2. `TranscriptionService` creates transcript records
3. Transcripts linked to meetings via foreign key
4. Batch operations supported for performance

## Performance Considerations

### Caching Strategy
- User sessions cached in memory for quick access
- Active meetings cached for real-time operations
- Database used for persistence and cross-session data

### Indexing
- Foreign key indexes created for optimal query performance
- Email unique constraint for user lookup
- Meeting organizer index for user meeting queries

### Connection Management
- Entity Framework handles connection pooling
- Scoped services ensure proper connection lifecycle
- Database context disposed automatically

## Troubleshooting

### Connection Issues
1. Check connection string format
2. Verify database server accessibility
3. Ensure credentials are correct
4. Check firewall settings

### Schema Issues
1. Verify tables exist using schema-info endpoint
2. Check for proper foreign key relationships
3. Ensure indexes are created

### Performance Issues
1. Monitor database connection pool
2. Check query execution plans
3. Verify proper indexing usage

## Monitoring

### Logging
- Database operations logged at Information level
- Errors logged with full exception details
- Connection status logged on startup

### Health Checks
- Database connection tested on startup
- Test endpoints available for monitoring
- Statistics provided for operational insight
