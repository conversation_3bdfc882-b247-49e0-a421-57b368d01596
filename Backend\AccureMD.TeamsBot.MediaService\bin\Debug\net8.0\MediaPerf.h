
//
// This file contains generated symbol names used by lodctr.exe.
//
#pragma once

#define PERF_OPERATIONS 0
#define PERF_OPERATIONS_GLOBAL_HEALTH 2
#define PERF_OPERATIONS_TCPPACKETIZER_OUT_OF_SYNC 4
#define PERF_OPERATIONS_FAILED_ALLOCATIONS 6
#define PERF_OPERATIONS_NUM_PACKETS_DROPPED_BY_SRTP 8

#define PERF_PLANNING 10
#define PERF_PLANNING_NUM_CONF_STARTED 12
#define PERF_PLANNING_NUM_AUDCHANNEL_STARTED 14
#define PERF_PLANNING_NUM_VIDCHANNEL_STARTED 16
#define PERF_PLANNING_NUM_DATCHANNEL_STARTED 18
#define PERF_PLANNING_NUM_PARTICIPANT_STARTED 20
#define PERF_PLANNING_NUM_PARENT_PARTICIPANT_STARTED 22
#define PERF_PLANNING_NUM_PARENT_AUDCHANNEL_STARTED 24
#define PERF_PLANNING_NUM_PARENT_VIDCHANNEL_STARTED 26
#define PERF_PLANNING_NUM_PARENT_DATCHANNEL_STARTED 28
#define PERF_PLANNING_NUM_CHILD_PARTICIPANT_STARTED 30
#define PERF_PLANNING_NUM_CHILD_AUDCHANNEL_STARTED 32
#define PERF_PLANNING_NUM_CHILD_VIDCHANNEL_STARTED 34
#define PERF_PLANNING_NUM_CHILD_DATCHANNEL_STARTED 36
#define PERF_PLANNING_NUM_CONFS_HEALTH_NORMAL 38
#define PERF_PLANNING_NUM_CONFS_HEALTH_OVERLOADED 40
#define PERF_PLANNING_NUM_PACKETS_DROPPED 42
#define PERF_PLANNING_FAILED_CONNECTIVITY_CHECKS 44
#define PERF_PLANNING_NUM_DATA_CONNECTION_DROPPED 46
#define PERF_PLANNING_NUM_CONFERENCE_PROCESSING_DELAYS 48

#define PERF_INFORMATIONAL 50
#define PERF_INFORMATIONAL_NUM_SEND_AUDCHANNEL_STARTED 52
#define PERF_INFORMATIONAL_NUM_RECV_AUDCHANNEL_STARTED 54
#define PERF_INFORMATIONAL_NUM_BOTH_AUDCHANNEL_STARTED 56
#define PERF_INFORMATIONAL_NUM_SEND_VIDCHANNEL_STARTED 58
#define PERF_INFORMATIONAL_NUM_RECV_VIDCHANNEL_STARTED 60
#define PERF_INFORMATIONAL_NUM_BOTH_VIDCHANNEL_STARTED 62
#define PERF_INFORMATIONAL_AUDIOROUTER_TIMESLICE_TIME 64
#define PERF_INFORMATIONAL_AUDIOROUTER_TIMESLICE_TIME_BASE 66
#define PERF_INFORMATIONAL_STREAMENG_TIMERTICKS 68
#define PERF_INFORMATIONAL_CONF_PROCESSING_RATE 70
#define PERF_INFORMATIONAL_AVERAGE_ICE_ADDRESS_BINDING_TIME 72
#define PERF_INFORMATIONAL_AVERAGE_ICE_ADDRESS_BINDING_TIME_BASE 74
#define PERF_INFORMATIONAL_AVERAGE_ICE_CONNECTIVITY_CHECK_TIME 76
#define PERF_INFORMATIONAL_AVERAGE_ICE_CONNECTIVITY_CHECK_TIME_BASE 78
#define PERF_INFORMATIONAL_RECV_AUDIOPACKETS_FROM_TRANSPORT 80
#define PERF_INFORMATIONAL_RECV_AUDIOPACKETS_INTO_ENGINE 82
#define PERF_INFORMATIONAL_RECV_AUDIOPACKETS_FROM_ENGINE 84
#define PERF_INFORMATIONAL_SEND_AUDIOPACKETS_INTO_ENGINE 86
#define PERF_INFORMATIONAL_AUDIOROUTER_INPUT_BUFFERS 88
#define PERF_INFORMATIONAL_AUDIOROUTER_OUTPUT_BUFFERS 90
#define PERF_INFORMATIONAL_SEND_AUDIOPACKETS_INTO_RTP 92
#define PERF_INFORMATIONAL_SEND_AUDIOPACKETS_INTO_TRANSPORT 94
#define PERF_INFORMATIONAL_AUDIO_FEC_PACKETS 96
#define PERF_INFORMATIONAL_RECV_VIDEOPACKETS_FROM_TRANSPORT 98
#define PERF_INFORMATIONAL_RECV_VIDEOPACKETS_INTO_ENGINE 100
#define PERF_INFORMATIONAL_RECV_VIDEOPACKETS_FROM_ENGINE 102
#define PERF_INFORMATIONAL_SEND_VIDEOPACKETS_FROM_ENGINE 104
#define PERF_INFORMATIONAL_SEND_VIDEOPACKETS_INTO_RTP 106
#define PERF_INFORMATIONAL_SEND_VIDEOPACKETS_INTO_TRANSPORT 108
#define PERF_INFORMATIONAL_VIDSWITCHER_INPUTFRAMES 110
#define PERF_INFORMATIONAL_VIDSWITCHER_OUTPUTFRAMES 112
#define PERF_INFORMATIONAL_RECV_DATAPACKETS_FROM_TRANSPORT 114
#define PERF_INFORMATIONAL_SEND_DATAPACKETS_TO_TRANSPORT 116
#define PERF_INFORMATIONAL_NUM_DATA_CHANNEL_STALLED 118
#define PERF_INFORMATIONAL_NUM_DATA_CHANNEL_TRANSPORT_STALLED 120
#define PERF_INFORMATIONAL_PACKETS_FROM_TCPPACKETIZER 122
#define PERF_INFORMATIONAL_RECEIVE_IO_SUCCEEDED 124
#define PERF_INFORMATIONAL_SEND_IO_SUCCEEDED 126
#define PERF_INFORMATIONAL_RECEIVE_IO_FAILED 128
#define PERF_INFORMATIONAL_SEND_IO_FAILED 130
#define PERF_INFORMATIONAL_RTCPPACKETS_RECEIVED 132
#define PERF_INFORMATIONAL_RTCPPACKETS_SENT 134
#define PERF_INFORMATIONAL_TOTAL_NUM_MEMORYPOOL_ALLOC 136
#define PERF_INFORMATIONAL_TOTAL_NUM_MEMORYPOOL_FREE 138
#define PERF_INFORMATIONAL_TOTAL_NUM_CBufferTransportIOContext 140
#define PERF_INFORMATIONAL_TOTAL_NUM_CBufferStream 142
#define PERF_INFORMATIONAL_TOTAL_NUM_ALLOC_PORT_COLLISION 144
#define PERF_INFORMATIONAL_TOTAL_NUM_MEDIA_TIMEOUTS 146
#define PERF_INFORMATIONAL_VIDSWITCHER_RATEMATCHEDFRAMES 148
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_QVGA 150
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_VGA 152
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_HD 154
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_PANO 156
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_QVGA 158
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_VGA 160
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_HD 162
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_PANO 164
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_VC1 166
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_VC1 168
#define PERF_INFORMATIONAL_AUDIO_IN_BANDWIDTH 170
#define PERF_INFORMATIONAL_AUDIO_OUT_BANDWIDTH 172
#define PERF_INFORMATIONAL_VIDEO_IN_BANDWIDTH 174
#define PERF_INFORMATIONAL_VIDEO_OUT_BANDWIDTH 176
#define PERF_INFORMATIONAL_DATA_IN_BANDWIDTH 178
#define PERF_INFORMATIONAL_DATA_OUT_BANDWIDTH 180
#define PERF_INFORMATIONAL_PARENT_AUDIO_IN_BANDWIDTH 182
#define PERF_INFORMATIONAL_PARENT_AUDIO_OUT_BANDWIDTH 184
#define PERF_INFORMATIONAL_PARENT_VIDEO_IN_BANDWIDTH 186
#define PERF_INFORMATIONAL_PARENT_VIDEO_OUT_BANDWIDTH 188
#define PERF_INFORMATIONAL_PARENT_DATA_IN_BANDWIDTH 190
#define PERF_INFORMATIONAL_PARENT_DATA_OUT_BANDWIDTH 192
#define PERF_INFORMATIONAL_CHILD_AUDIO_IN_BANDWIDTH 194
#define PERF_INFORMATIONAL_CHILD_AUDIO_OUT_BANDWIDTH 196
#define PERF_INFORMATIONAL_CHILD_VIDEO_IN_BANDWIDTH 198
#define PERF_INFORMATIONAL_CHILD_VIDEO_OUT_BANDWIDTH 200
#define PERF_INFORMATIONAL_CHILD_DATA_IN_BANDWIDTH 202
#define PERF_INFORMATIONAL_CHILD_DATA_OUT_BANDWIDTH 204
#define PERF_INFORMATIONAL_AUDIO_SIREN_ENCODES 206
#define PERF_INFORMATIONAL_AUDIO_SIREN_DECODES 208
#define PERF_INFORMATIONAL_AUDIO_SILK_ENCODES 210
#define PERF_INFORMATIONAL_AUDIO_SILK_DECODES 212
#define PERF_INFORMATIONAL_AUDIO_SATIN_LR_ENCODES 214
#define PERF_INFORMATIONAL_AUDIO_SATIN_LR_DECODES 216
#define PERF_INFORMATIONAL_AUDIO_SATIN_HR_ENCODES 218
#define PERF_INFORMATIONAL_AUDIO_SATIN_HR_DECODES 220
#define PERF_INFORMATIONAL_AUDIO_OTHERS_ENCODES 222
#define PERF_INFORMATIONAL_AUDIO_OTHERS_DECODES 224
#define PERF_INFORMATIONAL_AUDIO_G722_ENCODES 226
#define PERF_INFORMATIONAL_AUDIO_G722_DECODES 228
#define PERF_INFORMATIONAL_NUM_RTP_PACKETS_DROPPED 230
#define PERF_INFORMATIONAL_VIDEO_TICK_COUNT 232
#define PERF_INFORMATIONAL_SHAPER_FLUSH_COUNT 234
#define PERF_INFORMATIONAL_VIDEO_FRAME_THROUGHPUT 236
#define PERF_INFORMATIONAL_SHAPER_OVERFLOW_DROP_BUFFER_COUNT 238

#define PERF_PRIVATE 240
#define PERF_PRIVATE_AUDIO_BUFFER_HITS 242
#define PERF_PRIVATE_AUDIOROUTER_TIMESLICE 244
#define PERF_PRIVATE_CONF_PROCESSED 246
#define PERF_PRIVATE_AVERAGE_CONF_PROCESSING_TIME 248
#define PERF_PRIVATE_AVERAGE_CONF_PROCESSING_TIME_BASE 250
#define PERF_PRIVATE_AVERAGE_TRANSPORT_PROCESSING_TIME 252
#define PERF_PRIVATE_AVERAGE_TRANSPORT_PROCESSING_TIME_BASE 254
#define PERF_PRIVATE_NUM_SEND_20MS_ACHANNELS 256
#define PERF_PRIVATE_NUM_SEND_40MS_ACHANNELS 258
#define PERF_PRIVATE_NUM_SEND_60MS_ACHANNELS 260
#define PERF_PRIVATE_NUM_SEND_100MS_ACHANNELS 262
#define PERF_PRIVATE_NUM_SEND_200MS_ACHANNELS 264
#define PERF_PRIVATE_NUM_RECV_20MS_ACHANNELS 266
#define PERF_PRIVATE_NUM_RECV_40MS_ACHANNELS 268
#define PERF_PRIVATE_NUM_RECV_60MS_ACHANNELS 270
#define PERF_PRIVATE_NUM_RECV_100MS_ACHANNELS 272
#define PERF_PRIVATE_NUM_RECV_200MS_ACHANNELS 274
#define PERF_PRIVATE_AVERAGE_SIREN_ENCODE_TIME 276
#define PERF_PRIVATE_AVERAGE_SIREN_ENCODE_TIME_BASE 278
#define PERF_PRIVATE_AVERAGE_SIREN_DECODE_TIME 280
#define PERF_PRIVATE_AVERAGE_SIREN_DECODE_TIME_BASE 282
#define PERF_PRIVATE_AVERAGE_SILK_ENCODE_TIME 284
#define PERF_PRIVATE_AVERAGE_SILK_ENCODE_TIME_BASE 286
#define PERF_PRIVATE_AVERAGE_SILK_DECODE_TIME 288
#define PERF_PRIVATE_AVERAGE_SILK_DECODE_TIME_BASE 290
#define PERF_PRIVATE_AVERAGE_SRTP_ENCRYPT_TIME_LARGE 292
#define PERF_PRIVATE_AVERAGE_SRTP_ENCRYPT_TIME_LARGE_BASE 294
#define PERF_PRIVATE_AVERAGE_SRTP_ENCRYPT_TIME_SMALL 296
#define PERF_PRIVATE_AVERAGE_SRTP_ENCRYPT_TIME_SMALL_BASE 298
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_INC_TIME_LARGE 300
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_INC_TIME_LARGE_BASE 302
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_INC_TIME_SMALL 304
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_INC_TIME_SMALL_BASE 306
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_FINAL_TIME_LARGE 308
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_FINAL_TIME_LARGE_BASE 310
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_FINAL_TIME_SMALL 312
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_FINAL_TIME_SMALL_BASE 314
#define PERF_PRIVATE_AVERAGE_SRTP_DECRYPT_TIME_LARGE 316
#define PERF_PRIVATE_AVERAGE_SRTP_DECRYPT_TIME_LARGE_BASE 318
#define PERF_PRIVATE_AVERAGE_SRTP_DECRYPT_TIME_SMALL 320
#define PERF_PRIVATE_AVERAGE_SRTP_DECRYPT_TIME_SMALL_BASE 322
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_CHECK_TIME_LARGE 324
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_CHECK_TIME_LARGE_BASE 326
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_CHECK_TIME_SMALL 328
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_CHECK_TIME_SMALL_BASE 330
#define PERF_PRIVATE_TRANSPORT_SENDDROP 332
#define PERF_PRIVATE_AVERAGE_SENDTIME 334
#define PERF_PRIVATE_AVERAGE_SENDTIME_BASE 336
#define PERF_PRIVATE_AVERAGE_AUDIO_CROSSBAR_TIME 338
#define PERF_PRIVATE_AVERAGE_AUDIO_CROSSBAR_TIME_BASE 340
#define PERF_PRIVATE_AVERAGE_VIDEO_CROSSBAR_TIME 342
#define PERF_PRIVATE_AVERAGE_VIDEO_CROSSBAR_TIME_BASE 344
#define PERF_PRIVATE_AVERAGE_CHANNEL_CROSSBAR_TIME 346
#define PERF_PRIVATE_AVERAGE_CHANNEL_CROSSBAR_TIME_BASE 348
#define PERF_PRIVATE_COUNTER1 350
#define PERF_PRIVATE_COUNTER2 352
#define PERF_PRIVATE_COUNTER3 354
#define PERF_PRIVATE_COUNTER4 356
#define PERF_PRIVATE_NUM_VALUE1 358
#define PERF_PRIVATE_NUM_VALUE2 360
#define PERF_PRIVATE_NUM_VALUE3 362
#define PERF_PRIVATE_NUM_VALUE4 364
#define PERF_PRIVATE_AVERAGE_VALUE1 366
#define PERF_PRIVATE_AVERAGE_VALUE1_BASE 368
#define PERF_PRIVATE_AVERAGE_VALUE2 370
#define PERF_PRIVATE_AVERAGE_VALUE2_BASE 372
#define PERF_PRIVATE_AVERAGE_VALUE3 374
#define PERF_PRIVATE_AVERAGE_VALUE3_BASE 376
#define PERF_PRIVATE_AVERAGE_VALUE4 378
#define PERF_PRIVATE_AVERAGE_VALUE4_BASE 380
#define PERF_PRIVATE_AUDIO_MIXES 382
#define PERF_PRIVATE_RMA_SEND_BYTES 384
#define PERF_PRIVATE_RMA_RECV_BYTES 386
#define PERF_PRIVATE_RMA_SEND_EVENTS 388
#define PERF_PRIVATE_RMA_RECV_EVENTS 390
#define PERF_PRIVATE_NUM_AUDIO_MIXER 392
#define PERF_PRIVATE_NUM_MEMORYPOOL_STREAM 394
#define PERF_PRIVATE_NUM_MEMORYPOOL_AUDIO_SOURCE 396
#define PERF_PRIVATE_NUM_MEMORYPOOL_AUDIO_ENCODE 398
#define PERF_PRIVATE_NUM_MEMORYPOOL_AUDIO_METADATA 400
#define PERF_PRIVATE_NUM_MEMORYPOOL_RTCP 402
#define PERF_PRIVATE_NUM_MEMORYPOOL_RTPEXTHEADER 404
#define PERF_PRIVATE_NUM_MEMORYPOOL_RTPHEADER 406
#define PERF_PRIVATE_NUM_MEMORYPOOL_TRANSPORTIOCONTEXT 408
#define PERF_PRIVATE_TOTAL_POSTED_CBufferTransportIOContext 410
#define PERF_PRIVATE_CONFERENCE_SCHEDULING_RATE 412
#define PERF_PRIVATE_CONFERENCE_SCHEDULING_RATE_BASE 414
#define PERF_PRIVATE_NUM_CONFERENCE_SCHEDULES 416
#define PERF_PRIVATE_TOTAL_NUM_Core1_Conf 418
#define PERF_PRIVATE_TOTAL_Duration_Core1_Conf 420
#define PERF_PRIVATE_TOTAL_Duration_Core1_Work_Conf 422
#define PERF_PRIVATE_BACKGROUND_Duration_Core1_Conf 424
#define PERF_PRIVATE_Num_Offloaded_Participant_Core1 426
#define PERF_PRIVATE_TOTAL_NUM_Core2_Conf 428
#define PERF_PRIVATE_TOTAL_Duration_Core2_Conf 430
#define PERF_PRIVATE_TOTAL_Duration_Core2_Work_Conf 432
#define PERF_PRIVATE_BACKGROUND_Duration_Core2_Conf 434
#define PERF_PRIVATE_Num_Offloaded_Participant_Core2 436
#define PERF_PRIVATE_TOTAL_NUM_Core3_Conf 438
#define PERF_PRIVATE_TOTAL_Duration_Core3_Conf 440
#define PERF_PRIVATE_TOTAL_Duration_Core3_Work_Conf 442
#define PERF_PRIVATE_BACKGROUND_Duration_Core3_Conf 444
#define PERF_PRIVATE_Num_Offloaded_Participant_Core3 446
#define PERF_PRIVATE_TOTAL_NUM_Core4_Conf 448
#define PERF_PRIVATE_TOTAL_Duration_Core4_Conf 450
#define PERF_PRIVATE_TOTAL_Duration_Core4_Work_Conf 452
#define PERF_PRIVATE_BACKGROUND_Duration_Core4_Conf 454
#define PERF_PRIVATE_Num_Offloaded_Participant_Core4 456
#define PERF_PRIVATE_TOTAL_NUM_Core5_Conf 458
#define PERF_PRIVATE_TOTAL_Duration_Core5_Conf 460
#define PERF_PRIVATE_TOTAL_Duration_Core5_Work_Conf 462
#define PERF_PRIVATE_BACKGROUND_Duration_Core5_Conf 464
#define PERF_PRIVATE_Num_Offloaded_Participant_Core5 466
#define PERF_PRIVATE_TOTAL_NUM_Core6_Conf 468
#define PERF_PRIVATE_TOTAL_Duration_Core6_Conf 470
#define PERF_PRIVATE_TOTAL_Duration_Core6_Work_Conf 472
#define PERF_PRIVATE_BACKGROUND_Duration_Core6_Conf 474
#define PERF_PRIVATE_Num_Offloaded_Participant_Core6 476
#define PERF_PRIVATE_TOTAL_NUM_Core7_Conf 478
#define PERF_PRIVATE_TOTAL_Duration_Core7_Conf 480
#define PERF_PRIVATE_TOTAL_Duration_Core7_Work_Conf 482
#define PERF_PRIVATE_BACKGROUND_Duration_Core7_Conf 484
#define PERF_PRIVATE_Num_Offloaded_Participant_Core7 486
#define PERF_PRIVATE_TOTAL_NUM_Core8_Conf 488
#define PERF_PRIVATE_TOTAL_Duration_Core8_Conf 490
#define PERF_PRIVATE_TOTAL_Duration_Core8_Work_Conf 492
#define PERF_PRIVATE_BACKGROUND_Duration_Core8_Conf 494
#define PERF_PRIVATE_Num_Offloaded_Participant_Core8 496
#define PERF_PRIVATE_TOTAL_NUM_Core9_Conf 498
#define PERF_PRIVATE_TOTAL_Duration_Core9_Conf 500
#define PERF_PRIVATE_TOTAL_Duration_Core9_Work_Conf 502
#define PERF_PRIVATE_BACKGROUND_Duration_Core9_Conf 504
#define PERF_PRIVATE_Num_Offloaded_Participant_Core9 506
#define PERF_PRIVATE_TOTAL_NUM_Core10_Conf 508
#define PERF_PRIVATE_TOTAL_Duration_Core10_Conf 510
#define PERF_PRIVATE_TOTAL_Duration_Core10_Work_Conf 512
#define PERF_PRIVATE_BACKGROUND_Duration_Core10_Conf 514
#define PERF_PRIVATE_Num_Offloaded_Participant_Core10 516
#define PERF_PRIVATE_TOTAL_NUM_Core11_Conf 518
#define PERF_PRIVATE_TOTAL_Duration_Core11_Conf 520
#define PERF_PRIVATE_TOTAL_Duration_Core11_Work_Conf 522
#define PERF_PRIVATE_BACKGROUND_Duration_Core11_Conf 524
#define PERF_PRIVATE_Num_Offloaded_Participant_Core11 526
#define PERF_PRIVATE_TOTAL_NUM_Core12_Conf 528
#define PERF_PRIVATE_TOTAL_Duration_Core12_Conf 530
#define PERF_PRIVATE_TOTAL_Duration_Core12_Work_Conf 532
#define PERF_PRIVATE_BACKGROUND_Duration_Core12_Conf 534
#define PERF_PRIVATE_Num_Offloaded_Participant_Core12 536
#define PERF_PRIVATE_TOTAL_NUM_Core13_Conf 538
#define PERF_PRIVATE_TOTAL_Duration_Core13_Conf 540
#define PERF_PRIVATE_TOTAL_Duration_Core13_Work_Conf 542
#define PERF_PRIVATE_BACKGROUND_Duration_Core13_Conf 544
#define PERF_PRIVATE_Num_Offloaded_Participant_Core13 546
#define PERF_PRIVATE_TOTAL_NUM_Core14_Conf 548
#define PERF_PRIVATE_TOTAL_Duration_Core14_Conf 550
#define PERF_PRIVATE_TOTAL_Duration_Core14_Work_Conf 552
#define PERF_PRIVATE_BACKGROUND_Duration_Core14_Conf 554
#define PERF_PRIVATE_Num_Offloaded_Participant_Core14 556
#define PERF_PRIVATE_TOTAL_NUM_Core15_Conf 558
#define PERF_PRIVATE_TOTAL_Duration_Core15_Conf 560
#define PERF_PRIVATE_TOTAL_Duration_Core15_Work_Conf 562
#define PERF_PRIVATE_BACKGROUND_Duration_Core15_Conf 564
#define PERF_PRIVATE_Num_Offloaded_Participant_Core15 566
#define PERF_PRIVATE_TOTAL_NUM_Core16_Conf 568
#define PERF_PRIVATE_TOTAL_Duration_Core16_Conf 570
#define PERF_PRIVATE_TOTAL_Duration_Core16_Work_Conf 572
#define PERF_PRIVATE_BACKGROUND_Duration_Core16_Conf 574
#define PERF_PRIVATE_Num_Offloaded_Participant_Core16 576

#define PERF_TRANSCODING 578
#define PERF_TRANSCODING_MODE 580
#define PERF_TRANSCODING_NUM_INSTANCES_TOTAL 582
#define PERF_TRANSCODING_NUM_INSTANCES_CAPABLE 584
#define PERF_TRANSCODING_NUM_INSTANCES_CAPABLE_MAX 586
#define PERF_TRANSCODING_NUM_INSTANCES_FORCE_PASSTHROUGH 588
#define PERF_TRANSCODING_NUM_INSTANCES_FORCE_TRANSCODE_RESOLUTION 590
#define PERF_TRANSCODING_NUM_INSTANCES_FORCE_TRANSCODE_BITSTREAM 592
#define PERF_TRANSCODING_CURRENT_UTILIZATION 594
#define PERF_TRANSCODING_REQUIRED_UTILIZATION 596
#define PERF_TRANSCODING_REQUIRED_UTILIZATION_THRESHOLD 598
#define PERF_TRANSCODING_DYNAMIC_LOAD 600
#define PERF_TRANSCODING_NUM_STREAMS_SR_VERY_LOW 602
#define PERF_TRANSCODING_NUM_STREAMS_SR_LOW 604
#define PERF_TRANSCODING_NUM_STREAMS_SR_MEDIUM 606
#define PERF_TRANSCODING_NUM_STREAMS_SR_HIGH 608
#define PERF_TRANSCODING_NUM_STREAMS_SR_VERY_HIGH 610
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_VERY_LOW 612
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_LOW 614
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_MEDIUM 616
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_HIGH 618
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_VERY_HIGH 620
#define PERF_TRANSCODING_NUM_STREAMS_PT_VERY_LOW 622
#define PERF_TRANSCODING_NUM_STREAMS_PT_LOW 624
#define PERF_TRANSCODING_NUM_STREAMS_PT_MEDIUM 626
#define PERF_TRANSCODING_NUM_STREAMS_PT_HIGH 628
#define PERF_TRANSCODING_NUM_STREAMS_PT_VERY_HIGH 630
#define PERF_TRANSCODING_NUM_ENCODERS_VERY_OVERLOADED 632
#define PERF_TRANSCODING_ENCODERS_OVERLOADED 634
#define PERF_TRANSCODING_ENCODERS_UNDERLOADED 636
#define PERF_TRANSCODING_NUM_DECODERS_VERY_OVERLOADED 638
#define PERF_TRANSCODING_DECODERS_OVERLOADED 640
#define PERF_TRANSCODING_DECODERS_UNDERLOADED 642

#define PERF_MISC 644
#define PERF_MISC_NUM_VIDEO_IN_HD1080 646
#define PERF_MISC_NUM_VIDEO_IN_HD1440 648
#define PERF_MISC_NUM_VIDEO_IN_HD2160 650
#define PERF_MISC_NUM_VIDEO_OUT_HD1080 652
#define PERF_MISC_NUM_VIDEO_OUT_HD1440 654
#define PERF_MISC_NUM_VIDEO_OUT_HD2160 656
#define PERF_MISC_TOTAL_NUM_DTLS_HANDSHAKE_SUCCESS 658
#define PERF_MISC_TOTAL_NUM_DTLS_HANDSHAKE_FAILURE 660
#define PERF_MISC_AVERAGE_SENT_BW_ESTIMATE 662
#define PERF_MISC_AVERAGE_SENT_BW_ESTIMATE_BASE 664
#define PERF_MISC_AVERAGE_RECV_BW_ESTIMATE 666
#define PERF_MISC_AVERAGE_RECV_BW_ESTIMATE_BASE 668
#define PERF_MISC_INCOMING_PACKETS_LOST 670
#define PERF_MISC_OUTGOING_PACKETS_LOST 672
#define PERF_MISC_AVERAGE_RTT 674
#define PERF_MISC_AVERAGE_RTT_BASE 676
#define PERF_MISC_VIDEOROUTER_PARALLEL_MODE_STARTED_NUM 678
#define PERF_MISC_VIDEOROUTER_PARALLEL_THRESHOLD 680
#define PERF_MISC_VIDEOROUTER_PARALLEL_TOTAL_NUM_SINKS 682
#define PERF_MISC_VIDEOROUTER_PARALLEL_TOTAL_NUM_PARTICIPANTS 684
#define PERF_MISC_VIDEOROUTER_PARALLEL_SINK_NUM_GROUP1 686
#define PERF_MISC_VIDEOROUTER_PARALLEL_SINK_NUM_GROUP2 688
#define PERF_MISC_VIDEOROUTER_PARALLEL_SINK_NUM_GROUP3 690
#define PERF_MISC_VIDEOROUTER_PARALLEL_SINK_NUM_GROUP4 692
#define PERF_MISC_CONFERENCE_PARALLEL_NUM_CONSECUTIVE_ENGINETICK_SKIPPED 694
#define PERF_MISC_CONFERENCE_PARALLEL_NUM_MAX_CONSECUTIVE_ENGINETICK_SKIPPED 696
#define PERF_MISC_PARALLEL_NUM_SCALE_SRTP_PACKAGE_SHARED 698
#define PERF_MISC_PARALLEL_NUM_SCALE_SRTP_PACKAGE_UNSHARED 700
#define PERF_MISC_PARALLEL_NUM_SRTP_ENCRYPTION_FAILURE 702
#define PERF_MISC_PARALLEL_NUM_SRTP_DECRYPTION_FAILURE 704
#define PERF_MISC_ENGINE_API_PROCESS_RATE 706
#define PERF_MISC_ENGINE_API_CALL_PROCESS_TIME 708
#define PERF_MISC_ENGINE_API_CALL_PROCESS_TIME_BASE 710
#define PERF_MISC_VIDEO_ENGINE_DELAY 712
#define PERF_MISC_VIDEO_ENGINE_DELAY_BASE 714
#define PERF_MISC_VIDEO_ENGINE_TIMEOUT_PULL_NUM 716
#define PERF_MISC_RECEIVEQUEUE_AVG_LENGTH 718
#define PERF_MISC_RECEIVEQUEUE_AVG_LENGTH_BASE 720
#define PERF_MISC_RECEIVEQUEUE_MAX_LENGTH 722
#define PERF_MISC_RECEIVEQUEUE_AVG_PUSH_INTERVAL 724
#define PERF_MISC_RECEIVEQUEUE_MAX_PUSH_INTERVAL 726
#define PERF_MISC_RECEIVEQUEUE_AVG_PULL_INTERVAL 728
#define PERF_MISC_RECEIVEQUEUE_MAX_PULL_INTERVAL 730
#define PERF_MISC_RECEIVEQUEUE_FAILED_PUSH_RATE 732
#define PERF_MISC_RECEIVEQUEUE_FAILED_PULL_RATE 734
#define PERF_MISC_COUNT_10PCT_INCOMING_LOSS 736
#define PERF_MISC_COUNT_5PCT_INCOMING_LOSS 738
#define PERF_MISC_COUNT_1PCT_INCOMING_LOSS 740
#define PERF_MISC_COUNT_0PCT_INCOMING_LOSS 742
#define PERF_MISC_COUNT_UNK_INCOMING_LOSS 744
#define PERF_MISC_COUNT_10PCT_OUTGOING_LOSS 746
#define PERF_MISC_COUNT_5PCT_OUTGOING_LOSS 748
#define PERF_MISC_COUNT_1PCT_OUTGOING_LOSS 750
#define PERF_MISC_COUNT_0PCT_OUTGOING_LOSS 752
#define PERF_MISC_COUNT_UNK_OUTGOING_LOSS 754
#define PERF_MISC_COUNT_300MS_AVG_RTT 756
#define PERF_MISC_COUNT_100MS_AVG_RTT 758
#define PERF_MISC_COUNT_0MS_AVG_RTT 760
#define PERF_MISC_COUNT_UNK_AVG_RTT 762
#define PERF_MISC_COUNT_1500KBPS_VIDEO_IN_BANDWIDTH 764
#define PERF_MISC_COUNT_600KBPS_VIDEO_IN_BANDWIDTH 766
#define PERF_MISC_COUNT_0KBPS_VIDEO_IN_BANDWIDTH 768
#define PERF_MISC_COUNT_UNK_VIDEO_IN_BANDWIDTH 770
#define PERF_MISC_COUNT_1500KBPS_VIDEO_OUT_BANDWIDTH 772
#define PERF_MISC_COUNT_600KBPS_VIDEO_OUT_BANDWIDTH 774
#define PERF_MISC_COUNT_0KBPS_VIDEO_OUT_BANDWIDTH 776
#define PERF_MISC_COUNT_UNK_VIDEO_OUT_BANDWIDTH 778
#define PERF_MISC_COUNT_1500KBPS_VBSS_IN_BANDWIDTH 780
#define PERF_MISC_COUNT_600KBPS_VBSS_IN_BANDWIDTH 782
#define PERF_MISC_COUNT_0KBPS_VBSS_IN_BANDWIDTH 784
#define PERF_MISC_COUNT_UNK_VBSS_IN_BANDWIDTH 786
#define PERF_MISC_COUNT_1500KBPS_VBSS_OUT_BANDWIDTH 788
#define PERF_MISC_COUNT_600KBPS_VBSS_OUT_BANDWIDTH 790
#define PERF_MISC_COUNT_0KBPS_VBSS_OUT_BANDWIDTH 792
#define PERF_MISC_COUNT_UNK_VBSS_OUT_BANDWIDTH 794
#define PERF_MISC_MIN_VIDEO_TICK 796
#define PERF_MISC_MIN_AUDIO_TICK 798
#define PERF_MISC_MAX_PENDING_VIDEO_FRAMES 800
#define PERF_MISC_MAX_TRANSPORT_DELAY 802
#define PERF_MISC_MAX_VIDEO_ENGINE_DELAY 804
#define PERF_MISC_NUM_VIDEO_QUEUE_FLUSH_AVSYNC 806
#define PERF_MISC_NUM_INCOMING_SUBSCRIPTIONS 808
#define PERF_MISC_NUM_OUTGOING_SUBSCRIPTIONS 810
#define PERF_MISC_NUM_OUTGOING_PLIS 812
#define PERF_MISC_NUM_INCOMING_KEYFRAMES 814
#define PERF_MISC_NUM_ACTIVE_VIDEO_SOURCES 816
#define PERF_MISC_NUM_UNIQUE_INCOMING_SUBSCRIPTIONS 818
#define PERF_MISC_NUM_INCOMING_SUBSCRIPTIONS_WITH_KEYFRAME_REQUEST 820
#define PERF_MISC_NUM_OUTGOING_SUBSCRIPTIONS_WITH_KEYFRAME_REQUEST 822
#define PERF_MISC_NUM_CONF_IN_VIDEO_DEGRADED_MODE 824
#define PERF_MISC_NUM_SEND_LOSSRATE_ABOVE_THRESHOLD_IN_CROSSLINK 826
#define PERF_MISC_NUM_RECV_LOSSRATE_ABOVE_THRESHOLD_IN_CROSSLINK 828
#define PERF_MISC_NUM_SEND_LOSSRATE_ABOVE_THRESHOLD_AND_TICK_LOW_IN_CROSSLINK 830
#define PERF_MISC_NUM_RECV_LOSSRATE_ABOVE_THRESHOLD_AND_TICK_LOW_IN_CROSSLINK 832

