{"version": 3, "targets": {".NETFramework,Version=v4.7.2": {"Azure.Core/1.18.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Memory": "4.5.4", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/net461/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Azure.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Client/5.2.9": {"type": "package", "dependencies": {"Newtonsoft.Json": "6.0.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.2.9": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.9"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.2.7": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "[5.2.7, 5.3.0)", "Microsoft.Owin": "2.0.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.WebHost/5.2.9": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "[5.2.9, 5.3.0)"}, "compile": {"lib/net45/System.Web.Http.WebHost.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.WebHost.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Graph/4.7.0": {"type": "package", "dependencies": {"Microsoft.Graph.Core": "2.0.5"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Net.Http"], "compile": {"lib/net462/Microsoft.Graph.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Graph.dll": {"related": ".xml"}}}, "Microsoft.Graph.Auth/1.0.0-preview.7": {"type": "package", "dependencies": {"Microsoft.Graph.Core": "1.25.1", "Microsoft.Identity.Client": "4.29.0", "System.Security.Principal": "4.3.0"}, "compile": {"lib/net45/Microsoft.Graph.Auth.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Graph.Auth.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Calls/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Graph.Communications.Client": "1.2.0.3742", "Microsoft.Graph.Communications.Core": "1.2.0.3742"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Calls.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Calls.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Calls.Media/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Graph.Communications.Calls": "1.2.0.3742", "Microsoft.Skype.Bots.Media": "1.20.0.348-alpha"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Calls.Media.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Calls.Media.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Client/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Graph.Communications.Core": "1.2.0.3742", "System.Threading.Tasks.Dataflow": "4.9.0"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Client.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Common/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Graph.Core": "2.0.5", "Newtonsoft.Json": "6.0.1", "System.Text.Json": "5.0.2"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Common.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Core/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Graph": "4.7.0", "Microsoft.Graph.Communications.Common": "1.2.0.3742", "Microsoft.Graph.Core": "[2.0.5, 3.0.0)", "Newtonsoft.Json": "6.0.1"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Core.dll": {"related": ".xml"}}}, "Microsoft.Graph.Core/2.0.5": {"type": "package", "dependencies": {"Azure.Core": "1.18.0", "Microsoft.Identity.Client": "4.35.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.12.2", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Text.Json": "5.0.2"}, "compile": {"lib/net462/Microsoft.Graph.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Graph.Core.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client/4.64.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core", "System.Data", "System.Data.DataSetExtensions", "System.Drawing", "System.IdentityModel", "System.Net.Http", "System.Windows.Forms", "System.Xml", "System.Xml.Linq"], "compile": {"lib/net472/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.12.2": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.12.2", "Microsoft.IdentityModel.Tokens": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.12.2", "System.IdentityModel.Tokens.Jwt": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Microsoft.Skype.Bots.Media/**********-preview": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.7", "Microsoft.AspNet.WebApi.Core": "5.2.7", "Microsoft.AspNet.WebApi.Owin": "5.2.7", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "Microsoft.Owin": "4.2.2", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2", "Newtonsoft.Json": "13.0.3", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Threading.Tasks.Dataflow": "8.0.1", "Unity": "5.11.10"}, "compile": {"lib/net472/Microsoft.Skype.Bots.Media.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Skype.Bots.Media.dll": {"related": ".xml"}}, "build": {"build/net472/Microsoft.Skype.Bots.Media.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IdentityModel.Tokens.Jwt/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.12.2", "Microsoft.IdentityModel.Tokens": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/net461/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Principal/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Text.Encodings.Web/5.0.1": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/5.0.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encodings.Web": "5.0.1", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Dataflow/8.0.1": {"type": "package", "compile": {"lib/net462/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "Unity/5.11.10": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/net47/Unity.Abstractions.dll": {"related": ".pdb"}, "lib/net47/Unity.Container.dll": {"related": ".pdb"}}, "runtime": {"lib/net47/Unity.Abstractions.dll": {"related": ".pdb"}, "lib/net47/Unity.Container.dll": {"related": ".pdb"}}}}, ".NETFramework,Version=v4.7.2/win-x64": {"Azure.Core/1.18.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Memory": "4.5.4", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/net461/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Azure.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Client/5.2.9": {"type": "package", "dependencies": {"Newtonsoft.Json": "6.0.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.2.9": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.9"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.2.7": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "[5.2.7, 5.3.0)", "Microsoft.Owin": "2.0.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.WebHost/5.2.9": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "[5.2.9, 5.3.0)"}, "compile": {"lib/net45/System.Web.Http.WebHost.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.WebHost.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Graph/4.7.0": {"type": "package", "dependencies": {"Microsoft.Graph.Core": "2.0.5"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Net.Http"], "compile": {"lib/net462/Microsoft.Graph.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Graph.dll": {"related": ".xml"}}}, "Microsoft.Graph.Auth/1.0.0-preview.7": {"type": "package", "dependencies": {"Microsoft.Graph.Core": "1.25.1", "Microsoft.Identity.Client": "4.29.0", "System.Security.Principal": "4.3.0"}, "compile": {"lib/net45/Microsoft.Graph.Auth.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Graph.Auth.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Calls/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Graph.Communications.Client": "1.2.0.3742", "Microsoft.Graph.Communications.Core": "1.2.0.3742"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Calls.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Calls.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Calls.Media/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Graph.Communications.Calls": "1.2.0.3742", "Microsoft.Skype.Bots.Media": "1.20.0.348-alpha"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Calls.Media.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Calls.Media.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Client/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Graph.Communications.Core": "1.2.0.3742", "System.Threading.Tasks.Dataflow": "4.9.0"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Client.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Common/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Graph.Core": "2.0.5", "Newtonsoft.Json": "6.0.1", "System.Text.Json": "5.0.2"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Common.dll": {"related": ".xml"}}}, "Microsoft.Graph.Communications.Core/1.2.0.3742": {"type": "package", "dependencies": {"Microsoft.Graph": "4.7.0", "Microsoft.Graph.Communications.Common": "1.2.0.3742", "Microsoft.Graph.Core": "[2.0.5, 3.0.0)", "Newtonsoft.Json": "6.0.1"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Core.dll": {"related": ".xml"}}}, "Microsoft.Graph.Core/2.0.5": {"type": "package", "dependencies": {"Azure.Core": "1.18.0", "Microsoft.Identity.Client": "4.35.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.12.2", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Text.Json": "5.0.2"}, "compile": {"lib/net462/Microsoft.Graph.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Graph.Core.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client/4.64.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core", "System.Data", "System.Data.DataSetExtensions", "System.Drawing", "System.IdentityModel", "System.Net.Http", "System.Windows.Forms", "System.Xml", "System.Xml.Linq"], "compile": {"lib/net472/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.12.2": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.12.2", "Microsoft.IdentityModel.Tokens": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.12.2", "System.IdentityModel.Tokens.Jwt": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Microsoft.Skype.Bots.Media/**********-preview": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.7", "Microsoft.AspNet.WebApi.Core": "5.2.7", "Microsoft.AspNet.WebApi.Owin": "5.2.7", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "Microsoft.Owin": "4.2.2", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2", "Newtonsoft.Json": "13.0.3", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Threading.Tasks.Dataflow": "8.0.1", "Unity": "5.11.10"}, "compile": {"lib/net472/Microsoft.Skype.Bots.Media.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Skype.Bots.Media.dll": {"related": ".xml"}}, "build": {"build/net472/Microsoft.Skype.Bots.Media.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IdentityModel.Tokens.Jwt/6.12.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.12.2", "Microsoft.IdentityModel.Tokens": "6.12.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/net461/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Principal/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Text.Encodings.Web/5.0.1": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/5.0.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encodings.Web": "5.0.1", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Dataflow/8.0.1": {"type": "package", "compile": {"lib/net462/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "Unity/5.11.10": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/net47/Unity.Abstractions.dll": {"related": ".pdb"}, "lib/net47/Unity.Container.dll": {"related": ".pdb"}}, "runtime": {"lib/net47/Unity.Abstractions.dll": {"related": ".pdb"}, "lib/net47/Unity.Container.dll": {"related": ".pdb"}}}}}, "libraries": {"Azure.Core/1.18.0": {"sha512": "h0DQLyIKvowm6n9XV2njDiiI5Et0QecpE2VcGfhps9J4m9MJ6tBD4Qxl+fbL8k/41sD7UXP4nAWgxC81oV11oQ==", "type": "package", "path": "azure.core/1.18.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.18.0.nupkg.sha512", "azure.core.nuspec", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net5.0/Azure.Core.dll", "lib/net5.0/Azure.Core.xml", "lib/netcoreapp2.1/Azure.Core.dll", "lib/netcoreapp2.1/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml", "pkgicon.png"]}, "Microsoft.AspNet.WebApi.Client/5.2.9": {"sha512": "cuVhPjjNMSEFpKXweMNBbsG4RUFuuZpFBm8tSyw309U9JEjcnbB6n3EPb4xwgcy9bJ38ctIbv5G8zXUBhlrPWw==", "type": "package", "path": "microsoft.aspnet.webapi.client/5.2.9", "files": [".nupkg.metadata", ".signature.p7s", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Net.Http.Formatting.dll", "lib/net45/System.Net.Http.Formatting.xml", "lib/netstandard2.0/System.Net.Http.Formatting.dll", "lib/netstandard2.0/System.Net.Http.Formatting.xml", "lib/portable-wp8+netcore45+net45+wp81+wpa81/System.Net.Http.Formatting.dll", "lib/portable-wp8+netcore45+net45+wp81+wpa81/System.Net.Http.Formatting.xml", "microsoft.aspnet.webapi.client.5.2.9.nupkg.sha512", "microsoft.aspnet.webapi.client.nuspec"]}, "Microsoft.AspNet.WebApi.Core/5.2.9": {"sha512": "9C0/gBUeMBjorRFSmR5Z2j5wznf9BP5SddN+OZE7X0BpXgivBZTZ42d0h8tIjJzSLHYVCU4HZBf51+cidCY4fQ==", "type": "package", "path": "microsoft.aspnet.webapi.core/5.2.9", "files": [".nupkg.metadata", ".signature.p7s", "Content/web.config.transform", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Web.Http.dll", "lib/net45/System.Web.Http.xml", "microsoft.aspnet.webapi.core.5.2.9.nupkg.sha512", "microsoft.aspnet.webapi.core.nuspec"]}, "Microsoft.AspNet.WebApi.Owin/5.2.7": {"sha512": "Zb/qw8TyMgQi3CfmIWRmfOh89LozKzOJT6EwnY1D1aUjBCms8WXfvrRusc44hEODD7KUPBJLOEJLCx1yT4vMTg==", "type": "package", "path": "microsoft.aspnet.webapi.owin/5.2.7", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.Web.Http.Owin.dll", "lib/net45/System.Web.Http.Owin.xml", "microsoft.aspnet.webapi.owin.5.2.7.nupkg.sha512", "microsoft.aspnet.webapi.owin.nuspec"]}, "Microsoft.AspNet.WebApi.WebHost/5.2.9": {"sha512": "+mQWEe0/mWdEccKszPI7QOLduYH0ZSl1AjdRXLlM6jKrhS3LnLMBg2WuLEIlQcCS7GCs4GU5LlhycArgg3j84g==", "type": "package", "path": "microsoft.aspnet.webapi.webhost/5.2.9", "files": [".nupkg.metadata", ".signature.p7s", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Web.Http.WebHost.dll", "lib/net45/System.Web.Http.WebHost.xml", "microsoft.aspnet.webapi.webhost.5.2.9.nupkg.sha512", "microsoft.aspnet.webapi.webhost.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"sha512": "W8DPQjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.Abstractions/2.1.1": {"sha512": "XRzK7ZF+O6FzdfWrlFTi1Rgj2080ZDsd46vzOjadHUB0Cz5kOvDG8vI7caa5YFrsHQpcfn0DxtjS4E46N4FZsA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.2.1.1.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec"]}, "Microsoft.Graph/4.7.0": {"sha512": "msFHHXeZ3Lsts3vJRUa80YDTBb8tpdiiHpKO+edvCdzOllCMJVLrdziuThxkeMOoeIfwhLA1EbPA1Aj/nhhI6w==", "type": "package", "path": "microsoft.graph/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/Microsoft.Graph.dll", "lib/net462/Microsoft.Graph.xml", "lib/netstandard2.0/Microsoft.Graph.dll", "lib/netstandard2.0/Microsoft.Graph.xml", "microsoft.graph.4.7.0.nupkg.sha512", "microsoft.graph.nuspec"]}, "Microsoft.Graph.Auth/1.0.0-preview.7": {"sha512": "HZlh6hS01KUT0KpWdf7UDQKuWn2kpoBNps+wfolhsNwat3vaeXtOttFytvCzEdYm6+7BK0KxZCDXqRBe1y+2zw==", "type": "package", "path": "microsoft.graph.auth/1.0.0-preview.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net45/Microsoft.Graph.Auth.dll", "lib/net45/Microsoft.Graph.Auth.xml", "lib/netstandard1.3/Microsoft.Graph.Auth.dll", "lib/netstandard1.3/Microsoft.Graph.Auth.xml", "microsoft.graph.auth.1.0.0-preview.7.nupkg.sha512", "microsoft.graph.auth.nuspec"]}, "Microsoft.Graph.Communications.Calls/1.2.0.3742": {"sha512": "5EOLBy7oZYEIkhh5XnmHlHq9OgOCG39dF2ujgdc9JmB5fwfKfhpfel3+qymachSZ84SefjdGT1T/TKYQq/ajJQ==", "type": "package", "path": "microsoft.graph.communications.calls/1.2.0.3742", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net472/Microsoft.Graph.Communications.Calls.dll", "lib/net472/Microsoft.Graph.Communications.Calls.xml", "lib/netstandard2.0/Microsoft.Graph.Communications.Calls.dll", "lib/netstandard2.0/Microsoft.Graph.Communications.Calls.xml", "microsoft.graph.communications.calls.1.2.0.3742.nupkg.sha512", "microsoft.graph.communications.calls.nuspec"]}, "Microsoft.Graph.Communications.Calls.Media/1.2.0.3742": {"sha512": "WLmXS3MTzQ4X7Gny72KyT3WiasaTsMEB5rDPUwwLMEc0+J/M56jgnlR1H4HUAOlPg4dMUVHmTCbskoFdaMp0MA==", "type": "package", "path": "microsoft.graph.communications.calls.media/1.2.0.3742", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net472/Microsoft.Graph.Communications.Calls.Media.dll", "lib/net472/Microsoft.Graph.Communications.Calls.Media.xml", "microsoft.graph.communications.calls.media.1.2.0.3742.nupkg.sha512", "microsoft.graph.communications.calls.media.nuspec"]}, "Microsoft.Graph.Communications.Client/1.2.0.3742": {"sha512": "0LoZRKi0Bw0fx7cNqOLqx3ThDRNcvfjH74tP02WiIpWCkJAXxikoBLv4ouBw7LNSJAZXOdJGfmJUOmXwJxT70g==", "type": "package", "path": "microsoft.graph.communications.client/1.2.0.3742", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net472/Microsoft.Graph.Communications.Client.dll", "lib/net472/Microsoft.Graph.Communications.Client.xml", "lib/netstandard2.0/Microsoft.Graph.Communications.Client.dll", "lib/netstandard2.0/Microsoft.Graph.Communications.Client.xml", "microsoft.graph.communications.client.1.2.0.3742.nupkg.sha512", "microsoft.graph.communications.client.nuspec"]}, "Microsoft.Graph.Communications.Common/1.2.0.3742": {"sha512": "xJLs+eWTW/sK6y3Ix3MQtUZI8J4HknK/gxh0bnv9h7B+bJyMQDHePU30BEiI3t6xULxVY7qDhI3ts7i0CStjSA==", "type": "package", "path": "microsoft.graph.communications.common/1.2.0.3742", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net472/Microsoft.Graph.Communications.Common.dll", "lib/net472/Microsoft.Graph.Communications.Common.xml", "lib/netstandard2.0/Microsoft.Graph.Communications.Common.dll", "lib/netstandard2.0/Microsoft.Graph.Communications.Common.xml", "microsoft.graph.communications.common.1.2.0.3742.nupkg.sha512", "microsoft.graph.communications.common.nuspec"]}, "Microsoft.Graph.Communications.Core/1.2.0.3742": {"sha512": "eDsDQmvSDqG2JLWy7Ei8UcXIpsZw+sjF3QJk+wTN9TPbnDszwbor27yzqUJ/1E/clBwQ0SF/nb76zHWfjBBUFw==", "type": "package", "path": "microsoft.graph.communications.core/1.2.0.3742", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net472/Microsoft.Graph.Communications.Core.dll", "lib/net472/Microsoft.Graph.Communications.Core.xml", "lib/netstandard2.0/Microsoft.Graph.Communications.Core.dll", "lib/netstandard2.0/Microsoft.Graph.Communications.Core.xml", "microsoft.graph.communications.core.1.2.0.3742.nupkg.sha512", "microsoft.graph.communications.core.nuspec"]}, "Microsoft.Graph.Core/2.0.5": {"sha512": "PRaGr69SZU4eAex/7UaGWA2Qh2MoN+WSl0Dp+M44UFWtio6yDI7EvAgtKiVNZZG1RBYweEwaP6GflEbedLkIVA==", "type": "package", "path": "microsoft.graph.core/2.0.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/monoandroid80/Microsoft.Graph.Core.dll", "lib/monoandroid80/Microsoft.Graph.Core.xml", "lib/net462/Microsoft.Graph.Core.dll", "lib/net462/Microsoft.Graph.Core.xml", "lib/netstandard2.0/Microsoft.Graph.Core.dll", "lib/netstandard2.0/Microsoft.Graph.Core.xml", "lib/xamarinios10/Microsoft.Graph.Core.dll", "lib/xamarinios10/Microsoft.Graph.Core.xml", "lib/xamarinmac20/Microsoft.Graph.Core.dll", "lib/xamarinmac20/Microsoft.Graph.Core.xml", "microsoft.graph.core.2.0.5.nupkg.sha512", "microsoft.graph.core.nuspec"]}, "Microsoft.Identity.Client/4.64.1": {"sha512": "xiPBlQ/aRc1MRTOyTonQF9mwFfGQc/zDWEG1zlBopOubCXKpUWg91QeQNYKYnn/PGUnv1ivDyb84vuu5DsTlpw==", "type": "package", "path": "microsoft.identity.client/4.64.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net472/Microsoft.Identity.Client.dll", "lib/net472/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.64.1.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"sha512": "xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "type": "package", "path": "microsoft.identitymodel.abstractions/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Abstractions.dll", "lib/net45/Microsoft.IdentityModel.Abstractions.xml", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.12.2": {"sha512": "krI9z6wjjIMCoQRmRicPt5oy9qrpCyXJhopkv4L+8uoN8l+NTmyv+gUCxkZKjiBqChgCL4cMvYCAkVdbjgpj+g==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.12.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.12.2.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.12.2": {"sha512": "MGjGS5PIxGOdDSo3DzjqZ4qfy2i75R/CGE3aehCkflb4Brh617jg5F6GoFze321PMK1bjqiqQUFtGVuMKojDWg==", "type": "package", "path": "microsoft.identitymodel.logging/6.12.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.12.2.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/6.12.2": {"sha512": "gEa/zkofJxVBp+JONsZuvAYKBrRbOAxESEGzMFLO9pEFHihYEz+UI9rUeJ/E/am1VXF2UytQUUUdAlDa1ybC1A==", "type": "package", "path": "microsoft.identitymodel.protocols/6.12.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.dll", "lib/net45/Microsoft.IdentityModel.Protocols.xml", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.6.12.2.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.12.2": {"sha512": "zDsRZQuBJegBunnoCbQqOzInl79TDQK/N9Hc07hEoHAjXc+8lKsSGzUPlNTLvgYi97Qp2PEoVHQoUddwmjdXtQ==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/6.12.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.6.12.2.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.12.2": {"sha512": "qG/UJzrcPYVEMHgP6X4Nko0yeLIwr4dM2qZvIC91WYMmt1zGvDWW9ybqGBOQAs3A5Wf2M1PozobHDGRW3MmINQ==", "type": "package", "path": "microsoft.identitymodel.tokens/6.12.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.12.2.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"sha512": "Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q==", "type": "package", "path": "microsoft.io.recyclablememorystream/2.3.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net462/Microsoft.IO.RecyclableMemoryStream.xml", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.Owin/4.2.2": {"sha512": "jt410l/8dvCIguRdU7dupYdm4kGLepVdD8EOTKU4nYZcLRrn6kQYqI6pbJOTJp7Vlm/T2WUF5bzyKK2z29xtjg==", "type": "package", "path": "microsoft.owin/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.dll", "lib/net45/Microsoft.Owin.xml", "microsoft.owin.4.2.2.nupkg.sha512", "microsoft.owin.nuspec"]}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"sha512": "Kl1A0sBzfMD3qvX6XcGU0FopN6POFFRpIEQnKIAbvsShadIG9/UxgDdHVlX/IzFHXwIHfY59Ae4RGDVKYNvIqQ==", "type": "package", "path": "microsoft.owin.host.httplistener/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.Host.HttpListener.dll", "lib/net45/Microsoft.Owin.Host.HttpListener.xml", "microsoft.owin.host.httplistener.4.2.2.nupkg.sha512", "microsoft.owin.host.httplistener.nuspec"]}, "Microsoft.Owin.Hosting/4.2.2": {"sha512": "KsupM0TNPUfLn1uHvQy22IX6VWE+wi7C2shseSnhO9JYFxgwWcsSmjxrRpw+xcD+4hA3O280tBxfZ6T+kJuhjg==", "type": "package", "path": "microsoft.owin.hosting/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.Hosting.dll", "lib/net45/Microsoft.Owin.Hosting.xml", "microsoft.owin.hosting.4.2.2.nupkg.sha512", "microsoft.owin.hosting.nuspec"]}, "Microsoft.Skype.Bots.Media/**********-preview": {"sha512": "1Zw18ohqoRoFFTJGtOlmBHxy0fzbRbQzxb4vkLy+l+i9DKWqEgEC0z7NFHNl9MIjWzHGzkV2fun8u/AXFXlI1A==", "type": "package", "path": "microsoft.skype.bots.media/**********-preview", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/net472/Microsoft.Skype.Bots.Media.targets", "build/net6.0/Microsoft.Skype.Bots.Media.targets", "build/net8.0/Microsoft.Skype.Bots.Media.targets", "lib/net472/Microsoft.Skype.Bots.Media.dll", "lib/net472/Microsoft.Skype.Bots.Media.xml", "lib/net6.0/MP.Contracts.dll", "lib/net6.0/MP.WebAPI.dll", "lib/net6.0/MPAzAppHost.dll", "lib/net6.0/MPServiceHostLib.dll", "lib/net6.0/MPServiceImp.dll", "lib/net6.0/Microsoft.Applications.Events.Server.dll", "lib/net6.0/Microsoft.Rtc.Internal.Media.MediaApi.dll", "lib/net6.0/Microsoft.Rtc.Internal.Media.dll", "lib/net6.0/Microsoft.Skype.Bots.Media.dll", "lib/net6.0/Microsoft.Skype.Bots.Media.xml", "lib/net6.0/Microsoft.Skype.ECS.Client.InternalizedDependencies.dll", "lib/net6.0/Microsoft.Skype.Internal.Media.AudioLibNetCore.dll", "lib/net6.0/Microsoft.Skype.Internal.Media.H264NetCore.dll", "lib/net8.0/MP.Contracts.dll", "lib/net8.0/MP.WebAPI.dll", "lib/net8.0/MPAzAppHost.dll", "lib/net8.0/MPServiceHostLib.dll", "lib/net8.0/MPServiceImp.dll", "lib/net8.0/Microsoft.Applications.Events.Server.dll", "lib/net8.0/Microsoft.Rtc.Internal.Media.MediaApi.dll", "lib/net8.0/Microsoft.Rtc.Internal.Media.dll", "lib/net8.0/Microsoft.Skype.Bots.Media.dll", "lib/net8.0/Microsoft.Skype.Bots.Media.xml", "lib/net8.0/Microsoft.Skype.ECS.Client.InternalizedDependencies.dll", "lib/net8.0/Microsoft.Skype.Internal.Media.AudioLibNetCore.dll", "lib/net8.0/Microsoft.Skype.Internal.Media.H264NetCore.dll", "license.txt", "microsoft.skype.bots.media.**********-preview.nupkg.sha512", "microsoft.skype.bots.media.nuspec", "readme.txt", "ref/net6.0/Microsoft.Skype.Bots.Media.dll", "ref/net6.0/Microsoft.Skype.Bots.Media.xml", "ref/net8.0/Microsoft.Skype.Bots.Media.dll", "ref/net8.0/Microsoft.Skype.Bots.Media.xml", "runtime/win/lib/net6.0/MP.Contracts.dll", "runtime/win/lib/net6.0/MP.WebAPI.dll", "runtime/win/lib/net6.0/MPAzAppHost.dll", "runtime/win/lib/net6.0/MPServiceHostLib.dll", "runtime/win/lib/net6.0/MPServiceImp.dll", "runtime/win/lib/net6.0/Microsoft.Rtc.Internal.Media.MediaApi.dll", "runtime/win/lib/net6.0/Microsoft.Rtc.Internal.Media.dll", "runtime/win/lib/net6.0/Microsoft.Skype.ECS.Client.InternalizedDependencies.dll", "runtime/win/lib/net8.0/MP.Contracts.dll", "runtime/win/lib/net8.0/MP.WebAPI.dll", "runtime/win/lib/net8.0/MPAzAppHost.dll", "runtime/win/lib/net8.0/MPServiceHostLib.dll", "runtime/win/lib/net8.0/MPServiceImp.dll", "runtime/win/lib/net8.0/Microsoft.Rtc.Internal.Media.MediaApi.dll", "runtime/win/lib/net8.0/Microsoft.Rtc.Internal.Media.dll", "runtime/win/lib/net8.0/Microsoft.Skype.ECS.Client.InternalizedDependencies.dll", "src/net6.0/IfxMetricExtensions.dll", "src/net6.0/Ijwhost.dll", "src/net6.0/InstallMPServiceImpCounters.ps1", "src/net6.0/MediaPerf.dll", "src/net6.0/MediaPerf.h", "src/net6.0/MediaPerf.ini", "src/net6.0/MediaPerf.pdb.logmap", "src/net6.0/MediaPlatformStartupScript.bat", "src/net6.0/NativeMedia.dll", "src/net6.0/NativeMedia.pdb.logmap", "src/net6.0/RtmCodecs.dll", "src/net6.0/RtmCodecs.pdb.logmap", "src/net6.0/RtmMvrCs.dll", "src/net6.0/RtmMvrCs.pdb.logmap", "src/net6.0/RtmPal.dll", "src/net6.0/RtmPal.pdb.logmap", "src/net6.0/SlimCV.dll", "src/net6.0/skypert.dll", "src/net6.0/skypert.logmap", "src/net8.0/IfxMetricExtensions.dll", "src/net8.0/Ijwhost.dll", "src/net8.0/InstallMPServiceImpCounters.ps1", "src/net8.0/MediaPerf.dll", "src/net8.0/MediaPerf.h", "src/net8.0/MediaPerf.ini", "src/net8.0/MediaPerf.pdb.logmap", "src/net8.0/MediaPlatformStartupScript.bat", "src/net8.0/NativeMedia.dll", "src/net8.0/NativeMedia.pdb.logmap", "src/net8.0/RtmCodecs.dll", "src/net8.0/RtmCodecs.pdb.logmap", "src/net8.0/RtmMvrCs.dll", "src/net8.0/RtmMvrCs.pdb.logmap", "src/net8.0/RtmPal.dll", "src/net8.0/RtmPal.pdb.logmap", "src/net8.0/SlimCV.dll", "src/net8.0/skypert.dll", "src/net8.0/skypert.logmap", "src/skype_media_lib/AppDomainResolver.dll", "src/skype_media_lib/IfxMetricExtensions.dll", "src/skype_media_lib/InstallMPServiceImpCounters.ps1", "src/skype_media_lib/MP.Contracts.dll", "src/skype_media_lib/MP.WebAPI.dll", "src/skype_media_lib/MPAzAppHost.dll", "src/skype_media_lib/MPServiceHostLib.dll", "src/skype_media_lib/MPServiceImp.dll", "src/skype_media_lib/MediaPerf.dll", "src/skype_media_lib/MediaPerf.h", "src/skype_media_lib/MediaPerf.ini", "src/skype_media_lib/MediaPerf.pdb.logmap", "src/skype_media_lib/MediaPlatformStartupScript.bat", "src/skype_media_lib/Microsoft.Applications.Telemetry.Server.dll", "src/skype_media_lib/Microsoft.Applications.Telemetry.dll", "src/skype_media_lib/Microsoft.Bond.Interfaces.dll", "src/skype_media_lib/Microsoft.Bond.dll", "src/skype_media_lib/Microsoft.Rtc.Internal.Media.MediaApi.dll", "src/skype_media_lib/Microsoft.Rtc.Internal.Media.MediaApi.pdb.logmap", "src/skype_media_lib/Microsoft.Rtc.Internal.Media.dll", "src/skype_media_lib/Microsoft.Rtc.Internal.Media.pdb.logmap", "src/skype_media_lib/Microsoft.Skype.ECS.Client.InternalizedDependencies.dll", "src/skype_media_lib/Microsoft.Skype.Internal.Media.AudioLib.dll", "src/skype_media_lib/Microsoft.Skype.Internal.Media.H264.dll", "src/skype_media_lib/RtmCodecs.dll", "src/skype_media_lib/RtmCodecs.pdb.logmap", "src/skype_media_lib/RtmMvrCs.dll", "src/skype_media_lib/RtmMvrCs.pdb.logmap", "src/skype_media_lib/RtmPal.dll", "src/skype_media_lib/RtmPal.pdb.logmap", "src/skype_media_lib/SlimCV.dll", "src/skype_media_lib/skypert.dll", "src/skype_media_lib/skypert.logmap", "tools/install.ps1", "tools/uninstall.ps1"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Owin/1.0.0": {"sha512": "OseTFniKmyp76mEzOBwIKGBRS5eMoYNkMKaMXOpxx9jv88+b6mh1rSaw43vjBOItNhaLFG3d0a20PfHyibH5sw==", "type": "package", "path": "owin/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Owin.dll", "owin.1.0.0.nupkg.sha512", "owin.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/8.0.1": {"sha512": "vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/6.12.2": {"sha512": "EuH4UucURwusIGhZNJFI3dS8d6voaaF00v9ezllvn5AlJk+5O5LC7rH967ktWbFeTTYlQx3ltlcRp1hW4VXKWQ==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.12.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.12.2.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal/4.3.0": {"sha512": "I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "type": "package", "path": "system.security.principal/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Security.Principal.dll", "lib/netstandard1.0/System.Security.Principal.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Security.Principal.dll", "ref/netcore50/System.Security.Principal.xml", "ref/netcore50/de/System.Security.Principal.xml", "ref/netcore50/es/System.Security.Principal.xml", "ref/netcore50/fr/System.Security.Principal.xml", "ref/netcore50/it/System.Security.Principal.xml", "ref/netcore50/ja/System.Security.Principal.xml", "ref/netcore50/ko/System.Security.Principal.xml", "ref/netcore50/ru/System.Security.Principal.xml", "ref/netcore50/zh-hans/System.Security.Principal.xml", "ref/netcore50/zh-hant/System.Security.Principal.xml", "ref/netstandard1.0/System.Security.Principal.dll", "ref/netstandard1.0/System.Security.Principal.xml", "ref/netstandard1.0/de/System.Security.Principal.xml", "ref/netstandard1.0/es/System.Security.Principal.xml", "ref/netstandard1.0/fr/System.Security.Principal.xml", "ref/netstandard1.0/it/System.Security.Principal.xml", "ref/netstandard1.0/ja/System.Security.Principal.xml", "ref/netstandard1.0/ko/System.Security.Principal.xml", "ref/netstandard1.0/ru/System.Security.Principal.xml", "ref/netstandard1.0/zh-hans/System.Security.Principal.xml", "ref/netstandard1.0/zh-hant/System.Security.Principal.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.principal.4.3.0.nupkg.sha512", "system.security.principal.nuspec"]}, "System.Text.Encodings.Web/5.0.1": {"sha512": "KmJ+CJXizDofbq6mpqDoRRLcxgOd2z9X3XoFNULSbvbqVRZkFX3istvr+MUjL6Zw1RT+RNdoI4GYidIINtgvqQ==", "type": "package", "path": "system.text.encodings.web/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/netcoreapp3.0/System.Text.Encodings.Web.dll", "lib/netcoreapp3.0/System.Text.Encodings.Web.xml", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "lib/netstandard2.1/System.Text.Encodings.Web.dll", "lib/netstandard2.1/System.Text.Encodings.Web.xml", "system.text.encodings.web.5.0.1.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/5.0.2": {"sha512": "I47dVIGiV6SfAyppphxqupertT/5oZkYLDCX6vC3HpOI4ZLjyoKAreUoem2ie6G0RbRuFrlqz/PcTQjfb2DOfQ==", "type": "package", "path": "system.text.json/5.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.5.0.2.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Dataflow/8.0.1": {"sha512": "4pjq2vIPNZkKA9asAXzf5IRBb7K+b0+UQZZbpv6g029sAPZgnKdg/NNOC/DbJL8SWqYcFMVjb/T/YEmb0PHUYg==", "type": "package", "path": "system.threading.tasks.dataflow/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.Tasks.Dataflow.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.Tasks.Dataflow.targets", "lib/net462/System.Threading.Tasks.Dataflow.dll", "lib/net462/System.Threading.Tasks.Dataflow.xml", "lib/net6.0/System.Threading.Tasks.Dataflow.dll", "lib/net6.0/System.Threading.Tasks.Dataflow.xml", "lib/net7.0/System.Threading.Tasks.Dataflow.dll", "lib/net7.0/System.Threading.Tasks.Dataflow.xml", "lib/net8.0/System.Threading.Tasks.Dataflow.dll", "lib/net8.0/System.Threading.Tasks.Dataflow.xml", "lib/netstandard2.0/System.Threading.Tasks.Dataflow.dll", "lib/netstandard2.0/System.Threading.Tasks.Dataflow.xml", "lib/netstandard2.1/System.Threading.Tasks.Dataflow.dll", "lib/netstandard2.1/System.Threading.Tasks.Dataflow.xml", "system.threading.tasks.dataflow.8.0.1.nupkg.sha512", "system.threading.tasks.dataflow.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Unity/5.11.10": {"sha512": "B4+Ps3oqI78hJ+dAFsJhPkJT6qycsNExgLbtw7CEHSzKc2ac3YyUR8SHQ+ZyTwld/y5IbDx/aNOHnKE9Em1zWA==", "type": "package", "path": "unity/5.11.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Unity.Abstractions.dll", "lib/net40/Unity.Abstractions.pdb", "lib/net40/Unity.Container.dll", "lib/net40/Unity.Container.pdb", "lib/net45/Unity.Abstractions.dll", "lib/net45/Unity.Abstractions.pdb", "lib/net45/Unity.Container.dll", "lib/net45/Unity.Container.pdb", "lib/net46/Unity.Abstractions.dll", "lib/net46/Unity.Abstractions.pdb", "lib/net46/Unity.Container.dll", "lib/net46/Unity.Container.pdb", "lib/net47/Unity.Abstractions.dll", "lib/net47/Unity.Abstractions.pdb", "lib/net47/Unity.Container.dll", "lib/net47/Unity.Container.pdb", "lib/net48/Unity.Abstractions.dll", "lib/net48/Unity.Abstractions.pdb", "lib/net48/Unity.Container.dll", "lib/net48/Unity.Container.pdb", "lib/netcoreapp1.0/Unity.Abstractions.dll", "lib/netcoreapp1.0/Unity.Abstractions.pdb", "lib/netcoreapp1.0/Unity.Container.dll", "lib/netcoreapp1.0/Unity.Container.pdb", "lib/netcoreapp2.0/Unity.Abstractions.dll", "lib/netcoreapp2.0/Unity.Abstractions.pdb", "lib/netcoreapp2.0/Unity.Container.dll", "lib/netcoreapp2.0/Unity.Container.pdb", "lib/netcoreapp3.0/Unity.Abstractions.dll", "lib/netcoreapp3.0/Unity.Abstractions.pdb", "lib/netcoreapp3.0/Unity.Container.dll", "lib/netcoreapp3.0/Unity.Container.pdb", "lib/netstandard1.0/Unity.Abstractions.dll", "lib/netstandard1.0/Unity.Abstractions.pdb", "lib/netstandard1.0/Unity.Container.dll", "lib/netstandard1.0/Unity.Container.pdb", "lib/netstandard2.0/Unity.Abstractions.dll", "lib/netstandard2.0/Unity.Abstractions.pdb", "lib/netstandard2.0/Unity.Container.dll", "lib/netstandard2.0/Unity.Container.pdb", "unity.5.11.10.nupkg.sha512", "unity.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.7.2": ["Microsoft.AspNet.WebApi.Core >= 5.2.9", "Microsoft.AspNet.WebApi.WebHost >= 5.2.9", "Microsoft.Graph.Auth >= 1.0.0-preview.7", "Microsoft.Graph.Communications.Calls.Media >= 1.2.0.3742", "Microsoft.Graph.Communications.Client >= 1.2.0.3742", "Microsoft.Graph.Communications.Common >= 1.2.0.3742", "Microsoft.Identity.Client >= 4.64.1", "Microsoft.Owin >= 4.2.2", "Microsoft.Owin.Host.HttpListener >= 4.2.2", "Microsoft.Owin.Hosting >= 4.2.2", "Microsoft.Skype.Bots.Media >= **********-preview", "Newtonsoft.Json >= 13.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj", "projectName": "AccureMD.MediaBot.Worker", "projectPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Microsoft.AspNet.WebApi.Core": {"target": "Package", "version": "[5.2.9, )"}, "Microsoft.AspNet.WebApi.WebHost": {"target": "Package", "version": "[5.2.9, )"}, "Microsoft.Graph.Auth": {"target": "Package", "version": "[1.0.0-preview.7, )"}, "Microsoft.Graph.Communications.Calls.Media": {"target": "Package", "version": "[1.2.0.3742, )"}, "Microsoft.Graph.Communications.Client": {"target": "Package", "version": "[1.2.0.3742, )"}, "Microsoft.Graph.Communications.Common": {"target": "Package", "version": "[1.2.0.3742, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.64.1, )"}, "Microsoft.Owin": {"target": "Package", "version": "[4.2.2, )"}, "Microsoft.Owin.Host.HttpListener": {"target": "Package", "version": "[4.2.2, )"}, "Microsoft.Owin.Hosting": {"target": "Package", "version": "[4.2.2, )"}, "Microsoft.Skype.Bots.Media": {"target": "Package", "version": "[**********-preview, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}