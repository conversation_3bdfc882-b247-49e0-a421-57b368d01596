using AccureMD.TeamsBot.MediaService.Models;
using System.Collections.Concurrent;
using System.Text.Json;

namespace AccureMD.TeamsBot.MediaService.Services;

public class CallStateService
{
    private readonly ConcurrentDictionary<string, CallState> _activeCalls = new();
    private readonly ILogger<CallStateService> _logger;

    public CallStateService(ILogger<CallStateService> logger)
    {
        _logger = logger;
    }

    public void CreateCall(string callId, string meetingUrl, string? tenantId = null)
    {
        var callState = new CallState
        {
            CallId = callId,
            MeetingUrl = meetingUrl,
            TenantId = tenantId,
            Status = CallStatus.Connecting
        };

        _activeCalls.TryAdd(callId, callState);
        _logger.LogInformation("Created call state for call {CallId}", callId);
    }

    public CallState? GetCallState(string callId)
    {
        _activeCalls.TryGetValue(callId, out var callState);
        return callState;
    }

    public void UpdateCallStatus(string callId, CallStatus status)
    {
        if (_activeCalls.TryGetValue(callId, out var callState))
        {
            callState.Status = status;
            
            if (status == CallStatus.Established && !callState.EstablishedAt.HasValue)
            {
                callState.EstablishedAt = DateTime.UtcNow;
            }
            else if (status == CallStatus.Ended && !callState.EndedAt.HasValue)
            {
                callState.EndedAt = DateTime.UtcNow;
            }

            _logger.LogInformation("Updated call {CallId} status to {Status}", callId, status);
        }
    }

    public void AddParticipant(string callId, string participantId)
    {
        if (_activeCalls.TryGetValue(callId, out var callState))
        {
            if (!callState.Participants.Contains(participantId))
            {
                callState.Participants.Add(participantId);
                _logger.LogInformation("Added participant {ParticipantId} to call {CallId}", participantId, callId);
            }
        }
    }

    public void RemoveParticipant(string callId, string participantId)
    {
        if (_activeCalls.TryGetValue(callId, out var callState))
        {
            callState.Participants.Remove(participantId);
            _logger.LogInformation("Removed participant {ParticipantId} from call {CallId}", participantId, callId);
        }
    }

    public void UpdateMediaStream(string callId, string participantId, string mediaType, bool isActive)
    {
        if (_activeCalls.TryGetValue(callId, out var callState))
        {
            var streamKey = $"{participantId}_{mediaType}";
            
            if (!callState.MediaStreams.ContainsKey(streamKey))
            {
                callState.MediaStreams[streamKey] = new MediaStreamInfo
                {
                    ParticipantId = participantId,
                    MediaType = mediaType
                };
            }

            var stream = callState.MediaStreams[streamKey];
            stream.IsActive = isActive;
            
            if (isActive && !stream.StartedAt.HasValue)
            {
                stream.StartedAt = DateTime.UtcNow;
                if (callState.Status == CallStatus.Established)
                {
                    callState.Status = CallStatus.MediaActive;
                }
            }
            else if (!isActive && stream.IsActive)
            {
                stream.EndedAt = DateTime.UtcNow;
            }

            _logger.LogInformation("Updated media stream for participant {ParticipantId} in call {CallId}: {MediaType} = {IsActive}", 
                participantId, callId, mediaType, isActive);
        }
    }

    public void RecordCallbackEvent(string callId, string eventType, string rawData)
    {
        if (_activeCalls.TryGetValue(callId, out var callState))
        {
            var callbackEvent = new CallbackEvent
            {
                EventType = eventType,
                RawData = rawData,
                Processed = false
            };

            callState.Events.Add(callbackEvent);
            _logger.LogInformation("Recorded callback event {EventType} for call {CallId}", eventType, callId);
        }
    }

    public void RecordCallbackBody(string rawBody)
    {
        try
        {
            var jsonDoc = JsonDocument.Parse(rawBody);
            var root = jsonDoc.RootElement;
            
            // Try to extract call ID from the callback
            string? callId = null;
            if (root.TryGetProperty("resourceData", out var resourceData))
            {
                if (resourceData.TryGetProperty("id", out var idElement))
                {
                    callId = idElement.GetString();
                }
            }

            if (!string.IsNullOrEmpty(callId))
            {
                RecordCallbackEvent(callId, "callback", rawBody);
            }
            else
            {
                _logger.LogWarning("Could not extract call ID from callback body");
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse callback body as JSON");
        }
    }

    public void RemoveCall(string callId)
    {
        if (_activeCalls.TryRemove(callId, out var callState))
        {
            _logger.LogInformation("Removed call state for call {CallId}", callId);
        }
    }

    public IEnumerable<CallState> GetAllActiveCalls()
    {
        return _activeCalls.Values.Where(c => c.Status != CallStatus.Ended);
    }
}
