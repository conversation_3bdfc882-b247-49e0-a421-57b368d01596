<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <OutputType>Exe</OutputType>
    <Platforms>x64</Platforms>
    <PlatformTarget>x64</PlatformTarget>
    <AssemblyName>AccureMD.MediaBot.Worker</AssemblyName>
    <RootNamespace>AccureMD.MediaBot.Worker</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <!-- Graph Communications Calling SDK and Bot Media SDK -->
    <PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="5.2.9" />
    <PackageReference Include="Microsoft.AspNet.WebApi.WebHost" Version="5.2.9" />
    <PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.7" />
    <PackageReference Include="Microsoft.Graph.Communications.Common" Version="1.2.0.3742" />
    <PackageReference Include="Microsoft.Owin" Version="4.2.2" />
    <PackageReference Include="Microsoft.Owin.Host.HttpListener" Version="4.2.2" />
    <PackageReference Include="Microsoft.Owin.Hosting" Version="4.2.2" />
    <PackageReference Include="Microsoft.Skype.Bots.Media" Version="**********-preview" />
    <PackageReference Include="Microsoft.Graph.Communications.Calls.Media" Version="1.2.0.3742" />
    <PackageReference Include="Microsoft.Graph.Communications.Client" Version="1.2.0.3742" />

    <!-- Auth and logging -->
    <PackageReference Include="Microsoft.Identity.Client" Version="4.64.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Configuration" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Web" />
  </ItemGroup>

</Project>

