{"format": 1, "restore": {"D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj": {}}, "projects": {"D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj", "projectName": "AccureMD.MediaBot.Worker", "projectPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Microsoft.AspNet.WebApi.Core": {"target": "Package", "version": "[5.2.9, )"}, "Microsoft.AspNet.WebApi.WebHost": {"target": "Package", "version": "[5.2.9, )"}, "Microsoft.Graph.Auth": {"target": "Package", "version": "[1.0.0-preview.7, )"}, "Microsoft.Graph.Communications.Calls.Media": {"target": "Package", "version": "[1.2.0.3742, )"}, "Microsoft.Graph.Communications.Client": {"target": "Package", "version": "[1.2.0.3742, )"}, "Microsoft.Graph.Communications.Common": {"target": "Package", "version": "[1.2.0.3742, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.64.1, )"}, "Microsoft.Owin": {"target": "Package", "version": "[4.2.2, )"}, "Microsoft.Owin.Host.HttpListener": {"target": "Package", "version": "[4.2.2, )"}, "Microsoft.Owin.Hosting": {"target": "Package", "version": "[4.2.2, )"}, "Microsoft.Skype.Bots.Media": {"target": "Package", "version": "[**********-preview, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}