using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebSockets;
using System.Net.WebSockets;

namespace AccureMD.TeamsBot.Controllers;

[ApiController]
[Route("ws")] // WebSocket endpoints
public class IngestController : ControllerBase
{
    // /ws/audio?sessionId=...
    [Route("audio")]
    public async Task Audio()
    {
        if (!HttpContext.WebSockets.IsWebSocketRequest)
        {
            HttpContext.Response.StatusCode = 400;
            await HttpContext.Response.WriteAsync("Expected WebSocket");
            return;
        }

        using var ws = await HttpContext.WebSockets.AcceptWebSocketAsync();
        var sessionId = HttpContext.Request.Query["sessionId"].ToString();
        var buffer = new byte[64 * 1024];
        var seg = new ArraySegment<byte>(buffer);

        while (ws.State == WebSocketState.Open)
        {
            var result = await ws.ReceiveAsync(seg, HttpContext.RequestAborted);
            if (result.MessageType == WebSocketMessageType.Close) break;
            if (result.Count > 0)
            {
                // TODO: Parse WebM/Opus segment and pipe to transcription pipeline; for now, count bytes
                // You can integrate FFmpeg transcoding or decode Opus with a lib, then feed PCM to existing TranscriptionService
            }
        }
    }

    // /ws/video?sessionId=...
    [Route("video")]
    public async Task Video()
    {
        if (!HttpContext.WebSockets.IsWebSocketRequest)
        {
            HttpContext.Response.StatusCode = 400;
            await HttpContext.Response.WriteAsync("Expected WebSocket");
            return;
        }

        using var ws = await HttpContext.WebSockets.AcceptWebSocketAsync();
        var sessionId = HttpContext.Request.Query["sessionId"].ToString();
        var buffer = new byte[256 * 1024];
        var seg = new ArraySegment<byte>(buffer);

        while (ws.State == WebSocketState.Open)
        {
            var result = await ws.ReceiveAsync(seg, HttpContext.RequestAborted);
            if (result.MessageType == WebSocketMessageType.Close) break;
            if (result.Count > 0)
            {
                // TODO: Store WebM video chunks to disk or forward to an FFmpeg process for muxing/segmenting
            }
        }
    }
}

