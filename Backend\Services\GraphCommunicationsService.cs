using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Identity.Client;

namespace AccureMD.TeamsBot.Services;

public class GraphCommunicationsService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<GraphCommunicationsService> _logger;

    public GraphCommunicationsService(IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<GraphCommunicationsService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<(bool Success, string? CallId, string Message)> JoinMeetingAsGuestAsync(string joinWebUrl, string? meetingTenantId = null)
    {
        try
        {
            var configuredTenantId = _configuration["Teams:TenantId"] ?? _configuration["MicrosoftAppTenantId"];
            var clientId = _configuration["Teams:AppId"] ?? _configuration["MicrosoftAppId"];
            var clientSecret = _configuration["Teams:AppSecret"] ?? _configuration["MicrosoftAppPassword"];
            var baseUrl = _configuration["BaseUrl"] ?? string.Empty;

            if (string.IsNullOrWhiteSpace(configuredTenantId) || string.IsNullOrWhiteSpace(clientId) || string.IsNullOrWhiteSpace(clientSecret))
            {
                var msg = "Missing configuration (TenantId/AppId/AppSecret).";
                _logger.LogError(msg + " Ensure appsettings or environment variables are set.");
                return (false, null, msg);
            }

            var effectiveTenantId = string.IsNullOrWhiteSpace(meetingTenantId)
                ? (ExtractTidFromJoinUrl(joinWebUrl) ?? configuredTenantId)
                : meetingTenantId;

            var organizerId = ExtractOidFromJoinUrl(joinWebUrl);
            if (string.IsNullOrWhiteSpace(organizerId))
            {
                return (false, null, "Organizer Oid not found in join URL context");
            }

            _logger.LogInformation("JoinMeetingAsGuest (simplified) using organizerMeetingInfo. Host={Host}", new Uri(joinWebUrl).Host);
            _logger.LogInformation("EffectiveTenantId={EffectiveTenantId}", effectiveTenantId);

            // Acquire token for effective tenant
            var app = ConfidentialClientApplicationBuilder
                .Create(clientId)
                .WithClientSecret(clientSecret)
                .WithAuthority($"https://login.microsoftonline.com/{effectiveTenantId}")
                .Build();

            var tr = await app.AcquireTokenForClient(new[] { "https://graph.microsoft.com/.default" }).ExecuteAsync();

            // Log token tid for diagnostics (best-effort)
            try
            {
                var parts = tr.AccessToken.Split('.');
                if (parts.Length > 1)
                {
                    string payload = parts[1].PadRight(parts[1].Length + (4 - parts[1].Length % 4) % 4, '=');
                    var bytes = Convert.FromBase64String(payload.Replace('-', '+').Replace('_', '/'));
                    using var doc = JsonDocument.Parse(bytes);
                    if (doc.RootElement.TryGetProperty("tid", out var tidProp))
                    {
                        _logger.LogInformation("Acquired token tid={TokenTid} for tenant={Tenant}", tidProp.GetString(), effectiveTenantId);
                    }
                }
            }
            catch { }

            // Prepare callback to Media Service if configured
            var mediaBase = _configuration["MediaService:BaseUrl"];
            var callbackBase = string.IsNullOrWhiteSpace(mediaBase) ? baseUrl : mediaBase;
            var callbackUri = string.IsNullOrWhiteSpace(callbackBase) ? null : $"{callbackBase}/api/calls/callback";

            // Build organizerMeetingInfo payload
            using var docPayload = BuildCreateCallJsonOrganizerMeetingInfo(
                callbackUri,
                "AccureMD Bot",
                organizerId!,
                effectiveTenantId!);

            var json = docPayload.RootElement.GetRawText();

            var client = _httpClientFactory.CreateClient();
            var request = new HttpRequestMessage(HttpMethod.Post, "https://graph.microsoft.com/v1.0/communications/calls");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tr.AccessToken);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            _logger.LogInformation("Posting create call (organizerMeetingInfo). tenantId included");
            var response = await client.SendAsync(request);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Create call (organizer) failed. Status={Status}, Body={Body}", (int)response.StatusCode, responseBody);
                return (false, null, $"Graph create call failed: {(int)response.StatusCode} {response.ReasonPhrase} | {responseBody}");
            }

            try
            {
                using var respDoc = JsonDocument.Parse(responseBody);
                var root = respDoc.RootElement;
                var callId = root.TryGetProperty("id", out var idProp) ? idProp.GetString() : null;
                _logger.LogInformation("Create call (organizer) succeeded. CallId={CallId}", callId);
                return (true, callId, "Joined meeting as guest");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse call id from Graph response (organizer)");
                return (true, null, "Joined meeting as guest (call id parse warning)");
            }
        }
        catch (MsalServiceException msalEx)
        {
            _logger.LogError(msalEx, "MSAL error acquiring app token: {ErrorCode}", msalEx.ErrorCode);
            return (false, null, $"Auth error: {msalEx.ErrorCode} - {msalEx.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error creating Graph call");
            return (false, null, ex.Message);
        }
    }

    private static string ExtractJoinMeetingId(string joinWebUrl)
    {
        // Typical Teams link: https://teams.microsoft.com/l/meetup-join/19%3ameeting_XXXXX%40thread.v2/0?context=...
        var m = Regex.Match(joinWebUrl, "meetup-join/([^/?]+)");
        if (m.Success)
        {
            var encoded = m.Groups[1].Value;
            try
            {
                return Uri.UnescapeDataString(encoded);
            }
            catch { return encoded; }
        }
        return string.Empty;
    }

    private static string? ExtractTidFromJoinUrl(string joinWebUrl)
    {
        // Parse context JSON from query (?context=...) and extract Tid
        var contextMatch = Regex.Match(joinWebUrl, "[?&]context=([^&]+)");
        if (!contextMatch.Success) return null;
        var encoded = contextMatch.Groups[1].Value;
        try
        {
            var json = Uri.UnescapeDataString(encoded);
            // Expect a JSON like: {"Tid":"<tenantId>","Oid":"<guid>"}
            using var doc = JsonDocument.Parse(json);
            if (doc.RootElement.TryGetProperty("Tid", out var tid))
                return tid.GetString();
        }
        catch { }
        return null;
    }
        private static string? ExtractOidFromJoinUrl(string joinWebUrl)
    {
        var contextMatch = Regex.Match(joinWebUrl, "[?&]context=([^&]+)");
        if (!contextMatch.Success) return null;
        var encoded = contextMatch.Groups[1].Value;
        try
        {
            var json = Uri.UnescapeDataString(encoded);
            using var doc = JsonDocument.Parse(json);
            if (doc.RootElement.TryGetProperty("Oid", out var oid))
                return oid.GetString();
        }
        catch { }
        return null;
    }

    private static JsonDocument BuildCreateCallJsonOrganizerMeetingInfo(string? callbackUri, string subject, string organizerOid, string tenantId)
    {
        using var stream = new MemoryStream();
        using (var writer = new Utf8JsonWriter(stream))
        {
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.call");

            if (!string.IsNullOrEmpty(callbackUri))
                writer.WriteString("callbackUri", callbackUri);

            writer.WritePropertyName("requestedModalities");
            writer.WriteStartArray();
            writer.WriteStringValue("audio");
            writer.WriteEndArray();

            writer.WritePropertyName("mediaConfig");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.serviceHostedMediaConfig");
            writer.WriteEndObject();

            // Root-level tenantId
            writer.WriteString("tenantId", tenantId);

            writer.WritePropertyName("meetingInfo");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.organizerMeetingInfo");
            writer.WritePropertyName("organizer");
            writer.WriteStartObject(); // identitySet
            writer.WritePropertyName("user");
            writer.WriteStartObject(); // identity
            writer.WriteString("@odata.type", "#microsoft.graph.identity");
            writer.WriteString("id", organizerOid);
            writer.WriteString("tenantId", tenantId);
            writer.WriteEndObject(); // identity
            writer.WriteEndObject(); // identitySet
            writer.WriteEndObject(); // meetingInfo

            writer.WriteString("subject", subject);

            writer.WriteEndObject();
        }
        stream.Position = 0;
        return JsonDocument.Parse(stream);
    } // end BuildCreateCallJsonOrganizerMeetingInfo

    private static JsonDocument BuildCreateCallJsonJoinMeetingId(string? callbackUri, string subject, string joinMeetingId, string? tenantId)
    {
        using var stream = new MemoryStream();
        using (var writer = new Utf8JsonWriter(stream))
        {
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.call");

            if (!string.IsNullOrEmpty(callbackUri))
                writer.WriteString("callbackUri", callbackUri);

            writer.WritePropertyName("requestedModalities");
            writer.WriteStartArray();
            writer.WriteStringValue("audio");
            writer.WriteEndArray();

            writer.WritePropertyName("mediaConfig");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.serviceHostedMediaConfig");
            writer.WritePropertyName("preFetchMedia");
            writer.WriteStartArray();
            writer.WriteEndArray();
            writer.WriteEndObject();

            // Include root-level tenantId when provided
            if (!string.IsNullOrEmpty(tenantId))
                writer.WriteString("tenantId", tenantId);

            writer.WritePropertyName("meetingInfo");
            writer.WriteStartObject();
            writer.WriteString("@odata.type", "#microsoft.graph.joinMeetingIdMeetingInfo");
            writer.WriteString("joinMeetingId", joinMeetingId);
            if (!string.IsNullOrEmpty(tenantId))
                writer.WriteString("tenantId", tenantId);
            writer.WriteEndObject();

            writer.WriteString("subject", subject);

            writer.WriteEndObject();
        }
        stream.Position = 0;
        return JsonDocument.Parse(stream);
    }

	    private static JsonDocument BuildCreateCallJsonJoinWebUrl(string? callbackUri, string subject, string joinWebUrl)
	    {
	        using var stream = new MemoryStream();
	        using (var writer = new Utf8JsonWriter(stream))
	        {
	            writer.WriteStartObject();
	            writer.WriteString("@odata.type", "#microsoft.graph.call");

	            if (!string.IsNullOrEmpty(callbackUri))
	                writer.WriteString("callbackUri", callbackUri);

	            writer.WritePropertyName("requestedModalities");
	            writer.WriteStartArray();
	            writer.WriteStringValue("audio");
	            writer.WriteEndArray();

	            writer.WritePropertyName("mediaConfig");
	            writer.WriteStartObject();
	            writer.WriteString("@odata.type", "#microsoft.graph.serviceHostedMediaConfig");
	            writer.WritePropertyName("preFetchMedia");
	            writer.WriteStartArray();
	            writer.WriteEndArray();
	            writer.WriteEndObject();

	            writer.WritePropertyName("meetingInfo");
	            writer.WriteStartObject();
	            writer.WriteString("@odata.type", "#microsoft.graph.joinWebUrlMeetingInfo");
	            writer.WriteString("joinWebUrl", joinWebUrl);
	            writer.WriteEndObject();

	            writer.WriteString("subject", subject);

	            writer.WriteEndObject();
	        }
	        stream.Position = 0;
	        return JsonDocument.Parse(stream);
	    }
}
