using System;
using Microsoft.Graph.Communications.Calls;
using Microsoft.Graph.Communications.Calls.Media;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Skype.Bots.Media;

namespace AccureMD.MediaBot.Worker.Services
{
    // Minimal call handler to attach to created calls and wire LocalMediaSession events
    public class CallHandler
    {
        private readonly ICall _call;
        private readonly ILocalMediaSession _mediaSession;
        private readonly IGraphLogger _logger;

        public CallHandler(ICall call, ILocalMediaSession mediaSession, IGraphLogger logger)
        {
            _call = call;
            _mediaSession = mediaSession;
            _logger = logger;

            // Wire audio socket for inbound audio
            var audioSocket = _mediaSession.AudioSocket;
            if (audioSocket != null)
            {
                audioSocket.AudioMediaReceived += AudioSocket_AudioMediaReceived;
                _logger.Info($"[CallHandler] Audio socket wired for call {_call.Id}");
            }
        }

        private void AudioSocket_AudioMediaReceived(object sender, AudioMediaReceivedEventArgs e)
        {
            try
            {
                // e.Buffer contains PCM audio in the negotiated format
                _logger.Verbose($"[CallHandler] Audio received: {e.Buffer?.Length} bytes, Timestamp={e.Buffer?.Timestamp}");
                // TODO: forward to ASR pipeline if needed
            }
            catch (Exception ex)
            {
                _logger.Warn(ex, "[CallHandler] Error processing received audio");
            }
        }
    }
}

