# AccureMD Teams Media Service

This service provides real-time media streaming capabilities for Microsoft Teams meetings, enabling audio capture and processing for transcription and analysis.

## Features

- Join Teams meetings as a bot with media streaming capabilities
- Real-time audio stream processing
- Graph API integration for call management
- Callback handling for Teams events
- Docker support for Linux deployment
- RESTful API for meeting management

## Prerequisites

### Azure AD App Registration
1. **Application Permissions Required:**
   - `Calls.AccessMedia.All`
   - `Calls.JoinGroupCallAsGuest.All`
   - `Calls.JoinGroupCall.All`
   - `Calls.Initiate.All`

2. **Admin Consent:** Ensure all permissions have admin consent granted

3. **Application Access Policy:**
   ```powershell
   New-CsApplicationAccessPolicy -Identity "MediaBotPolicy" -AppIds "your-app-id" -Description "Policy for AccureMD Media Bot"
   Grant-CsApplicationAccessPolicy -PolicyName "MediaBotPolicy" -Global
   ```

### Environment Requirements
- Linux environment (required for real-time media processing)
- Docker support
- UDP traffic capability on port 8445
- HTTPS endpoint for callbacks

## Configuration

Update `appsettings.json` with your specific values:

```json
{
  "BaseUrl": "https://your-media-service-url.azurewebsites.net",
  "Teams": {
    "AppId": "your-app-id",
    "AppSecret": "your-app-secret",
    "TenantId": "your-tenant-id"
  },
  "MediaPlatform": {
    "ServiceDnsName": "your-media-service-url.azurewebsites.net",
    "ServiceCname": "your-media-service-url.azurewebsites.net"
  }
}
```

## Deployment

### Windows VM Deployment (accuremd.eastus.cloudapp.azure.com)

Follow these steps to deploy the Media Service on your Windows VM with DNS accuremd.eastus.cloudapp.azure.com.

1) Prerequisites on the VM
- Windows Server 2019/2022
- Public DNS: accuremd.eastus.cloudapp.azure.com
- Inbound NSG/Firewall: UDP 8445 open, TCP 443 open; outbound UDP allowed
- Latest .NET 8 Hosting Bundle installed (includes ASP.NET Core Module V2)

2) TLS Certificate
- Obtain a publicly trusted certificate for accuremd.eastus.cloudapp.azure.com
- Import to LocalMachine\\My
- Copy its Thumbprint (no spaces)

3) Configure the app
- Edit Backend/AccureMD.TeamsBot.MediaService/appsettings.json:
  - BaseUrl: https://accuremd.eastus.cloudapp.azure.com
  - MediaPlatform:ServiceDnsName: accuremd.eastus.cloudapp.azure.com
  - MediaPlatform:ServiceCname: accuremd.eastus.cloudapp.azure.com
  - MediaPlatform:CertificateThumbprint: <YOUR_CERT_THUMBPRINT>
  - Teams:AppId/Secret/TenantId: verify values

4) IIS site and binding
- Ensure IIS is installed and the "Default Web Site" is present
- Copy the published build to the site’s physical path (e.g., C:\inetpub\wwwroot)
- Ensure web.config is deployed (already included in project)

5) Automate firewall and binding (optional)
- Run the setup script as Administrator on the VM:
  PowerShell (elevated):

  PowerShell
  ./deploy/setup-media-vm.ps1 -DnsName accuremd.eastus.cloudapp.azure.com -CertThumbprint <YOUR_CERT_THUMBPRINT> -UdpPort 8445

6) Publish and deploy
- On your dev machine:
  - Build Release publish for AccureMD.TeamsBot.MediaService
  - Copy the publish folder contents to the VM (RDP or CI/CD)

7) Validate
- Browse https://accuremd.eastus.cloudapp.azure.com/api/meeting/health
- You should see JSON with status=healthy

8) Integrate with existing Backend
- In Backend/appsettings.json, set:

  JSON
  "MediaService": {
    "BaseUrl": "https://accuremd.eastus.cloudapp.azure.com"
  }

- The Backend now calls the Media Service to create the call; no changes needed in Teams bot messaging.

9) Graph callback
- The Media Service automatically sets callbackUri to https://accuremd.eastus.cloudapp.azure.com/api/calls/callback
- Ensure the service is reachable over HTTPS from the internet

10) End-to-end test
- Trigger a join from your existing app (it will call Media Service /api/meeting/join)
- Watch Media Service logs; you should see 201 Created from Graph and subsequent callbacks
- If you receive terminated with 2203, re-verify:
  - TLS cert matches DNS, thumbprint set
  - UDP 8445 inbound/outbound allowed
  - VM public DNS resolves and is reachable

Notes
- Linux/Docker is not supported for Microsoft.Skype.Bots.Media. Use Windows VM/IIS.
- Callback endpoint returns 202 Accepted to support async processing.
- The Media Service uses joinWebUrlMeetingInfo for reliability.

## API Endpoints

### Join Meeting
```http
POST /api/meeting/join
Content-Type: application/json

{
  "meetingUrl": "https://teams.microsoft.com/l/meetup-join/...",
  "tenantId": "optional-tenant-id",
  "displayName": "AccureMD Media Bot"
}
```

### Get Call State
```http
GET /api/meeting/call/{callId}/state
```

### Get Active Calls
```http
GET /api/meeting/calls/active
```

### Health Check
```http
GET /api/meeting/health
```

### Graph Callback (Internal)
```http
POST /api/calls/callback
```

## Usage Flow

1. **Join Meeting:** Call the `/api/meeting/join` endpoint with a Teams meeting URL
2. **Monitor State:** Use `/api/meeting/call/{callId}/state` to monitor call status
3. **Process Media:** The service automatically processes incoming audio streams
4. **Handle Events:** Graph callbacks are processed automatically for call state changes

## Media Processing

The service handles:
- Real-time audio stream capture
- Audio format conversion (SILK/Opus to PCM)
- Stream recording to files
- Integration points for transcription services

## Monitoring and Logging

- Comprehensive logging for all operations
- Call state tracking and event history
- Health check endpoint for monitoring
- Swagger UI available at root URL in development

## Security Considerations

- Use HTTPS for all endpoints
- Secure storage of app secrets
- Network security for UDP media ports
- Regular token refresh handling

## Troubleshooting

### Common Issues

1. **Policy Propagation:** Application access policies can take several hours to propagate
2. **UDP Connectivity:** Ensure UDP port 8445 is accessible
3. **Certificate Issues:** Verify SSL/TLS configuration for callbacks
4. **Token Errors:** Check app registration permissions and secrets

### Logs

Check application logs for detailed error information:
```bash
docker logs container-name
```

## Integration with Existing Teams Bot

This media service is designed to work alongside your existing Teams bot. The main bot can trigger media streaming by calling this service's API endpoints when media processing is needed.

## Development

### Local Development
1. Update `appsettings.Development.json` with your configuration
2. Run with `dotnet run`
3. Access Swagger UI at `https://localhost:5001`

### Testing
Use the provided HTTP file (`AccureMD.TeamsBot.MediaService.http`) for API testing.
