# Teams Authentication Production Fix

## Problem Analysis

The Teams authentication was failing in production with HTTP 400 error. After investigation, the issue was identified as an incorrect authentication flow implementation for Teams popup context.

The error occurred when trying to access:
```
https://accuremd.azurewebsites.net/api/auth/start-teams-login?userId=5f2cd7...&redirectUri=https%3A%2F%2Faccuremd.azurewebsites.net%2Fhtml%2Fauth-callback.html
```

## Root Causes

1. **Incorrect Teams Authentication Flow**: The Teams app was calling a server-side redirect endpoint (`/api/auth/start-teams-login`) from within a Teams authentication popup, which doesn't work properly.

2. **Missing Redirect URI in Azure App Registration**: The redirect URI `https://accuremd.azurewebsites.net/html/auth-callback.html` is not registered in your Azure App Registration.

3. **Missing CORS Configuration**: Teams authentication requires proper CORS setup for cross-origin requests.

4. **Popup Context Issue**: Server-side redirects don't work properly within Teams authentication popups.

## Azure App Registration Configuration Steps

### Step 1: Add Redirect URIs

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Find your app with ID: `24a397f4-16dd-4dae-8b8f-5368c3a81fed`
4. Go to **Authentication** section
5. Under **Redirect URIs**, add these URIs:
   ```
   https://accuremd.azurewebsites.net/html/auth-callback.html
   https://accuremd.azurewebsites.net/api/auth/callback
   https://accuremd.azurewebsites.net/html/auth-start.html
   ```
6. Set **Redirect URI type** to **Web**
7. Click **Save**

### Step 2: Configure API Permissions

1. In your app registration, go to **API permissions**
2. Ensure these permissions are granted:
   ```
   Microsoft Graph:
   - User.Read (Delegated)
   - OnlineMeetings.ReadWrite (Delegated)
   - Calendars.Read (Delegated)
   - openid (Delegated)
   - profile (Delegated)
   - email (Delegated)
   ```
3. Click **Grant admin consent** for your tenant

### Step 3: Configure Authentication Settings

1. In **Authentication** section:
   - Enable **Access tokens** (used for implicit flows)
   - Enable **ID tokens** (used for OpenID Connect flows)
   - Under **Supported account types**, ensure it's set to **Single tenant** (matches your config)

### Step 4: Configure Teams App Manifest

1. Update your Teams app manifest to include the correct domains:
   ```json
   "validDomains": [
     "accuremd.azurewebsites.net",
     "login.microsoftonline.com",
     "graph.microsoft.com"
   ]
   ```

### Step 5: Verify Client Secret

1. In **Certificates & secrets** section
2. Ensure your client secret `****************************************` is still valid
3. If expired, create a new one and update your configuration

## Code Changes Made

### 1. Fixed Teams Authentication Flow (teams-app.js)
**BEFORE** (Incorrect - using server endpoint):
```javascript
const startLoginUrl = `${window.location.origin}/api/auth/start-teams-login?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;
```

**AFTER** (Correct - using HTML page):
```javascript
const startLoginUrl = `${window.location.origin}/html/auth-start.html?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;
```

### 2. Enhanced auth-start.html to Accept Parameters
- Added URL parameter parsing for userId and redirectUri
- Used specific tenant ID instead of 'common'
- Proper parameter handling for Teams context

### 3. Added CORS Configuration (Program.cs)
```csharp
// Add CORS for Teams authentication
builder.Services.AddCors(options =>
{
    options.AddPolicy("TeamsPolicy", policy =>
    {
        policy.WithOrigins(
            "https://teams.microsoft.com",
            "https://teams.microsoft.us",
            "https://login.microsoftonline.com",
            "https://accuremd.azurewebsites.net"
        )
        .AllowAnyMethod()
        .AllowAnyHeader()
        .AllowCredentials();
    });
});
```

### 4. Enhanced Error Handling (AuthenticationService.cs)
- Added redirect URI validation
- Added HTTPS enforcement for production
- Better error logging

### 5. Improved Logging (AuthController.cs)
- Added configuration logging for debugging
- Enhanced error messages
- Better parameter validation

## Testing Steps

1. **Deploy the updated code** to Azure App Service
2. **Test the configuration endpoint**:
   ```
   GET https://accuremd.azurewebsites.net/api/auth/test
   ```
3. **Test Teams authentication flow**:
   - Open your Teams app
   - Try to authenticate
   - Check the browser console for detailed logs

## Debugging Commands

If issues persist, check these endpoints:

1. **Configuration test**:
   ```
   curl https://accuremd.azurewebsites.net/api/auth/test
   ```

2. **Manual auth URL generation**:
   ```
   curl "https://accuremd.azurewebsites.net/api/auth/start-teams-login?userId=test&redirectUri=https%3A%2F%2Faccuremd.azurewebsites.net%2Fhtml%2Fauth-callback.html"
   ```

## Why This Fix Works

### The Problem
The original implementation tried to use a server-side redirect endpoint (`/api/auth/start-teams-login`) within a Teams authentication popup. This doesn't work because:

1. **Popup Context**: Teams authentication popups expect to load an HTML page, not receive a server redirect
2. **Model Binding**: The server endpoint was designed for different use cases
3. **Flow Mismatch**: Server redirects don't work properly in popup authentication flows

### The Solution
The fix changes the authentication flow to use a client-side HTML page (`/html/auth-start.html`) that:

1. **Loads in Popup**: Works properly within Teams authentication popup context
2. **Handles Parameters**: Accepts userId and redirectUri from query string
3. **Client-Side Redirect**: Performs the OAuth redirect from JavaScript
4. **Proper Flow**: Follows the correct OAuth flow for popup authentication

## Expected Results

After implementing these fixes:
- Teams authentication popup will load the auth-start.html page correctly
- The page will redirect to Microsoft login with proper parameters
- Users should be able to complete the OAuth flow
- The callback should work correctly
- Authentication status should persist in the database

## Common Issues and Solutions

1. **Still getting 400 error**: Double-check redirect URIs in Azure App Registration
2. **CORS errors**: Ensure all Teams domains are in CORS policy
3. **Token validation errors**: Verify client secret and tenant ID
4. **Callback not working**: Check that auth-callback.html is accessible
5. **Popup blocked**: Ensure popup blockers are disabled for Teams

## Next Steps

1. Apply Azure App Registration changes (add redirect URIs)
2. Deploy the code updates
3. Test the authentication flow in Teams
4. Monitor browser console for detailed logs
5. Verify authentication persistence in database
