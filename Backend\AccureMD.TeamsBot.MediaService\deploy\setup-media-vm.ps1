param(
  [Parameter(Mandatory=$true)][string]$DnsName,
  [Parameter(Mandatory=$true)][string]$CertThumbprint,
  [int]$UdpPort = 8445
)

Write-Host "Configuring Windows VM for Teams Media Service..." -ForegroundColor Cyan

# Open Windows Firewall for UDP port
Write-Host "Opening Windows Firewall UDP port $UdpPort..."
try {
  New-NetFirewallRule -DisplayName "Teams Media UDP $UdpPort" -Direction Inbound -Protocol UDP -LocalPort $UdpPort -Action Allow -ErrorAction Stop | Out-Null
} catch {}

# Verify certificate exists
Write-Host "Checking certificate with thumbprint $CertThumbprint in LocalMachine\My..."
$cert = Get-ChildItem -Path Cert:\LocalMachine\My | Where-Object { $_.Thumbprint -eq $CertThumbprint }
if (-not $cert) {
  throw "Certificate with thumbprint $CertThumbprint not found in LocalMachine\\My. Install a public cert for $DnsName."
}

Write-Host "Certificate found: $($cert.Subject)"

# Create IIS site binding if needed (assumes site name 'Default Web Site')
Import-Module WebAdministration
$siteName = "Default Web Site"

# Ensure HTTPS binding exists
$binding = Get-WebBinding -Name $siteName -Protocol https -ErrorAction SilentlyContinue
if (-not $binding) {
  Write-Host "Creating HTTPS binding for $DnsName..."
  New-WebBinding -Name $siteName -Protocol https -Port 443 -HostHeader $DnsName
}

# Assign certificate to binding
$ipPort = "0.0.0.0!443"
Write-Host "Assigning certificate to 443 binding..."
& netsh http add sslcert ipport=$ipPort certhash=$CertThumbprint appid="{00000000-0000-0000-0000-000000000000}" certstorename=MY

Write-Host "IIS binding configuration complete."

Write-Host "Done." -ForegroundColor Green
