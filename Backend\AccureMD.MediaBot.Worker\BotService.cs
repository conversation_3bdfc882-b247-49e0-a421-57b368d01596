using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Identity.Client;
using Newtonsoft.Json.Linq;
using System.Web.Http;
using Microsoft.Graph.Communications.Calls;
using Microsoft.Graph.Communications.Client;
using Microsoft.Graph.Communications.Common;

namespace AccureMD.MediaBot.Worker
{
    public class BotService
    {
        private readonly string _appId;
        private readonly string _appSecret;
        private readonly string _tenantId;
        private readonly string _publicBaseUrl; // e.g., https://accuremd.eastus.cloudapp.azure.com/mediabot

        public BotService()
        {
            _appId = GetConfig("AppId") ?? throw new InvalidOperationException("AppId missing");
            _appSecret = GetConfig("AppSecret") ?? throw new InvalidOperationException("AppSecret missing");
            _tenantId = GetConfig("TenantId") ?? throw new InvalidOperationException("TenantId missing");
            _publicBaseUrl = ((GetConfig("BotBaseUrl") ?? string.Empty).TrimEnd('/')) + "/mediabot";
        }

        public async Task<(bool ok, string callId, string message)> JoinViaJoinWebUrlAsync(string joinWebUrl, string displayName)
        {
            try
            {
                var effectiveTenantId = ExtractTid(joinWebUrl) ?? _tenantId;
                if (string.IsNullOrWhiteSpace(effectiveTenantId))
                    return (false, null, "Could not determine tenant ID from URL or configuration");

                var token = await AcquireAppTokenAsync(effectiveTenantId);
                var callbackUri = _publicBaseUrl + "/notifications";
                Console.WriteLine($"[MediaBot] Using callbackUri: {callbackUri}");

                // Application-hosted media: use appHostedMediaConfig and organizerMeetingInfo
                var organizerOid = ExtractOid(joinWebUrl);
                var threadId = ExtractThreadId(joinWebUrl);
                if (string.IsNullOrWhiteSpace(organizerOid))
                    return (false, null, "Organizer OID not found in join URL context");

                var root = new JObject
                {
                    ["@odata.type"] = "#microsoft.graph.call",
                    ["callbackUri"] = callbackUri,
                    ["requestedModalities"] = new JArray("audio"),
                    ["mediaConfig"] = new JObject
                    {
                        ["@odata.type"] = "#microsoft.graph.appHostedMediaConfig",
                        // Per Microsoft docs, appHostedMediaConfig requires a 'blob' with media session configuration
                        // The CommunicationsClient + MediaPlatform settings will generate the configuration. For REST, include an empty string if SDK generates it server-side.
                        ["blob"] = ""
                    },
                    ["tenantId"] = effectiveTenantId,
                    ["meetingInfo"] = new JObject
                    {
                        ["@odata.type"] = "#microsoft.graph.organizerMeetingInfo",
                        ["organizer"] = new JObject
                        {
                            ["@odata.type"] = "#microsoft.graph.identitySet",
                            ["user"] = new JObject
                            {
                                ["@odata.type"] = "#microsoft.graph.identity",
                                ["id"] = organizerOid,
                                ["tenantId"] = effectiveTenantId
                            }
                        }
                    },
                    ["source"] = new JObject
                    {
                        ["@odata.type"] = "#microsoft.graph.participantInfo",
                        ["identity"] = new JObject
                        {
                            ["@odata.type"] = "#microsoft.graph.identitySet",
                            ["application"] = new JObject
                            {
                                ["@odata.type"] = "#microsoft.graph.identity",
                                ["displayName"] = string.IsNullOrWhiteSpace(displayName) ? "AccureMD Media Bot" : displayName,
                                ["id"] = _appId
                            }
                        }
                    },
                    ["subject"] = string.IsNullOrWhiteSpace(displayName) ? "AccureMD Media Join" : displayName,
                    // Include chatInfo if we could parse threadId from the join URL (helps Graph route correctly)
                    ["chatInfo"] = string.IsNullOrEmpty(threadId) ? null : new JObject
                    {
                        ["@odata.type"] = "#microsoft.graph.chatInfo",
                        ["threadId"] = threadId
                    }
                };

                var json = root.ToString();

                using (var client = new HttpClient())
                {
                    var req = new HttpRequestMessage(HttpMethod.Post, "https://graph.microsoft.com/v1.0/communications/calls");
                    req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    req.Content = new StringContent(json, Encoding.UTF8, "application/json");

                    var resp = await client.SendAsync(req);
                    var body = await resp.Content.ReadAsStringAsync();
                    if (!resp.IsSuccessStatusCode)
                    {
                        return (false, null, $"Graph create call failed: {(int)resp.StatusCode} {resp.ReasonPhrase}. Body={body}");
                    }
                    var parsed = JObject.Parse(body);
                    var callId = parsed.Value<string>("id");
                    return (true, callId, "Call created");
                }
            }
            catch (Exception ex)
            {
                return (false, null, ex.Message);
            }
        }

        private static string GetConfig(string key)
        {
            return System.Configuration.ConfigurationManager.AppSettings[key];
        }

        private async Task<string> AcquireAppTokenAsync(string tenantId)
        {
            var app = ConfidentialClientApplicationBuilder
                .Create(_appId)
                .WithClientSecret(_appSecret)
                .WithAuthority($"https://login.microsoftonline.com/{tenantId}")
                .Build();

            var tr = await app.AcquireTokenForClient(new[] { "https://graph.microsoft.com/.default" }).ExecuteAsync();
            return tr.AccessToken;
        }

        public static string ExtractTid(string joinUrl)
        {
            var m = Regex.Match(joinUrl, "[?&]context=([^&]+)");
            if (!m.Success) return null;
            try
            {
                var json = Uri.UnescapeDataString(m.Groups[1].Value);
                var obj = JObject.Parse(json);
                return (string)obj["Tid"];
            }
            catch { return null; }
        }

        public static string ExtractOid(string joinUrl)
        {
            var m = Regex.Match(joinUrl, "[?&]context=([^&]+)");
            if (!m.Success) return null;
            try
            {
                var json = Uri.UnescapeDataString(m.Groups[1].Value);
                var obj = JObject.Parse(json);
                return (string)obj["Oid"];
            }
            catch { return null; }
        }

        public static string ExtractThreadId(string joinUrl)
        {
            // ThreadId is the meeting id prefixed with 19:meeting_...@thread.v2 typically in the join URL path
            var m = Regex.Match(joinUrl, @"/l/meetup-join/([^/]+)@thread\.v2", RegexOptions.IgnoreCase);
            if (m.Success)
            {
                var encoded = m.Groups[1].Value; // e.g., 19%3ameeting_... (URL encoded)
                var decoded = Uri.UnescapeDataString(encoded);
                return decoded + "@thread.v2";
            }
            return null;
        }
    }
}

