using System;
using System.Threading.Tasks;
using System.Web.Http;
using Microsoft.Graph.Communications.Client;

namespace AccureMD.MediaBot.Worker.Controllers
{
    [RoutePrefix("mediabot/notifications")]
    public class NotificationsController : ApiController
    {
        [HttpGet, Route("")]
        public IHttpActionResult Get([FromUri] string validationToken = null)
        {
            if (!string.IsNullOrEmpty(validationToken))
            {
                Console.WriteLine($"[MediaBot] Validation request received with token: {validationToken}");
                return new System.Web.Http.Results.ResponseMessageResult(
                    new System.Net.Http.HttpResponseMessage(System.Net.HttpStatusCode.OK)
                    {
                        Content = new System.Net.Http.StringContent(validationToken, System.Text.Encoding.UTF8, "text/plain")
                    });
            }
            return Ok();
        }

        [HttpPost, Route("")]
        public async Task<IHttpActionResult> Post()
        {
            // Log raw body for troubleshooting
            var body = await Request.Content.ReadAsStringAsync();
            Console.WriteLine($"[MediaBot] Graph callback received at /mediabot/notifications: {body}");

            var host = (CommunicationsHost)this.Configuration.Properties[typeof(CommunicationsHost)];
            await host.Client.ProcessNotificationAsync(Request);
            // Return 200 OK per Microsoft guidance to acknowledge the notification
            return Ok();
        }
    }
}

