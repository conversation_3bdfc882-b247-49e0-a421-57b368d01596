using System;
using System.Threading.Tasks;
using Microsoft.Graph;
using Microsoft.Graph.Communications.Calls;
using Microsoft.Graph.Communications.Calls.Media;
using Microsoft.Graph.Communications.Client;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Skype.Bots.Media;

namespace AccureMD.MediaBot.Worker.Services
{
    public class SdkCallService
    {
        private readonly ICommunicationsClient _client;
        private readonly IGraphLogger _logger;
        private readonly string _callbackUri;

        public SdkCallService(ICommunicationsClient client, string callbackUri, IGraphLogger logger = null)
        {
            _client = client;
            _callbackUri = callbackUri;
            _logger = logger ?? new GraphLogger(typeof(SdkCallService).Assembly.GetName().Name);
        }

        public async Task<(bool ok, string callId, string message)> JoinByOrganizerAsync(string tenantId, string organizerObjectId, string displayName)
        {
            try
            {
                // Ensure token acquisition is tenant-aligned
                Authentication.TenantContext.CurrentTenant = tenantId;

                // Create a local media session (audio only). SDK will derive appHostedMediaConfig blob.
                var audioSettings = new AudioSocketSettings();
                var mediaSession = _client.CreateMediaSession(audioSettings);

                var call = new Call
                {
                    CallbackUri = _callbackUri,
                    RequestedModalities = new[] { Modality.Audio },
                    TenantId = tenantId,
                    MediaConfig = new AppHostedMediaConfig(),
                    MeetingInfo = new OrganizerMeetingInfo
                    {
                        Organizer = new IdentitySet
                        {
                            User = new Identity
                            {
                                Id = organizerObjectId,
                                DisplayName = displayName
                            }
                        }
                    },
                    Subject = string.IsNullOrWhiteSpace(displayName) ? "AccureMD Media Join" : displayName
                };

                var created = await _client.Calls().AddAsync(call, mediaSession).ConfigureAwait(false);

                // Attach call handler to wire media events
                var handler = new CallHandler(created, mediaSession, _logger);

                return (true, created.Id, "Call created via SDK");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating call via SDK");
                return (false, null, ex.Message);
            }
            finally
            {
                // Clear the tenant context
                Authentication.TenantContext.CurrentTenant = null;
            }
        }


    }
}

