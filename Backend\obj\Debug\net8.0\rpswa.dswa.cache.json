{"GlobalPropertiesHash": "ITtAFkNiSgdhUb2BwCQUf6iyFsK/CwOgeB9JuCIv+uU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["krYWps1CzkR5Ooh9BYy7m+6VhUh9fpaKyhRCEfueQzc=", "mPX9KepOnQKwJvrnxMgYSY/ut7EhcOYfSVENXYHkITs=", "7XusbHBJIpUyvY/iS11FnagLtJGOz/0s4axvc71rEus=", "FU2jzAoNm6iBK+2mM+KL2Zrtp2luW6iNX/AGlgY1yYo=", "2jlAfMeKZHTI2Gc6wHbI/9fwd5IM2YX/UhKrjfhLUqk=", "IgWOXbLysaRw3oDUNGIYgen6Dvslg4FLoApxNDP1Ouk=", "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=", "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=", "cBnaSEnyODVTBjN4rStBz0rTFKNHS/ud/Si5OEF93ks=", "v9Qezx+Sdp4qPOXw3vYcHeGRHoLNNoXWv0yxCInWmqs=", "9pLLrzTrPJrsPZWKdVAmfryJUMVoCAoYUlM8PE3AfAA=", "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=", "PfSy9iEmWCZmTlXtjhNtqpGcckvmuVG0YKHQNxHLUyE=", "qFlo+EWtmLm+4U7Gxv51oXPqVQeMlk06V4S/Hpw/l8A=", "keVAT7wQGM9+0Y97CPc43YVrJx3WCGpxtwHJC+8+xd8=", "wrYoMC2thk9gjtVBiTFBSi9kjDdglq/9SdTpsRNzaKU=", "RkMKh3yIlUSSZHxaSVTXxwo4nUTZH7bReDPN1vpNuFk=", "LYeYaZIxD8dS9XqOdHFNcdAM4CJdDHMyU0s4D7GLZfA=", "TdwhZMVmDfoc5pgh+XXTvHFcw/L+EuYFLYfqYH4RuvU="], "CachedAssets": {"krYWps1CzkR5Ooh9BYy7m+6VhUh9fpaKyhRCEfueQzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "css/teams-app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vwl5012ydz", "Integrity": "0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\teams-app.css", "FileLength": 11424, "LastWriteTime": "2025-08-08T19:01:32.3671149+00:00"}, "mPX9KepOnQKwJvrnxMgYSY/ut7EhcOYfSVENXYHkITs=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-callback#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q6j5iap63k", "Integrity": "nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-callback.html", "FileLength": 28396, "LastWriteTime": "2025-08-10T18:39:37.7322432+00:00"}, "7XusbHBJIpUyvY/iS11FnagLtJGOz/0s4axvc71rEus=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-start#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ksq8v2n24n", "Integrity": "XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-start.html", "FileLength": 6131, "LastWriteTime": "2025-08-10T12:33:49.4841474+00:00"}, "FU2jzAoNm6iBK+2mM+KL2Zrtp2luW6iNX/AGlgY1yYo=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zbbusfory5", "Integrity": "3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-test.html", "FileLength": 11310, "LastWriteTime": "2025-08-09T13:11:16.5535654+00:00"}, "2jlAfMeKZHTI2Gc6wHbI/9fwd5IM2YX/UhKrjfhLUqk=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/configure#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inynrhuf2", "Integrity": "gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\configure.html", "FileLength": 4092, "LastWriteTime": "2025-08-08T17:45:39.2143566+00:00"}, "IgWOXbLysaRw3oDUNGIYgen6Dvslg4FLoApxNDP1Ouk=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5cqt4i9h8l", "Integrity": "a/meDWy3DNvnGb/a1OEnuc7vKsp5/RFzckmJulzdb5U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\index.html", "FileLength": 6692, "LastWriteTime": "2025-08-10T16:14:28.5813582+00:00"}, "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/privacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9ukuo7vfri", "Integrity": "AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\privacy.html", "FileLength": 1322, "LastWriteTime": "2025-08-08T08:30:26.6394667+00:00"}, "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/termsofuse#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tv39flyyfq", "Integrity": "FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\termsofuse.html", "FileLength": 1168, "LastWriteTime": "2025-08-08T08:30:34.6547862+00:00"}, "cBnaSEnyODVTBjN4rStBz0rTFKNHS/ud/Si5OEF93ks=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\test-auth-fix.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/test-auth-fix#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ew7c11alxw", "Integrity": "AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\test-auth-fix.html", "FileLength": 9642, "LastWriteTime": "2025-08-10T12:36:26.2753017+00:00"}, "v9Qezx+Sdp4qPOXw3vYcHeGRHoLNNoXWv0yxCInWmqs=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "js/teams-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zozdymfjfk", "Integrity": "8x9bMylEvRg7ipKZ/a+RU49RlF+FsxUTWfddl9BUI7E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\teams-app.js", "FileLength": 20390, "LastWriteTime": "2025-08-12T10:58:55.8961076+00:00"}, "9pLLrzTrPJrsPZWKdVAmfryJUMVoCAoYUlM8PE3AfAA=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "teams-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cztlu2xxj7", "Integrity": "+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\teams-test.html", "FileLength": 6390, "LastWriteTime": "2025-08-08T19:44:49.8475713+00:00"}, "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2r3ito90zx", "Integrity": "fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-auth.html", "FileLength": 2140, "LastWriteTime": "2025-08-08T11:50:16.6719653+00:00"}, "PfSy9iEmWCZmTlXtjhNtqpGcckvmuVG0YKHQNxHLUyE=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-teams-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r54zswcdn2", "Integrity": "zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-teams-auth.html", "FileLength": 9705, "LastWriteTime": "2025-08-08T20:45:49.2592213+00:00"}}, "CachedCopyCandidates": {}}