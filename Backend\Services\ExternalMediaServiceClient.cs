using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace AccureMD.TeamsBot.Services;

public class ExternalMediaServiceClient
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ExternalMediaServiceClient> _logger;

    public ExternalMediaServiceClient(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<ExternalMediaServiceClient> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<(bool Success, string? CallId, string Message)> JoinMeetingAsync(string meetingUrl, string? tenantId = null, string? displayName = null)
    {
        try
        {
            var baseUrl = _configuration["MediaService:BaseUrl"];
            if (string.IsNullOrWhiteSpace(baseUrl))
            {
                return (false, null, "MediaService:BaseUrl is not configured");
            }

            var client = _httpClientFactory.CreateClient("MediaService");
            // Route to application-hosted media worker join endpoint
            var url = baseUrl.TrimEnd('/') + "/mediabot/join";

            var payload = new
            {
                meetingUrl,
                tenantId,
                displayName = displayName ?? "AccureMD Media Bot"
            };

            var json = JsonSerializer.Serialize(payload);
            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = new StringContent(json, Encoding.UTF8, "application/json")
            };

            _logger.LogInformation("Calling Media Service join endpoint: {Url}", url);
            var response = await client.SendAsync(request);
            var body = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Media Service join failed. Status={Status}, Body={Body}", (int)response.StatusCode, body);
                return (false, null, $"Media Service join failed: {(int)response.StatusCode} {response.ReasonPhrase} | {body}");
            }

            using var doc = JsonDocument.Parse(body);
            var root = doc.RootElement;
            var callId = root.TryGetProperty("callId", out var callIdEl) ? callIdEl.GetString() : null;
            var message = root.TryGetProperty("message", out var msgEl) ? msgEl.GetString() ?? "" : "";

            if (string.IsNullOrWhiteSpace(callId))
            {
                _logger.LogWarning("Media Service returned success HTTP but missing callId. Treating as failure. Body={Body}", body);
                return (false, null, string.IsNullOrWhiteSpace(message) ? "Media Service returned no callId" : message);
            }

            return (true, callId, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling Media Service join endpoint");
            return (false, null, ex.Message);
        }
    }
}
