Nice — you’ve picked the most portable way to make a Teams bot “just join” meetings in other orgs: act like an anonymous guest (the same flow a human uses in the browser). Below I’ll explain the architecture options, the hard parts, an actionable implementation plan, a compact Puppeteer + in-page recording example, and the things you must check (admin settings, lobby/OTP, legal).

I’ll cite Microsoft + tooling docs for the important points so you can follow up.

Short summary (TL;DR)
Build a small browser client (headless Chromium via P<PERSON>peteer / Playwright) that opens the Teams JoinWebUrl, fills the guest name, clicks through the pre-join UI, and joins the meeting as a guest. This requires the target tenant to allow anonymous joins (Teams admin setting). 
Microsoft Support
Microsoft Learn

To record / process audio/video, either (A) capture streams in-page (MediaRecorder / WebRTC forwarding) and send chunks to your backend, or (B) run Chromium with virtual audio devices on the host and capture system audio. MediaRecorder + WebSocket streaming is common. 
MDN Web Docs
Twilio

You must handle lobby admission, possible email-code verification, and clearly show consent/recording notice to participants. Admins can block anonymous joins or require verification; plan for both. 
Microsoft Learn
+1

Key constraints & admin settings you must handle
Tenant may block anonymous join — the tenant admin must enable “Anonymous users can join a meeting” or allow guest joins for the organizer’s policy. If not, your guest join will fail. Microsoft docs explain these meeting policy settings and that changes can take time to propagate. 
Microsoft Learn

Lobby / verification — some tenants require email one-time codes or put anonymous guests in the lobby until admitted. Design UX/automation for the host to admit the bot. 
Microsoft Learn

Recording consent / legality — Teams will show recording/participant notifications but you must still get any required consent under local law and tenant policy (display a clear notice and/or require host consent).

Less Graph access — as a guest you lose Graph Call APIs features (participant roster, programmatic mute, app-level recording) — acceptable tradeoff for cross-tenant compatibility.

(Examples of real services using the guest approach: Read.ai allows a user to paste a meeting link and then the assistant joins as a participant — see Read.ai docs for manual “paste link” flow.) 
support.read.ai
+1

Two common architectures to join as guest & capture audio
A — Browser-in-page capture + upload (recommended for cloud deployment)
Launch headless/full Chromium (Puppeteer/Playwright).

Navigate to JoinWebUrl.

Interact with pre-join UI (set display name, disable camera/mic or enable mic, click Join).

In the page, use navigator.mediaDevices.getUserMedia() to obtain the meeting media streams or intercept the remote tracks and:

Use MediaRecorder to blob audio chunks and stream them to your backend over WebSocket/HTTP for realtime ASR/transcription.

Or forward tracks to an SFU on your backend by creating a second RTCPeerConnection and adding the tracks (more advanced, lower latency). 
MDN Web Docs
Twilio

Pros: pure web approach, portable to many tenants.
Cons: intercepting remote audio reliably in headless contexts can be tricky.

B — System audio capture with virtual devices (reliable for audio fidelity)
Run Chrome with a virtual audio sink (PulseAudio/ALSA loopback on Linux; virtual audio cable on Windows). Configure Chrome to output meeting audio to that sink and capture that device server-side.

Use --use-file-for-fake-audio-capture / --use-fake-device-for-media-stream flags for tests, or real virtual devices for production. 
webrtc.github.io
Stack Overflow

Pros: captures exactly what participants hear (good audio quality).
Cons: more infra (virtual audio), harder to scale, fiddly cross-OS.

Practical implementation plan (step-by-step)
Admin checks / onboarding

Document for customers: “If you want automatic guest joins, please enable Anonymous joins in Teams admin and (optionally) set lobby bypass for organizers.” Provide PowerShell commands or UI steps. 
Microsoft Learn

Choose runtime

Node.js + Puppeteer or Playwright is common (good library support for Chromium flags). Use a managed container per meeting or pooled browser workers.

Chromium flags & device handling (for automation)

For testing you can use Chrome flags --use-fake-ui-for-media-stream and --use-fake-device-for-media-stream and --use-file-for-fake-audio-capture to auto-grant / simulate mic input. For production, prefer real/virtual devices (PulseAudio or Windows virtual audio). 
webrtc.github.io
Stack Overflow

Automate the join UI

With Puppeteer: open join URL, wait for pre-join page, fill guest name, click “Continue on this browser” / “Join now”, click through microphones/camera toggles. UI selectors change over time — put robust waits and fallback flows.

Handle lobby/admit flow

Wait for “You’re in the lobby” UI and optionally implement retry / alert the meeting host to admit the bot (via email/Slack/Teams message).

Capture audio

Option A: in-page MediaRecorder capturing navigator.mediaDevices.getUserMedia({audio:true}) or capturing remote audio tracks. Send chunks via WebSocket to your speech pipeline. 
MDN Web Docs
Twilio

Option B: use host virtual audio sink and capture the sink from the OS (e.g., PulseAudio, then feed to ASR). This is the most robust for capturing both remote voices and the bot’s audio mix.

Post-join lifecycle

Keep the session alive, detect disconnects, do graceful shutdown (stop recording, upload final blobs, optionally send transcript back to the meeting chat or store in db).

Scale & reliability

Run each meeting session in an isolated container (1 Chromium per meeting is typical). Monitor CPU/RAM. Use health checks and automated restart policies.

Example: minimal Puppeteer flow + in-page MediaRecorder → WebSocket
This is a compact, practical starting point (you’ll need to adapt selectors for Teams UI changes and production audio capture).

javascript
Copy
Edit
// server.js (Node.js)
const puppeteer = require('puppeteer');
const WS_SERVER = 'wss://your-backend.example/ws/meeting-audio';

async function joinMeetingAsGuest(joinUrl, guestName) {
  const browser = await puppeteer.launch({
    headless: false, // set true for headless; headless may behave differently with media
    args: [
      '--no-sandbox',
      '--disable-gpu',
      // For tests: auto grant mic/cam and feed fake devices (not for prod)
      '--use-fake-ui-for-media-stream',
      // '--use-file-for-fake-audio-capture=/path/to/mic.wav',
      // '--use-file-for-fake-video-capture=/path/to/video.y4m',
    ]
  });

  const page = await browser.newPage();

  // optionally set viewport/userAgent if needed
  await page.goto(joinUrl, { waitUntil: 'networkidle2' });

  // Inject guest name and proceed through pre-join (selectors will evolve)
  await page.evaluate((name) => {
    // NOTE: these selectors are illustrative only — you must inspect Teams' current DOM
    const nameInput = document.querySelector('input[placeholder="Enter name"]') ||
                      document.querySelector('input[aria-label*="Enter name"]');
    if (nameInput) nameInput.value = name;

    // click join/continue buttons; may need to trigger events
    const continueBtn = document.querySelector('button:contains("Continue on this browser")') ||
                        document.querySelector('button[aria-label*="Continue"]');
    if (continueBtn) continueBtn.click();
  }, guestName);

  // Wait until we are in the meeting (or in-lobby)
  await page.waitForFunction(
    () => document.querySelector('body') && (document.body.innerText.includes('You are in the lobby') || document.body.innerText.includes('Leave')),
    { timeout: 120000 }
  );

  // Start recording inside the page and send chunks to backend via WebSocket
  await page.exposeFunction('getWsServer', () => WS_SERVER);
  await page.evaluate(async () => {
    const ws = new WebSocket(await window.getWsServer());
    ws.binaryType = 'arraybuffer';

    async function startCapture() {
      // capture microphone (or the page could capture remote tracks if possible)
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
      const recorder = new MediaRecorder(stream, { mimeType: 'audio/webm;codecs=opus' });

      recorder.ondataavailable = (ev) => {
        if (ev.data && ev.data.size > 0 && ws.readyState === 1) {
          ev.data.arrayBuffer().then(buf => ws.send(buf));
        }
      };
      recorder.start(2000); // send chunks every 2s
    }

    // attempt to capture remote audio tracks: depends on Teams internals
    // fallback: capture local mic which could be loopbacked on host if using virtual audio devices
    startCapture().catch(e => console.error('capture failed', e));
  });

  return { browser, page };
}

// usage
joinMeetingAsGuest('https://teams.microsoft.com/l/meetup-join/xxxxx', 'Read Assistant')
  .catch(console.error);
Notes on the snippet:

The real Teams UI selectors are volatile — you’ll need to inspect and adapt the click/select logic.

In production, headless: true is attractive but some media or permission features behave differently in headless mode — test both.

For reliable capture of remote audio you likely need a virtual audio sink or to inject logic that forwards remote RTCPeerConnection tracks to your own RTCPeerConnection (advanced).

References for MediaRecorder usage and streaming chunks to a server: MDN / Twilio articles and examples. 
MDN Web Docs
Twilio

Chrome/Puppeteer flags for fake media during tests: WebRTC/Chromium docs (--use-fake-ui-for-media-stream, --use-fake-device-for-media-stream, --use-file-for-fake-audio-capture). 
webrtc.github.io
Stack Overflow

Handling specific hard problems (practical tips)
Lobby/Admission: If the bot sits in the lobby, build a notification channel (email/Teams message to host) and optionally attempt rejoin after admission. Consider asking customers to set the organizer to “bypass lobby for everyone” if they want fully automated joins. 
Microsoft Learn

Remote audio capture: The robust production method is virtual-audio-sink + capture device at OS level. Browser-only capture of remote tracks is possible but brittle across Teams web updates.

Headless quirks: headless Chrome may not support audio playback & device capture the same way; test headful and headless.

Scaling: 1 browser per meeting; use containers, autoscale workers and keep the session short-lived.

Detect if anonymous join is blocked: the pre-join will show the “you can’t join” UI — detect that and report helpful instructions.

Compliance & user experience
Always display to meeting participants (and the host) that an assistant/recorder has joined and is recording/transcribing. Read.ai shows participant notification and often requires host approval; replicate that UX. 
support.read.ai
+1

Keep retention, access, and deletion policies clear for customers.

Recommended next steps (concrete)
Decide capture method:

If you need remote audio reliably: use virtual audio sink approach (B).

If you want simpler deployment and can accept occasional fragility: go with in-page MediaRecorder + websockets (A).

Prototype one meeting with Puppeteer using --use-fake-ui-for-media-stream locally; iterate on DOM selectors until join is robust.

Replace fake devices with a real/virtual mic device on the container host for production.

Add lobby/admit detection and host notification flow.

Test with multiple tenant configurations (OTP, lobby, blocked anonymous).

Sources / reading (important tech docs)
Microsoft: Join a Teams meeting without an account (how guest join behaves). 
Microsoft Support

Microsoft: Manage anonymous participant access / meeting policies (admin toggles, verification, 24-hour propagation). 
Microsoft Learn

Read.ai support docs showing paste-a-link manual join UX and behavior (real-world example). 
support.read.ai
+1

WebRTC testing flags and fake devices (Chrome/WeRTC docs) — --use-fake-ui-for-media-stream, --use-fake-device-for-media-stream. 
webrtc.github.io
Stack Overflow

MediaRecorder / recording in-browser guides (MDN, Twilio blog). 
MDN Web Docs
Twilio

