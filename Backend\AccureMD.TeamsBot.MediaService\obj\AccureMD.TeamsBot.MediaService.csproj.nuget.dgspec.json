{"format": 1, "restore": {"D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.MediaService\\AccureMD.TeamsBot.MediaService.csproj": {}}, "projects": {"D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.MediaService\\AccureMD.TeamsBot.MediaService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.MediaService\\AccureMD.TeamsBot.MediaService.csproj", "projectName": "AccureMD.TeamsBot.MediaService", "projectPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.MediaService\\AccureMD.TeamsBot.MediaService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.MediaService\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Logging.ApplicationInsights": {"target": "Package", "version": "[2.22.0, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.64.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.8.1, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}