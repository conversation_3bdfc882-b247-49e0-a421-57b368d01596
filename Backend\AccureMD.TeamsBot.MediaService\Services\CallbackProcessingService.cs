using AccureMD.TeamsBot.MediaService.Models;
using System.Text.Json;

namespace AccureMD.TeamsBot.MediaService.Services;

public class CallbackProcessingService
{
    private readonly CallStateService _callStateService;
    private readonly ILogger<CallbackProcessingService> _logger;

    public CallbackProcessingService(
        CallStateService callStateService,
        ILogger<CallbackProcessingService> logger)
    {
        _callStateService = callStateService;
        _logger = logger;
    }

    public async Task ProcessCallbackAsync(string callbackBody)
    {
        try
        {
            _logger.LogInformation("Processing Graph callback: {Body}", callbackBody);

            var jsonDoc = JsonDocument.Parse(callbackBody);
            var root = jsonDoc.RootElement;

            // Extract basic information
            var changeType = root.TryGetProperty("changeType", out var changeTypeElement) 
                ? changeTypeElement.GetString() 
                : null;

            var resourceData = root.TryGetProperty("resourceData", out var resourceDataElement) 
                ? resourceDataElement 
                : (JsonElement?)null;

            if (resourceData == null)
            {
                _logger.LogWarning("No resourceData found in callback");
                return;
            }

            var callId = resourceData.Value.TryGetProperty("id", out var idElement) 
                ? idElement.GetString() 
                : null;

            if (string.IsNullOrEmpty(callId))
            {
                _logger.LogWarning("No call ID found in callback");
                return;
            }

            // Record the event
            _callStateService.RecordCallbackEvent(callId, changeType ?? "unknown", callbackBody);

            // Process based on change type
            switch (changeType?.ToLower())
            {
                case "created":
                    await ProcessCallCreatedAsync(callId, resourceData.Value);
                    break;
                case "updated":
                    await ProcessCallUpdatedAsync(callId, resourceData.Value);
                    break;
                case "deleted":
                    await ProcessCallDeletedAsync(callId, resourceData.Value);
                    break;
                default:
                    _logger.LogInformation("Unhandled change type: {ChangeType} for call {CallId}", changeType, callId);
                    break;
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse callback JSON");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing callback");
        }
    }

    private async Task ProcessCallCreatedAsync(string callId, JsonElement resourceData)
    {
        _logger.LogInformation("Processing call created event for call {CallId}", callId);
        
        // Update call status to established
        _callStateService.UpdateCallStatus(callId, CallStatus.Established);
        
        await Task.CompletedTask;
    }

    private async Task ProcessCallUpdatedAsync(string callId, JsonElement resourceData)
    {
        _logger.LogInformation("Processing call updated event for call {CallId}", callId);

        // Check for state changes
        if (resourceData.TryGetProperty("state", out var stateElement))
        {
            var state = stateElement.GetString();
            _logger.LogInformation("Call {CallId} state changed to: {State}", callId, state);

            switch (state?.ToLower())
            {
                case "established":
                    _callStateService.UpdateCallStatus(callId, CallStatus.Established);
                    break;
                case "terminated":
                    _callStateService.UpdateCallStatus(callId, CallStatus.Ended);
                    break;
            }
        }

        // Check for participants updates
        if (resourceData.TryGetProperty("participants", out var participantsElement))
        {
            await ProcessParticipantsUpdateAsync(callId, participantsElement);
        }

        // Check for media state changes
        if (resourceData.TryGetProperty("mediaState", out var mediaStateElement))
        {
            await ProcessMediaStateChangeAsync(callId, mediaStateElement);
        }

        await Task.CompletedTask;
    }

    private async Task ProcessCallDeletedAsync(string callId, JsonElement resourceData)
    {
        _logger.LogInformation("Processing call deleted event for call {CallId}", callId);
        
        _callStateService.UpdateCallStatus(callId, CallStatus.Ended);
        
        // Clean up after a delay to allow for final processing
        _ = Task.Delay(TimeSpan.FromMinutes(5)).ContinueWith(_ => 
        {
            _callStateService.RemoveCall(callId);
        });

        await Task.CompletedTask;
    }

    private async Task ProcessParticipantsUpdateAsync(string callId, JsonElement participantsElement)
    {
        try
        {
            if (participantsElement.ValueKind == JsonValueKind.Array)
            {
                foreach (var participant in participantsElement.EnumerateArray())
                {
                    if (participant.TryGetProperty("info", out var infoElement) &&
                        infoElement.TryGetProperty("identity", out var identityElement))
                    {
                        var participantId = ExtractParticipantId(identityElement);
                        if (!string.IsNullOrEmpty(participantId))
                        {
                            // Check if participant is being added or removed
                            var isInLobby = participant.TryGetProperty("isInLobby", out var lobbyElement) && 
                                          lobbyElement.GetBoolean();
                            
                            var isMuted = participant.TryGetProperty("isMuted", out var mutedElement) && 
                                        mutedElement.GetBoolean();

                            if (!isInLobby)
                            {
                                _callStateService.AddParticipant(callId, participantId);
                                _logger.LogInformation("Participant {ParticipantId} joined call {CallId}", participantId, callId);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing participants update for call {CallId}", callId);
        }

        await Task.CompletedTask;
    }

    private async Task ProcessMediaStateChangeAsync(string callId, JsonElement mediaStateElement)
    {
        try
        {
            // Process audio media state
            if (mediaStateElement.TryGetProperty("audio", out var audioElement))
            {
                var audioState = audioElement.GetString();
                _logger.LogInformation("Audio state for call {CallId}: {AudioState}", callId, audioState);
                
                // Update call status if media becomes active
                if (audioState == "active")
                {
                    _callStateService.UpdateCallStatus(callId, CallStatus.MediaActive);
                }
            }

            // In a real implementation, you would process individual participant media streams here
            // This would involve parsing the media stream information and updating the call state accordingly
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing media state change for call {CallId}", callId);
        }

        await Task.CompletedTask;
    }

    private string? ExtractParticipantId(JsonElement identityElement)
    {
        // Try different identity types
        if (identityElement.TryGetProperty("user", out var userElement) &&
            userElement.TryGetProperty("id", out var userIdElement))
        {
            return userIdElement.GetString();
        }

        if (identityElement.TryGetProperty("application", out var appElement) &&
            appElement.TryGetProperty("id", out var appIdElement))
        {
            return appIdElement.GetString();
        }

        if (identityElement.TryGetProperty("guest", out var guestElement) &&
            guestElement.TryGetProperty("id", out var guestIdElement))
        {
            return guestIdElement.GetString();
        }

        return null;
    }
}
