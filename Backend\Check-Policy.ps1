# This script checks if the Teams Application Access Policy for the bot is active
# by attempting to get an application token and calling a protected Graph API.

# --- Configuration ---
# Path to your appsettings.json file. Assumes the script is run from the 'Backend' directory.
$settingsPath = ".\appsettings.json"

# --- Script ---
try {
    Write-Host "--- Script Start ---"
    Write-Host "🔎 Reading configuration from $settingsPath..." -ForegroundColor Yellow
    if (-not (Test-Path $settingsPath)) {
        Write-Host "❌ ERROR: Configuration file not found. Make sure you are in the 'Backend' directory." -ForegroundColor Red
        throw "Configuration file not found at '$settingsPath'. Please run this script from the 'Backend' directory."
    }

    $settings = Get-Content $settingsPath -Raw | ConvertFrom-Json

    # Fetch credentials, preferring the 'Teams' section but falling back to root level
    $tenantId = $settings.Teams.TenantId -or $settings.MicrosoftAppTenantId
    $clientId = $settings.Teams.AppId -or $settings.MicrosoftAppId
    $clientSecret = $settings.Teams.AppSecret -or $settings.MicrosoftAppPassword

    if (-not $tenantId -or -not $clientId -or -not $clientSecret) {
        Write-Host "❌ ERROR: Could not find required settings in appsettings.json." -ForegroundColor Red
        throw "Could not find TenantId, AppId, or AppSecret in $settingsPath. Please ensure they are configured."
    }

    Write-Host "✅ Configuration loaded successfully."

    # --- Step 1: Get an access token for the application ---
    Write-Host "🔑 Requesting an access token for the application..." -ForegroundColor Yellow

    $tokenBody = @{
        grant_type    = "client_credentials"
        client_id     = $clientId
        client_secret = $clientSecret
        scope         = "https://graph.microsoft.com/.default"
    }

    $tokenUri = "https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token"
    Write-Host "Requesting token from: $tokenUri"

    # Added -TimeoutSec to prevent indefinite hanging
    $tokenResponse = Invoke-RestMethod -Uri $tokenUri -Method POST -Body $tokenBody -TimeoutSec 30
    $token = $tokenResponse.access_token

    if (-not $token) {
        throw "Failed to acquire access token. Check your credentials and tenant ID."
    }

    Write-Host "✅ Access token acquired successfully."

    # --- Step 2: Call a protected Graph API endpoint ---
    # We use /communications/onlineMeetings as it requires application permissions.
    # A successful call (even with an empty result) indicates the policy is active.
    $graphUri = "https://graph.microsoft.com/v1.0/communications/onlineMeetings"
    Write-Host "📞 Calling Graph API: $graphUri..." -ForegroundColor Yellow

    $headers = @{ Authorization = "Bearer $token" }

    # Added -TimeoutSec to prevent indefinite hanging
    $graphResponse = Invoke-RestMethod -Uri $graphUri -Headers $headers -Method GET -TimeoutSec 30

    # If we get here without an exception, the call was successful.
    Write-Host "🎉 SUCCESS! The Application Access Policy is active." -ForegroundColor Green
    Write-Host "The application was successfully authorized to call the Graph Communications API."
    Write-Host "You should now be able to join meetings."
    Write-Host "Response from Graph:"
    $graphResponse | ConvertTo-Json -Depth 5
    Write-Host "--- Script End ---"
}
catch {
    Write-Host "❌ ERROR: An error occurred." -ForegroundColor Red

    # Check if it's a web exception from Invoke-RestMethod, which is the most common failure point.
    if ($_.Exception -is [System.Net.WebException] -and $_.Exception.Response) {
        $response = $_.Exception.Response
        $statusCode = [int]$response.StatusCode
        
        $responseStream = $response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($responseStream)
        $errorBody = $reader.ReadToEnd()
        $reader.Close()
        $responseStream.Close()

        Write-Host "HTTP Status Code: $statusCode" -ForegroundColor Red

        if ($statusCode -eq 403) {
            Write-Host "🚦 Received HTTP 403 (Forbidden). This is the expected error if the policy is not yet active." -ForegroundColor Yellow
            Write-Host "This could mean the policy has not propagated yet. Please wait (it can take up to a few hours) and try again."
        }
        
        Write-Host "--- Error Response Body ---"
        try {
            # Try to format as JSON, but fall back to raw text if it fails
            ($errorBody | ConvertFrom-Json) | ConvertTo-Json -Depth 5
        }
        catch {
            $errorBody
        }
        Write-Host "---------------------------"
    }
    else {
        # It's a different kind of error (e.g., config file not found, JSON parsing, network issue, etc.)
        Write-Host "An unexpected error occurred:" -ForegroundColor Red
        Write-Host $_.Exception.Message
    }
    Write-Host "--- Script End (Error) ---"
}