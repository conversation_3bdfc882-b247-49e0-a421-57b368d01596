using System;
using System.Threading.Tasks;
using System.Web.Http;
using Newtonsoft.Json.Linq;

namespace AccureMD.MediaBot.Worker.Controllers
{
    [RoutePrefix("mediabot/join")]
    public class JoinController : ApiController
    {
        private CommunicationsHost ResolveHost()
        {
            object value;
            if (this.Configuration != null && this.Configuration.Properties.TryGetValue(typeof(CommunicationsHost), out value))
            {
                return (CommunicationsHost)value;
            }
            if (GlobalConfiguration.Configuration != null && GlobalConfiguration.Configuration.Properties.TryGetValue(typeof(CommunicationsHost), out value))
            {
                return (CommunicationsHost)value;
            }
            throw new System.Collections.Generic.KeyNotFoundException("CommunicationsHost not found in configuration properties");
        }

        [HttpPost, Route("")]
        public async Task<IHttpActionResult> Join()
        {
            var json = await Request.Content.ReadAsStringAsync();
            Console.WriteLine($"[MediaBot] /mediabot/join payload: {json}");
            var obj = JObject.Parse(json);
            var meetingUrl = (string)obj["meetingUrl"];
            var displayName = (string)obj["displayName"] ?? "AccureMD Media Bot";
            var requestTenantId = (string)obj["tenantId"]; // root-level tenantId supported
            var meetingInfo = obj["meetingInfo"] as JObject; // optional explicit meetingInfo

            // Use Graph Communications SDK; it will generate the appHostedMediaConfig blob
            var host = ResolveHost();
            var commsClient = host.Client;
            var callbackUri = (System.Configuration.ConfigurationManager.AppSettings["BotBaseUrl"] ?? string.Empty).TrimEnd('/') + "/mediabot/notifications";
            var sdk = new AccureMD.MediaBot.Worker.Services.SdkCallService(commsClient, callbackUri);

            // Effective tenant: prefer provided, otherwise extract from joinUrl, otherwise config
            var extractedTid = AccureMD.MediaBot.Worker.BotService.ExtractTid(meetingUrl);
            var tenantId = requestTenantId ?? extractedTid ?? System.Configuration.ConfigurationManager.AppSettings["TenantId"];

            if (string.IsNullOrWhiteSpace(tenantId))
            {
                return BadRequest(Newtonsoft.Json.JsonConvert.SerializeObject(new { success = false, message = "Missing tenantId. Provide in payload or ensure join URL context contains Tid." }));
            }

            if (meetingInfo != null && meetingInfo["@odata.type"]?.ToString() == "#microsoft.graph.organizerMeetingInfo")
            {
                var organizerId = meetingInfo["organizer"]?["user"]?["id"]?.ToString();
                if (string.IsNullOrWhiteSpace(organizerId))
                {
                    return BadRequest(Newtonsoft.Json.JsonConvert.SerializeObject(new { success = false, message = "meetingInfo.organizer.user.id is required for organizer flow" }));
                }
                var resultOrg = await sdk.JoinByOrganizerAsync(tenantId, organizerId, displayName);
                Console.WriteLine($"[MediaBot] SDK join (organizer) result: ok={resultOrg.ok}, callId={resultOrg.callId}, message={resultOrg.message}");
                return resultOrg.ok ? (IHttpActionResult)Ok(new { success = true, callId = resultOrg.callId, message = resultOrg.message })
                                    : BadRequest(Newtonsoft.Json.JsonConvert.SerializeObject(new { success = false, message = resultOrg.message }));
            }

            // Default: organizer id extracted from joinUrl context
            var organizerOid = AccureMD.MediaBot.Worker.BotService.ExtractOid(meetingUrl);
            if (string.IsNullOrWhiteSpace(organizerOid))
            {
                return BadRequest(Newtonsoft.Json.JsonConvert.SerializeObject(new { success = false, message = "Missing organizer Oid in meetingUrl context (or provide meetingInfo.organizer)" }));
            }
            var result = await sdk.JoinByOrganizerAsync(tenantId, organizerOid, displayName);
            Console.WriteLine($"[MediaBot] SDK join result: ok={result.ok}, callId={result.callId}, message={result.message}");
            return result.ok ? (IHttpActionResult)Ok(new { success = true, callId = result.callId, message = result.message })
                              : BadRequest(Newtonsoft.Json.JsonConvert.SerializeObject(new { success = false, message = result.message }));
        }
    }
}

