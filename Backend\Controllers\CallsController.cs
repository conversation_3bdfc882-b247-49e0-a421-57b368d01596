using Microsoft.AspNetCore.Mvc;
using AccureMD.TeamsBot.Services;

namespace AccureMD.TeamsBot.Controllers;

[ApiController]
[Route("api/calls")]
public class CallsController : ControllerBase
{
    private readonly ILogger<CallsController> _logger;
    private readonly CallStateService _callState;

    public CallsController(ILogger<CallsController> logger, CallStateService callState)
    {
        _logger = logger;
        _callState = callState;
    }

    // Graph will POST events to this callbackUri once the call is created or changes
    [HttpPost("callback")]
    public async Task<IActionResult> Callback()
    {
        using var reader = new StreamReader(Request.Body);
        var body = await reader.ReadToEndAsync();
        _logger.LogInformation("Received Graph call callback: {Body}", body);

        // Record body and mark callId if present
        _callState.RecordCallbackBody(body);

        // For quick smoke we don't validate JWT. In production, validate Authorization header from Graph.
        return Accepted();
    }

    // Diagnostic: fetch last few callbacks for debugging
    [HttpGet("recent")]
    public IActionResult Recent()
    {
        var recent = _callState.GetRecentCallbacks(10);
        return Ok(recent);
    }
}

