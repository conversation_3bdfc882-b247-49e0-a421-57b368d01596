007b0bd2:"[cid:%s] pal_audio: removing virtual device, eType = %d, privateId = %ls, device = %p, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UnregisterVirtualAudioDevices"
01655400:"[cid:%s] Glitch occurs: QPCPos=%llu, device position=%llu, glitch info=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::GetGlitchInfo"
016bcf26:"[cid:%s] WifiStats: channel:%lu, signal:%lu, handovers:%lu, channelSwitches:%lu, freq:%llu, channelReassociations:%lu, BatteryCharged:%lu","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorPlatform::OnTimerCallback"
0180eacb:"[cid:%s] ConnectEx: SO_UPDATE_CONNECT_CONTEXT fails 0x%x OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnConnectExCompletion"
026071c5:"[cid:%s] Wrong device type %d!","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalCreateVirtualAudioDevice"
0290b9c7:"[cid:%s] Flush ETW Event: EventsLost %lu LogBuffersLost %lu NumberOfBuffers %lu FreeBuffers %lu BuffersWritten %lu","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"DumpEtwStatistics"
029f68c6:"[cid:%s] Failed to create GetPnpNameAndRelatedFieldsThreadFuncParams","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
02bedf01:"[cid:%s] Invalid arg. !pTimeOutCountCurrentDevice. Not querying device gain fields. Will use default values","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
02e23dc2:"[cid:%s] Enumerate and update WinRT Device ID from cache failed and fallback to regular winrt enumeration. hr=0x%08X. ","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
02f7da5d:"[cid:%s] Fail to initialize trace route module: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/TraceRtSocket.cpp";"RtcPalTraceRtStartup"
030b7d10:"[cid:%s] MFStartup fail hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/mfhelper/mfhelper.cpp";"RtcPalMFHasAVCapture"
032a54cd:"[cid:%s] Set ducking options failed with error 0x%x. Not opening speaker in comms category","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
03967ebc:"[cid:%s] dequeue sample: time:%llu, frame sample dequeued:%d, pSample:%p, totalSamplesDequeued:%llu, samplesDeliveredToLMS:%llu, totalSamplesDeliveredToLMS:%llu, fFrameReady:%d, expectedFramesDelivered:%llu, framesDelivered:%llu, m_startTime:%llu, m_lastStartTime:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
03a3e1b1:"[cid:%s] Failed to set client session volume. hr: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
03f00740:"[cid:%s] Successfully read cached value for setting: %s. Value: %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/rtcpaldeviceaudioregistryhelper.cpp";"RtcPal::ReadDeviceSettingFromCache"
041c84e5:"[cid:%s] return OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::GetQueuedCompletionStatus"
044801dd:"[cid:%s] Failed to unsubscribe VoipCallCoordinator muteStateChanged, hr=0x%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalVoipCallCoordinator.cpp";"voipCallCoordinator::RtcPalVoipCallCoordinator::ReleaseVoipCallCoordinator"
049685db:"[cid:%s] Get ducking control service failed with error 0x%x. Not opening speaker in comms category","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
049861d8:"[cid:%s] Failed to set context","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OnReadSample"
04cd83c2:"[cid:%s] Failed to set probe source as it is null","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::SetSourceProbeClientToSinkProbeClient"
04ea8a72:"[cid:%s] Format not Ok, suggested closest format: ch=%d, f=%d, bps=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
052495e0:"[cid:%s] ConnectEx fails 0x%x OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnConnectExCompletion"
05be96ea:"[cid:%s] m_pAudioDevice is nullptr","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::RtcPalDeviceAudioBoostControlImpl"
0605f768:"[cid:%s] m_pAsyncParas->pGainControl->GetEnabled failed with hr = %#x ","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::GetAutoBoostControlEnabled"
06af0d0b:"[cid:%s] pal_audio: UpdateVolumeControlSink[%ls] hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
06bcc142:"[cid:%s] spAudioEffectManager is nullptr","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioStreamEffectsCallBack::OnAudioEffectsChanged"
06f9c9f7:"[cid:%s] Error(%d), Failed to create resampler output type","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
071ba2ac:"[cid:%s] Failed to get file duration","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
07c501d8:"[cid:%s] Failed to lock sample buffer","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
07f6099e:"[cid:%s] SuggestedInitialConfigIDs is empty, and there is no other available configs to try, return config ID = 0 as the last resort.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
080b6c63:"[cid:%s] Format Ok","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
08db7297:"[cid:%s] Error(%d),Can\'t Set MF_MT_MAJOR_TYPE GUID","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
08eae51d:"[cid:%s] Successfully saved device format info to cache for device: %ls, setting is: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveDeviceFormatToCache"
092e01f5:"[cid:%s] Failed to start trace OS log session. status=%lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartLogSession"
09328fbe:"[cid:%s] Failed to resample data","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
0938d1a0:"[cid:%s] [ECS]: StorageKey %s is found, value is: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"InitSettingValue"
09ddceb6:"[cid:%s] Caught system error %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::RtcPalDeviceAudioBoostControlImpl"
09e895ad:"[cid:%s] OnDeviceStateChanged, device id=%ls, new state=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnDeviceStateChanged"
09e9da81:"[cid:%s] %s","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/common/RTCPalTraceImpl.cpp";"RtcPalPrintDefaultTrace"
0b03da11:"[cid:%s] failed to create string from GUID Type %d pnp %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
0bab0748:"[cid:%s] Volume is set on active sink device. Using volume control source to set volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
0bdf7598:"[cid:%s] Read device format info from cache failed for reading setting from cache for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ReadDeviceFormatFromCache"
0c84ec1f:"[cid:%s] pal_audio: async enumeration of usb controller waiting time=0x%I64X(ms), hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateUSBControllers"
0ca64c01:"[cid:%s] deviceId %ls type 0x%x sessionMute %u fltVolSession %f sysMute %u fltVolSystem %f fltVolSystemDb %f fltVol(%f %f %f)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::SetEndpointVolume"
0dba27d5:"[cid:%s] pal_audio: GetDefaultDeviceIdsThreadFunc starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
0e06228e:"[cid:%s] Volume type 0x%x not supported devId %ls %s Volume MuteSession %d MuteSystem %d volSession %f volSys %f","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetOrSetSystemVolumeInfo"
0e350dc8:"[cid:%s] Error(%d), Failed to set Reader output to PCM","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
0ed3685d:"[cid:%s] Device is in whitelist. Spatial audio capability is enabled for device with pal_audio: enum[certifiedDeviceId=%ls,isForceBlocked=%d,isForceCapable=%d]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateSpatialAudioSupport"
0f71bcf1:"[cid:%s] StopTraceW() failed: hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalStopWppAndEtwInternal"
0fa511ed:"[cid:%s] File size larger than MAX_INT_ULONG","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GetFileDuration"
100e5edc:"[cid:%s] AudioLoopback (%ls): Start","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Start"
1020a889:"[cid:%s] sample got re-used for production without consuming. ","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::GetBuffer"
10bfb92a:"[cid:%s] Volume is queried on non-active device. Using default mode to return volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
1126dd37:"[cid:%s] Failed GetBuffer on sample, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADLockBufferCBackInternal"
117851df:"[cid:%s] ActivateIAudioClientWithTimeout failed with error hr=%x. Will use default values for device format","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetAudioClient"
125c47a8:"[cid:%s] Sample SetCallback to Func=0x%p, Context=0x%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::SetCallback"
131c6b25:"[cid:%s] %s enumeration is in progress, platform metrics is not ready","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetMetrics"
13bb581d:"[cid:%s] ConfigHandlerName = %ls, device type = %d, maxNumOfRetry = %d, suggestedInitialConfigIDs = %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
13cff050:"[cid:%s] created sample:%llu, time:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::CreateSample"
140044ce:"[cid:%s] Open registry key failed.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/windows/RtcPalDeviceUtilsWinClassic.cpp";"RtcPalDeviceUtils::GetMachineId"
14390331:"[cid:%s] failed removing event from socket: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::DoAccept"
1445004e:"[cid:%s] Async Enum Parameters: type=%s ulCategoryMask=%d timeoutMode=%d timeoutMs=%d m_maxTimeOutsPerDevice=%d maxTimeOutsOverall=%d, timeOutCountOverall=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
1450a39b:"[cid:%s] Setting session volume for device %ls to %d Scaling Factor: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
14c3de26:"[cid:%s] mStartDeviceReason = %d, respect the suggested initial config.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalInitAudioClientConfigHandler::AllowAlternativeInitialConfigIDs"
14fd8de9:"[cid:%s] AudioCapture (%ls): Destroyed","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::~RtcPalDeviceAudioCaptureWASAPI"
15e572f9:"[cid:%s] Invalid arg. !pTimeOutCountCurrentDevice. Not querying device format. Will use default values","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetAudioClient"
15f18ee0:"[cid:%s] Reset was required. start queue size: %d. end queue size: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
165f6a80:"[cid:%s] pal_audio: enumerated %u virtual audio devices, and in total %u audio devices","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
16a2d237:"[cid:%s] Failed to create GetPropStoreAndFriendlyNameThreadFuncParams","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
16f5b91b:"[cid:%s] pal_audio: enumeration updates device for type: %s, count: %d, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
16f73a7f:"[cid:%s] Queue Close request OVERLAPPED %p for completion","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnIoCompletion"
1718414a:"[cid:%s] Failed to pass sample to Resampler","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
17218b0b:"[cid:%s] GetPnpNameAndRelatedFieldsWithTimeout failed with error hr=%x. Will use default values for pnp name and related fields","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
17bdb76d:"[cid:%s] InitializeAudioClientPlatformSpecific failed, hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClient"
1802cf9f:"[cid:%s] Audio Effects are changed, DNS old config = %d DNS new config = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioStreamEffectsCallBack::OnAudioEffectsChanged"
18846bc2:"[cid:%s] Failed to populate raw stream support info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for raw support","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
1926624b:"[cid:%s] RtcPalSystemMetricsCollector: Could not stop timer.","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsBase.cpp";"RtcPalSystemMetricsCollector::SetStart"
192d7774:"[cid:%s] There is no cached failure record for config %d.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
198d0424:"[cid:%s] RegisterSocket socket=%p: fails 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::RegisterSocket"
1a107227:"[cid:%s] ActivateIAudioEndpointVolumeWithTimeout failed with timeout error. Will use default values for gain fields.Time out counts for current device: %d Time out counts overall: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
1a887e90:"[cid:%s] Failed to get RtcPalAudioMediaExtensionType_VirtualDeviceManager extension, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::RegisterVirtualAudioDevices"
1aa1b1e4:"[cid:%s] Audio effects status Changed","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::NotifyAudioStreamEffectChange"
1b7d17c7:"[cid:%s] Total samples produced=%llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADLockBufferCBackInternal"
1bd9e8e5:"[cid:%s] Specified format is not supported directly, fallback to default. Specified format: wFormatTag = %d, nChannels = %d, nSamplesPerSec = %d, wBitsPerSample = %d, nBlockAlign = %d, nAvgBytesPerSec = %d, cbSize = %d, hr=%0x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
1c4786b4:"[cid:%s] Identified initial Config ID: %d, description: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::RunAndMonitor"
1c8077ab:"[cid:%s] Failed to get driver version for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for driver version","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
1c8889a1:"[cid:%s] pal_audio: %ls completed RunAndMonitor, device type = %d. Final configuration used: ID = %d, %s. Final returned hr = 0x%x, number of retry(s) taken = %d, continuous number of failure = %d, total number of failure = %d, all attempted configurations and results = %s, all known faulty configs prevented = %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::RunAndMonitor"
1ca7e238:"[cid:%s] queuedBytes=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::GetQueuedBytes"
1d5094c1:"[cid:%s] ConfigHandlerName = %ls, device type = %d, ReceiveMidcallEventFeedback event = %d, hrInput = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalInitAudioClientConfigHandler::ReceiveMidcallEventFeedback"
1d8ff65d:"[cid:%s] GetPnpNameAndRelatedFieldsThreadFunc failed with error hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
1db837cb:"[cid:%s] audio device thread was already initialized","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::InitializeAudioDeviceThread"
1de60568:"[cid:%s] EmulatedAcceptEx: DoAccept succeeds: queue OVERLAPPED %p for completion","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectAccept"
1ee8065d:"[cid:%s] Activate AudioInterface for %ls added in map size %zu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::GetAudioEndpointVolumeIntf"
1ef08397:"[cid:%s] Failed ReserveBuffer on sample, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADLockBufferCBackInternal"
1faf4111:"[cid:%s] RAW Stream support set to: %d, clientPropertyEcsFlag: disabled.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::MMDevicePropertyStoreHelper::GetRawStreamSupport"
1fd814ef:"[cid:%s] start virtual audio device with return: %x, requestSampleThreadEnabled = %u, startCount = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioCaptureDeviceImpl::PRtcPALVADStartCBackInternal"
2009ecda:"[cid:%s] Read device format info from cache failed for parsing setting for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ReadDeviceFormatFromCache"
2047418a:"[cid:%s] GetPropStoreAndDeviceNameWithTimeout failed with timeout error. Will use default values for friendly name and display name and other prop store fields","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
206ca85a:"[cid:%s] ecsDeepVQEDeviceBlockList: %s.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateDeepVQEAllowedOnDeviceFlag"
20eae09b:"[cid:%s] Failed to get device display name. Will use default values for display name","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
20f96e34:"[cid:%s] Queue EmulatedAccept OVERLAPPED %p for completion - cancelled","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Close"
2147099e:"[cid:%s] pal_audio: IAudioClient::GetDevicePeriod succeeded, hnsDefaultDevicePeriod: %llu, hnsMinimumDevicePeriod: %llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
21e83051:"[cid:%s] ReorderDeviceList ECS key is set to false. Device list will not be sorted","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
22472f17:"[cid:%s] rt::persistent::Flush() failed. err=%s (%d)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/rtcpaldeviceaudioregistryhelper.cpp";"RtcPal::WriteDeviceSettingToCache"
22be1fd1:"[cid:%s] Speaker session volume is set to: %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
232c39a7:"[cid:%s] pal_audio: #%u: skip one device since it\'s failed to query device id, type=%s, hr = %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
236d995d:"[cid:%s] OnAddrChangeEventSignaled: unexpected error: WSAIoctl return success: AddressFamily: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::OnAddrChangeEventSignaled"
23e5645d:"[cid:%s] Failed to get device friendly name. Will use default values for friendly name","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
246cd05b:"[cid:%s] OnDeviceAdded, dvice id=%ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnDeviceAdded"
24a35afc:"[cid:%s] The device client handle requested to be removed doesn\'t exist in the RingBuffer map.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::RemoveDeviceClientHandle"
259ca9f6:"[cid:%s] Virtual Platform has been initialized!","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::Initialize"
26af750f:"[cid:%s] Setting session mute for device %ls to %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
2703ca00:"[cid:%s] AudioLoopback (%ls): Stop - LockBuffer=%u FramesCaptured=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Stop"
2763de0f:"[cid:%s] IsEndpointAMicArray == true, getting device mic geometry array","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetMicArrayGeometryIfAvailable"
277f18eb:"[cid:%s] Render one sample to virtual sink. queued bytes=%d, device position = %llu, expected finished time=%llu, total samples delivered=%llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::AudioDeviceThreadProcOneTime"
278242d2:"[cid:%s] pEffect ={%08lX-%04hX-%04hX-%02hhX%02hhX-%02hhX%02hhX%02hhX%02hhX%02hhX%02hhX} , can we set the state: %d , state of the effect = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioStreamEffectsCallBack::OnAudioEffectsChanged"
2825bd72:"[cid:%s] async thread has timed out inside %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalThreadHelper.cpp";"WaitForCompletion"
28cf5254:"[cid:%s] Successfully write result to cache with exponential backoff. key: %ls, value: %d, cache base valid duration in second: %llu, max valid duration in second: %llu, final valid duration in second written: %llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::WriteToCacheWithExponentialBackoff"
290a439a:"[cid:%s] Releasing audio client","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
296dd1cd:"[cid:%s] RtcPalSystemMetricsCollector: callback stats -> callbacks %d (completed %d), avg: %.2f, max: %lld, first: %lld, last: %lld","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorPlatform::LogStatsOnStop"
2aa5b73f:"[cid:%s] Enumerate and update WinRT Device ID from cache succeeded. hr=0x%08X. ","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
2b25f7be:"[cid:%s] num of effects detected = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::SetAudioBlurEnabled"
2b8a6495:"[cid:%s] Successfully wrote all cache to registry.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::WriteAllCacheToRegistry"
2bbe428d:"[cid:%s] spEnumerator->GetDefaultAudioEndpoint failed for DCD, hr = %x. Will set first enumerated device as default communications device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
2bbfb1b0:"[cid:%s] pal_audio: async enumeration of usb controller starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateUSBControllers"
2bdd0dcd:"[cid:%s] Failed to populate friendly name for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for device friendly name","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
2e5d1111:"[cid:%s] Posting device property change notification","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::PostDevicePropertyChangedNotification"
2e9260c4:"[cid:%s] Device audio effects changed","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::OnDeviceEffectsChange"
2ea27a01:"[cid:%s] SetVolumeOnActiveSink failed with hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::SetVolumeOnActiveSink"
2eb06808:"[cid:%s] Voip call coordinator created, appName = %s, coordinator = %p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalVoipCallCoordinatorExtension.cpp";"voipCallCoordinator::RtcPalVoipCallCoordinatorExtension::CreateVoipCallCoordinator"
2eb518da:"[cid:%s] Setting system dB volume for device %ls to %d Scaling Factor: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
2fb411ec:"[cid:%s] Virtual Platform has been deleted!","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::~RtcPalVirtualDevicePlatform"
302f6bc2:"[cid:%s] Failed to read cache value for init spk session vol override  hr=%0x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
3053320d:"[cid:%s] EnableVirtualCaptureRequestSampleThread failed due to virtual audio platform has not been initialized yet","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::EnableVirtualCaptureRequestSampleThread"
305453bf:"[cid:%s] Master volume is 0 at end of call. Not caching user override of speaker master volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
3094bd8d:"[cid:%s] IsFormatSupported, hr=%#x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
31582ff9:"[cid:%s] Successfully read but failed to parse cache from registry. key: %s, valueStr: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::ReadCacheFromRegistry"
31dddb43:"[cid:%s] AudioRender: ReleaseBuffer=%u FramesRendered=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::ReleaseBuffer"
321d9bbf:"[cid:%s] asyncOp->QueryInterface failed with hr = %8x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/winrt/PnpAsyncCompletionHandler.h";"RtcPalAsyncCompleteHandler::Invoke"
32a29695:"[cid:%s] Unable to allocate memory for microphonegeometry array out of memory error: hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetMicArrayGeometryIfAvailable"
32a81b02:"[cid:%s] Failed to create IMFSourceReader","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
3301c160:"[cid:%s] Zero duration","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GetFileDuration"
33560b9f:"[cid:%s] EmulatedConnectEx: Connect request aborted: NetworkEvent %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
33a6700c:"[cid:%s] Failed to populate jack subtype info for device: type=%d, id=%ls. Error code returned: hr=%x. Not refining device form factor","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
342252fe:"[cid:%s] AcceptEx fails WSAEINVAL OVERLAPPED %p: name buffer is too small, input %d bytes required %d bytes","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
34a0e305:"[cid:%s] Failed to create RtcPalDevicePlatformPropertyChangeNotificationFilter, no enough memory","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::RegisterNotification"
354a3408:"[cid:%s] Closing: I/O count %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Close"
360b10b3:"[cid:%s] Deliver simple device event (%d) failed with hr:%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::DeliverSimpleAudioDeviceEvent"
36ade1b3:"[cid:%s] AudioRender (%ls): Activate - FormatTag=%u Channels=%d SamplesPerSec=%d BitsPerSample=%u 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
36b3c625:"[cid:%s] failed to create RVDDeviceInfo_t object from pDevInfo(%p) with error %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceInfoListToCollection"
3725a5ca:"[cid:%s] GetResult failed, m_spUnk is nullptr","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterfaceDispatcher::GetResult"
375a6b7d:"[cid:%s] Queue Close request OVERLAPPED %p for completion","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::SendTo"
37649168:"[cid:%s] Read winrt ID from cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateWinRTDeviceInfoFromCache"
37904338:"[cid:%s] PdhAddEnglishCounterW for path %ls failed with error %ld","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorWindows::CollectPdhMetrics"
37e1d9f4:"[cid:%s] pal_audio: enum[type=%s,fn=%ls,pnp=%ls,ff=%d,dad=%d,dcd=%d,caps=0x%I64X,priorityScore=%d,uiIsInternalDevice=%d,channel=%d, samplerate=%d, bitspersample=%d, IsSpatialCapable=%d, containerId=%ls]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
37e9a776:"[cid:%s] Create IOCP %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCP.cpp";"RtcPalCreateSocketIOCP"
37f9cccf:"[cid:%s] Set probe source to probe sink successfully","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::SetSourceProbeClientToSinkProbeClient"
38ddf222:"[cid:%s] Found AudioInterface for %ls in map size %zu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::GetAudioEndpointVolumeIntf"
3906086a:"[cid:%s] pal_audio: populate basic device information succeeded (id=%ls, friendlyname=%ls, pnpname=%ls).","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
396fecc0:"[cid:%s] Failed to lock sample buffer","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OnReadSample"
3a78b109:"[cid:%s] m_frameSampleJBQueueCapacity:%d, m_samplesPerFrame:%d, m_sampleSizeinBytes:%d, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::SetFrameSampleJBQueueCap"
3ab8c615:"[cid:%s] Read winrt CONN from cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateWinRTDeviceInfoFromCache"
3b4fe2dd:"[cid:%s] Format not Ok, suggested closest format as: fmt=%d, ch=%d, f=%d, bps=%d, size=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
3bb5422d:"[cid:%s] delivered sample:%llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DeliverSample"
3bb7daad:"[cid:%s] rt::persistent::Flush() failed. err=%s (%d)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::WriteAllCacheToRegistry"
3d1bb0c8:"[cid:%s] Failed to read cache value for init spk vol override  hr=%0x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
3d2560c2:"[cid:%s] IsEndpointAMicArray: getting device topology.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::IsEndpointAMicArray"
3d355199:"[cid:%s] Error(%d), Failed to get resampler input properties","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
3d367a24:"[cid:%s] CertifiedListId:=%ls; device name=%ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::GetCertifiedListDeviceID"
3d583fb6:"[cid:%s] closesocket() failed with error 0x%x.","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/CloseSocketOffload.cpp";"CloseSocketOffload::CloseSocket"
3d5a4060:"[cid:%s] AudioRender: pui64QPCPos=%lu, pui64Pos=%lu, piPadding = %u, curPadding = %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::GetGlitchInfo"
3d5f7f50:"[cid:%s] pal_audio: %ls began, device type = %d, isStreaming = %d. Constraints: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientWithResilienceHandling"
3d89a036:"[cid:%s] EnableAsyncOSActions is false","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::Initialize"
3dc55f17:"[cid:%s] Error(%d),Can\'t get MediaType object","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
3ddeb998:"[cid:%s] PdhCollectQueryData failed with error %ld","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorWindows::CollectPdhMetrics"
3e20a0aa:"[cid:%s] Failed while push samples while delivering sample","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DeliverSample"
3e87e643:"[cid:%s] Found available output type, nChannels = %u, wBitsPerSample = %u, nSamplePerSec = %u, nAvgBytesPerSec = %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/AVWriter/windows/AVWriter.cpp";"RtcPalConvertAudioMediaType"
3ed6bbda:"[cid:%s] Failed to get Presentation Attribute","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GetFileDuration"
3edb5d1d:"[cid:%s] EmulatedConnectEx fails 0x%x OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaEventSelect"
3f544925:"[cid:%s] pal_audio: async enumeration waiting time for type: %s, time=0x%I64X, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
3f9d56db:"[cid:%s] Could not find audio metrics for device handle (%p)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioCaptureDeviceImpl::PRtcPalVadGetMetricsCBInternal"
3fac0558:"[cid:%s] ActivateAudioInterfaceAsync 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterfaceDispatcher::Invoke"
401c92e6:"[cid:%s] Winrt device info updated on classic device from cache old: vid = %x, pid=%x, eConnection=%d, new: vid = %x, pid=%x, eConnection=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateWinRTDeviceInfoFromCache"
4045a3c5:"[cid:%s] EnableVirtualCaptureRequestSampleThread succeeded, enable = %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::EnableVirtualCaptureRequestSampleThread"
40ae61c7:"[cid:%s] pal_audio: GetPropStoreAndDeviceNameThreadFunc starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
4110f216:"[cid:%s] EmulatedConnectEx: Status: 0x%x OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
4181c5f6:"[cid:%s] PopulateInfoFromPropStoreThreadFuncParams failed with error hr=%x. Using default values for prop store fields","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
41e87572:"[cid:%s] Existing queued samples got reset: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::DrainQueuedVirtualAudioSamples"
41ee2ad2:"[cid:%s] DeInit","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::DeInit"
42175c14:"[cid:%s] Read winrt PID from cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateWinRTDeviceInfoFromCache"
4275870f:"[cid:%s] Failed to populate WinRT device information and skip with hr=0x%08X. ","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateWinRTAudioDevicesAndUpdateWinClassicDeviceInfo"
4299e8f0:"[cid:%s] pal_audio: GetDefaultDeviceIdsThreadFunc waiting time, time=%llu, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
42feaf04:"[cid:%s] Successfully read gain control info from cache for device: %ls, setting is: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ReadGainControlInfoFromCache"
4324539a:"[cid:%s] Using timeout mode to query device gain info. Overall timeout count: %d. Max allowed timeout count overall: %dTimeout count for current device: %d. Max allowed timeout count per device: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
43afc689:"[cid:%s] !m_spEPVolume Not setting master volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
43e2834e:"[cid:%s] pal_audio: enumerate virtual audio devices failed, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
43f0a262:"[cid:%s] Successfully read result from cache, %s. key: %ls, value: %d, timeStampInSecond: %llu, cache valid duration in second: %llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::ReadFromCache"
44155072:"[cid:%s] AudioCapture (%ls): Start","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Start"
44c5f192:"[cid:%s] Start running %s async","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalThreadHelper.cpp";"WaitForCompletion"
44fc0c47:"[cid:%s] forceMonoForUnspecifiedFmt = %d, isCapture: %d, the default format has %d channels, trying to change it to mono channel, which is successfully supported by the audio client.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
4549e8f2:"[cid:%s] SetAudioVolume failed with hr=%0x Not setting speaker master volume to %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
45e4fab3:"[cid:%s] Topology traversal of %ls: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
46676824:"[cid:%s] Failed to get volume on active source. Using default mode to return volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
467a41b2:"[cid:%s] pal_audio: VolumeControlThreadFunc waiting time, time=0x%I64X, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::VolumeControlWithTimeout"
4691cfe8:"[cid:%s] Setting system mute for device %ls to %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
469c394c:"[cid:%s] EmulatedAcceptEx: WSAEventSelect fails 0x%x synchronously OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncAcceptViaEventSelect"
46e27fdd:"[cid:%s] Failed to SetEvent on m_hCloseEvent","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::StopDevicePropertyChangeWorkItem"
46e3087b:"[cid:%s] Using timeout mode to query device gain fields. Overall timeout count: %d. Max allowed timeout count overall: %dTimeout count for current device: %d. Max allowed timeout count per device: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
46fc2c72:"[cid:%s] Write min gain DB to cache failed for writing setting to cache for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveGainControlInfoToCache"
470b27ba:"[cid:%s] The remote desktop status is %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
471c7125:"[cid:%s] deviceId %ls type 0x%x sessionMute %u fltVolSession %f sysMute %u fltVolSystem %f fltVolSystemDb %f fltVol(%f %f %f)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::GetEndpointVolume"
472affa6:"[cid:%s] Format Ok","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
47b4bb19:"[cid:%s] Couldn\'t find a matching protocol entry, will rely on the defaults","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Initialize"
47b6d318:"[cid:%s] Releasing previous voipCallCoordinator","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalVoipCallCoordinator.cpp";"voipCallCoordinator::RtcPalVoipCallCoordinator::CreateVoipCallCoordinator"
4800e0a4:"[cid:%s] Child handler do not support alternative initial configs for its scenario, respect the suggested initial config.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
480bca99:"[cid:%s] The real error hr = %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDeviceErrorCodeFromHResult"
4870f6b5:"[cid:%s] Successfully read device format info from cache for device: %ls, setting is: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ReadDeviceFormatFromCache"
48a33029:"[cid:%s] GetWiFiNicInfo failed. hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalGetSystemConfigurationInfo"
48c2dc07:"[cid:%s] Failed to create ActivateIEndpointVolumeThreadFuncParams","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
48fe7910:"[cid:%s] Failed to populate guid container id for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for guid contrainer id","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
49665d4c:"[cid:%s] Device is in blocklist. Deep VQE is not available for device with pal_audio: enum[certifiedDeviceId=%ls,isForceBlocked=%d]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateDeepVQEAllowedOnDeviceFlag"
4996c8b0:"[cid:%s] GetResult fail 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterface"
49ba2b1d:"[cid:%s] Set ducking options failed with error 0x%x.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
49d853a2:"[cid:%s] Sending typing callbacks to up level.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::TypingDetectProc"
4a2c0322:"[cid:%s] ActivateVolumeNotification successful","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
4a316e55:"[cid:%s] Create audio device thread timer event failed with error:%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::InitializeAudioDeviceThread"
4aa8ea6d:"[cid:%s] SuggestedInitialConfigIDs is empty, and could not find a good alternative config, return the 1st alternative config.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
4ab819b9:"[cid:%s] GetVolumeOnActiveSource failed with hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetVolumeOnActiveSource"
4b9d53e5:"[cid:%s] Failed to Initialize COM and MF","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
4bc2fe5d:"[cid:%s] Error(%d),Can\'t Set MF_MT_SUBTYPE GUID","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
4bfee3b4:"[cid:%s] Error(%d),Failed to create SourceReader from URL","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
4c06e0f7:"[cid:%s] GetMixFormat failed and use default format. hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
4c12ea8d:"[cid:%s] Failed to populate pnp name for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for pnp name and connectinon type","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
4c30c6f4:"[cid:%s] Failed to get device prop store. Will use default values for prop store fields","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
4c488de5:"[cid:%s] ConnectEx OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaConnectEx"
4c58599a:"[cid:%s] pal_audio: VirtualDeviceVolumeControlThreadFunc waiting time, time=%llu, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::VolumeControlWithTimeout"
4c9e9cdf:"[cid:%s] Failed to set Resampler Input Type","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GenerateMFT"
4ce35560:"[cid:%s] AudioRender (%ls): Activate - Clock frequency from IAudioClock does not match device format. ClockFrequency=%llu, AvgBytesPerSec=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
4cf37f5c:"[cid:%s] Force audio render happens","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::AudioDeviceThreadProc"
4e23a8b0:"[cid:%s] RegisterSocket socket=%p: already registered","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::RegisterSocket"
4e7585cc:"[cid:%s] pDevInfo is nullptr","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateWinrtDeviceInfoOnClassicDevice"
4e8ac833:"[cid:%s] device client: %p, time:%llu, pSample:%p, enqueued:%d, totalSamplesEnqueued:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DeliverSample"
4ea07195:"[cid:%s] Trying to collect mic geometry array info for device: %ls, deviceid: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceMicArrayGeometryToCollection"
4ed2f0c2:"[cid:%s] Listen succeeds backlog=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Listen"
4f626c1f:"[cid:%s] WifiStats: sendRate:%lu, recvRate:%lu, sendKbps:%lu, recvKbps:%lu, transFrameCount:%llu","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorPlatform::OnTimerCallback"
4f932bcd:"[cid:%s] Failed to stop trace session. status=%lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartLogSession"
5016cc67:"[cid:%s] Priority score is not set for device %d. Device list will not be sorted","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
5115d12e:"[cid:%s] SetDuckingPreference for render device faild with 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
514a972a:"[cid:%s] Enabled Audio Effects = %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::GetEnabledAudioProcessingFeatures"
51b235de:"[cid:%s] Delete device failed with return: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::RemoveDevice"
51e5428f:"[cid:%s] pal_audio: PopulateInfoFromPropStoreThreadFuncParams starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
51f21910:"[cid:%s] Triggered device change notification for delayed retry","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"propertyValueChangedInEnum"
523274c2:"[cid:%s] OS logging: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::RtcPalDevicePlatformWASAPI"
52a240ca:"[cid:%s] MFStartup unexpectedly return","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/mfhelper/mfhelper.cpp";"RtcPalMFHasAVCapture"
5368c334:"[cid:%s] RAW Stream support set to: %d, clientPropertyEcsFlag: enabled.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::MMDevicePropertyStoreHelper::GetRawStreamSupport"
539fb279:"[cid:%s] ConfigHandlerName = %ls, device type = %d, began RunAndMonitor, maxNumOfRetry = %d.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::RunAndMonitor"
53a1ee42:"[cid:%s] VoipCallCoordinator created successfully","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalVoipCallCoordinator.cpp";"voipCallCoordinator::RtcPalVoipCallCoordinator::CreateVoipCallCoordinator"
5401faf6:"[cid:%s] RtcPalSystemMetricsCollector: Cancelling timer took - %lld ms ","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsBase.cpp";"RtcPalSystemMetricsCollector::SetStart"
5482d7ea:"[cid:%s] [ECS]: StorageKey %s is found, value is: %u","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"InitSettingValue"
54edf049:"[cid:%s] voip call coordinator metrics, coordinator = %p, createVoipCallCoordinatorFailure = 0x%8x, setOnAirMuteFailure = 0x%8x, releaseVoipCallCoordinatorFailure = 0x%8x, mutePercentage = %f, onAirMuteCount = %u, onAirUnmuteCount = %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalVoipCallCoordinator.cpp";"voipCallCoordinator::RtcPalVoipCallCoordinator::~RtcPalVoipCallCoordinator"
5564026c:"[cid:%s] VolumeControlThreadFunc failed with error %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::VolumeControlWithTimeout"
560c4127:"[cid:%s] SetWindowsHookEx fails with 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::RtcPalMsgThreadProc"
560ea6a4:"[cid:%s] Skip update Active %s PnpName %ls for EnableEndPointVolumeControllerPnP","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
57d80b4f:"[cid:%s] pal_audio: UpdateVolumeControlSource[%ls] hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
5809b1a9:"[cid:%s] pal_audio: IAudioClient::Initialize succeeded","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
581edd7c:"[cid:%s] Failed to call CoInitializeEx","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
58cf084d:"[cid:%s] GetType = %d, m_id.GetRawStreamUsage= %d, m_id.GetCommunicationsMode() = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::GetInitialConfigToSetAudioClientPlatformSpecific"
592d1e6d:"[cid:%s] Found webcam device: enum[certifiedDeviceId=%ls, caps=0x%I64X]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateCapabilitiesForWebcam"
595dd866:"[cid:%s] %s@%d: if_WARN(%s) == true","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalTraceCondIfTrue"
59a638e0:"[cid:%s] Get IAudioSessionControl2 for render device faild with 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
5a12f6ad:"[cid:%s] MicGeometryArray for device: %ls, id: %ls | array type: %s| vertical angle range: %f deg to %f deg| horizontal angle range: %f deg to %f deg| frequency range: %hu Hz to %hu Hz| number of microphones: %hu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceMicArrayGeometryToCollection"
5a782599:"[cid:%s] Winrt device info updated on classic device old: vid = %x, pid=%x, eConnection=%d, new: vid = %x, pid=%x, eConnection=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateWinrtDeviceInfoOnClassicDevice"
5a947598:"[cid:%s] pal_audio: device list [type=%d,name=%ls,ff=%d,dad=%d,dcd=%d,caps=0x%I64X,priorityScore=%d,isInternalDevice=%d,isSpatialCapable=%d, isAllowedToUseDeepVQE=%d]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::GetDeviceList"
5b4a0844:"[cid:%s] Volume is set on active source device. Using volume control source to set volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
5b539ffa:"[cid:%s] pal_audio: GetPropStoreAndDeviceNameThreadFunc waiting time, time=%llu, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
5c450d71:"[cid:%s] failed making socket a nonblocking socket: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::DoAccept"
5c8c1df8:"[cid:%s] Fail to initialize UI work item: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterface"
5d511be3:"[cid:%s] [ECS]: Reset ecs key: %s/%s to default","C:/_work/1/s/MSRTC/msrtc/src/common/ecs/EcsSettings.cpp";"MsrtcEcsSettings::Settings::Reset"
5d5fffc8:"[cid:%s] Privacy sensitive API permission scope = %u","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalWiFiInfo.cpp";"RtcPal::WiFiInspector::GetInfo"
5d704911:"[cid:%s] num of effects detected = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioStreamEffectsCallBack::OnAudioEffectsChanged"
5dbfc992:"[cid:%s] Invalid boost value, minLevelDb(%f) > maxLevelDb(%f)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
5e06d83c:"[cid:%s] Failed to flush source reader","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::FlushSourceReader"
5e6bd626:"[cid:%s] device property change event proc has woken up, current time = %I64d ms","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::DevicePropertyChangedEventProc"
5e98537f:"[cid:%s] WaitForCompletion failed for %s with hr = %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"WaitForCompletionIfNeccessary"
5f26229d:"[cid:%s] Stop OS logging","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.h";"ScopedRtcPalAudioOSLogHelper::~ScopedRtcPalAudioOSLogHelper"
5f4312c7:"[cid:%s] ControlTrace(stop) failed with %lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
5f884427:"[cid:%s] ReorderDeviceList ECS key is set to False. Not setting device priority score","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SetDevicePriorityScore"
5fae5d52:"[cid:%s] Failed to get vid pid for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for vid, pid","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
5fb4995b:"[cid:%s] Using timeout mode to query device format. Overall timeout count: %d. Max allowed timeout count overall: %dTimeout count for current device: %d. Max allowed timeout count per device: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetAudioClient"
60032d17:"[cid:%s] spDevice->Activate with context = %lu failed with error: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioActivationHelperWASAPI.cpp";"RtcPalDeviceAudioActivationHelperWASAPI::ActivateAudioInterface"
604c7b84:"[cid:%s] Invalid arg. !pTimeOutCountCurrentDevice. Not querying device prop store fields. Will use default values","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
60519c92:"[cid:%s] pal_audio: enumeration is disabled completely, no audio device usage expected","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
6072c378:"[cid:%s] DeviceMicArrayGeometry created with # %hu mic coordinates.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceMicGeometry.h";"DeviceMicArrayGeometry::DeviceMicArrayGeometry"
614d850b:"[cid:%s] AudioRender (%ls): Stop - ReleaseBuffer=%u FramesRendered=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
61cf4312:"[cid:%s] Sample Consumed callback invoked: func=0x%p, context=0x%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::Release"
61facfd9:"[cid:%s] accept fails: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::DoAccept"
621499ce:"[cid:%s] update raw stream support for device %ls on machine %s as %d.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateDeviceInfoForRawModeCapable"
62823253:"[cid:%s] activate virtual device with return: %x, openCount = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADActivateCBackInternal"
62bdf7e3:"[cid:%s] Failed to find any output format","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/AVWriter/windows/AVWriter.cpp";"RtcPalConvertAudioMediaType"
63289a79:"[cid:%s] ControlTrace(flush) failed with %lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::FlushAudioOSLogging"
636f7dc3:"[cid:%s] GetDefaultDeviceIdsThreadFunc failed with timeout error for eRole: %d. Will mark first device as default device. Timeout count overall: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
63e8d5ce:"[cid:%s] Failed to resample data","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OnReadSample"
6414e75c:"[cid:%s] ReorderDeviceList ECS key is set to False. Not setting device priority score","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
643c35f4:"[cid:%s] spEnumerator->GetDefaultAudioEndpoint failed. Will set first enumerated device as default device. hr: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdsThreadFunc"
64778c65:"[cid:%s] Successfully saved gain control info to cache for device: %ls, setting is: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveGainControlInfoToCache"
648c89cd:"[cid:%s] Failed to GetMixFormat in TryGetMixFormat, hr=%x.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"TryGetMixFormat"
6499360c:"[cid:%s] Failed on sending the property request to the device driver: hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetMicArrayGeometryIfAvailable"
655e908b:"[cid:%s] Volume is set on non-active device. Using default mode to set volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
65d98698:"[cid:%s] ActivateIAudioEndpointVolumeThreadFunc failed with error hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
6624149c:"[cid:%s] Inited: AddressFamily %d Win32 %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::Init"
664ccbca:"[cid:%s] ActivateMMDeviceInterfaceThreadFunc failed with error hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
66bd6b09:"[cid:%s] Cannot find entry from cache for key: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::ReadFromCache"
6743822b:"[cid:%s] Get ducking control service failed with error 0x%x.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
680ef23b:"[cid:%s] Specified format is not supported directly and skip handling, hr=%0x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
6826f6ef:"[cid:%s] Not initialized yet","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::SetAutoBoostControlEnabled"
69355a90:"[cid:%s] pal_audio: activate[nullptr] hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
69bd092a:"[cid:%s] Read winrt VID from cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateWinRTDeviceInfoFromCache"
69c97a18:"[cid:%s] Enter Media Streaming Mode: guid(%s) Win32=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"RtcPalWlanMSM::DelayedEnterMSM"
6ad3a29b:"[cid:%s] Failed to populate form factor info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for device form factor","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
6affca19:"[cid:%s] ActivateAudioStreamEffectNotification failed with errorcode = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
6bdfc04f:"[cid:%s] AudioRender (%ls): Destroyed","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::~RtcPalDeviceAudioRenderWASAPI"
6c4bc69d:"[cid:%s] Num of enum operations timeouts exceeded max allowed count. Enum operations timeout count overall: %d Max allowed timeout count overall: %dEnum operations timeout count for current device: %d Max allowed timeout count per device: %dNot querying device format. Will use default values","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetAudioClient"
6c60a6e5:"[cid:%s] PdhOpenQueryW failed with error %ld","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorWindows::CollectPdhMetrics"
6d52fe03:"[cid:%s] Deliver simple device event for EDeviceHostClosed","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::PRtcPalVadDestructDeviceCBInternal"
6d7b8b39:"[cid:%s] Failed to populate pnp name for device: id=%ls. Error code returned: hr=%x. Using default values for pnp name and connectinon type","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsThreadFunc"
6d94ee0b:"[cid:%s] DeviceId %ls not found in map size %zu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::ReleaseAudioEndpointVolumeIntf"
6dbb0ff8:"[cid:%s] ControlTrace(stop) failed with %lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StopAudioOSLogging"
6deb73f9:"[cid:%s] Error happens while running one time proc last error: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::AudioDeviceThreadProc"
6e06fd50:"[cid:%s] Query Gain hr 0x%x hardwareSupport %u min %f max %f step %f","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::GetAudioVolumeGainInfo"
6e320e4d:"[cid:%s] Winrt device info updated on classic device (ID=%ls):WinRTID = %ls, lid: %d, dock: %d, panel: 0x%08x, certifiedDeviceId = %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateWinrtDeviceInfoOnClassicDevice"
6e3e35d7:"[cid:%s] Volume is queried on active sink device. Using volume control source to return volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
6eec02ee:"[cid:%s] Invalid stepLevelDb, (maxLevelDb[%f] - minLevelDb[%f]) / stepLevelDb[%f] is not a integer","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
6f09279a:"[cid:%s] Return ranked config and recovery action IDs under constraint: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalInitAudioClientConfigHandler::GetAvailableConfigsAndRecoveryActionsByPriority"
6fc610cb:"[cid:%s] Sample Ref=%d after release, Func=0x%p, Context=0x%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::Release"
6fcb6d03:"[cid:%s] Unable to allocate %d bytes for properties structure.\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
70179b2b:"[cid:%s] OnAddrChangeEventSignaled: unexpected error: WSAIoctl return error %d: AddressFamily: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::OnAddrChangeEventSignaled"
7077b28a:"[cid:%s]  unknown Exception when accessing map size %zu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::GetAudioEndpointVolumeIntf"
708ecc1b:"[cid:%s] pal_audio: not using raw mode as raw mode not supported (%d) or default mode requested (%d)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientPlatformSpecific"
7092d5e5:"[cid:%s] pal_audio: async enumeration starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
70d50d96:"[cid:%s] AudioLoopback (%ls): Destroyed","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::~RtcPalDeviceAudioLoopbackWASAPI"
711066e7:"[cid:%s] Set speaker system volume to: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
71bdb05c:"[cid:%s] Failed enqueue sample to the sample queue","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADReleaseBufferCBackInternal"
7252887b:"[cid:%s] boost info: autoBoostControlSupported = %d, manualBoostControlSupported = %d, minLevelDb = %f, maxLevelDb = %f, stepLevelDb = %f","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
72765c3b:"[cid:%s] pal_audio: VirtualDeviceVolumeControlThreadFunc starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::VolumeControlWithTimeout"
72af4774:"[cid:%s] spDevice->Activate with INPROC_SERVER context failed with error: %x, try recovery with ALL context: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioActivationHelperWASAPI.cpp";"RtcPalDeviceAudioActivationHelperWASAPI::ActivateAudioInterface"
72dcc6bb:"[cid:%s] The remote desktop status is %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
730bd1fd:"[cid:%s] Failed to populate raw stream support info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for raw support","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
7346a42c:"[cid:%s] pal_audio: populate more information for device (id=%ls, vid=%x, pid=%x, connection=%d).","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
742fe702:"[cid:%s] Failed to get client session vol controller. hr: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
743f36d3:"[cid:%s] Volume is queried on active source device. Using volume control source to return volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
74b420a7:"[cid:%s] pal_audio: after reordering, [type=%d,fn=%ls,pnp=%ls,ff=%d,dad=%d,dcd=%d,caps=0x%I64X, priorityScore=%u]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
751f5710:"[cid:%s] Releasing devId %ls from map size %zu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::ReleaseAudioEndpointVolumeIntf"
7542d7ce:"[cid:%s] Create audio device thread failed with error: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::InitializeAudioDeviceThread"
75508d71:"[cid:%s] GetNework failed. hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
7554ae25:"[cid:%s] Cannot find any good alternative configs that has not failed before.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetRecoveryActionAndNewConfigID"
7564981c:"[cid:%s] Closed","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Close"
764a2616:"[cid:%s] pal_audio: Retrying audio client initialization due to failure: hr = %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClient"
765bafb5:"[cid:%s] EmulatedAcceptEx fails 0x%x synchronously OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncAcceptViaEventSelect"
77815109:"[cid:%s] No device were found with geometry supported, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::GetDeviceArrayGeometry"
7866e7e4:"[cid:%s] Failed to set mmcss thread characteristic with errorcode: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/kernel/windows/win32/thread.cpp";"RtcPalThreadContext::AttachMmcss"
78933413:"[cid:%s] pal_audio: IAudioClient::GetMixFormat returns unsupported format by pipeline, bitsPerSample=%u, try to recover: %d, isCapture: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
7918706f:"[cid:%s] Session Vol: %d Scaling Factor: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
795053ca:"[cid:%s] pal_audio: we are enumerating only virtual devices","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
7a249ce2:"[cid:%s] Get IAudioSessionControl for loopback device faild with 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Activate"
7a6dcc90:"[cid:%s] Failed to startup MF","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::InitMediaFoundation"
7a97a16c:"[cid:%s] Attempted to set system mute before unmuting for device %ls to %d with return %0x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
7b23d56e:"[cid:%s] WaitForCompletion hanging for %u times for %s, back to sync mode for this call","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"WaitForCompletionIfNeccessary"
7bb024b3:"[cid:%s] pal_audio: terminal type from usb controller: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
7bd0f986:"[cid:%s] VirtualDeviceVolumeControlThreadFunc failed with error %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::VolumeControlWithTimeout"
7d317d3d:"[cid:%s] Session volume at end of call: %d is different from init setting: %d. Caching user override of speaker session volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
7d38ce1e:"[cid:%s] AcceptEx succeeds synchronously OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncAcceptViaAcceptEx"
7ee53d2e:"[cid:%s] pal_audio: ActivateIAudioEndpointVolumeThreadFunc starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
7f385018:"[cid:%s] Failed to get volume on active sink. Using default mode to return volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
7f61d859:"[cid:%s] Microphone friendly name: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetMicArrayGeometryIfAvailable"
7f974dbe:"[cid:%s] Virtual Platform has not been initialized!","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalCreateVirtualAudioDevice"
7fda5a86:"[cid:%s] %s. hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDeviceReadDeviceSettingFromCache"
803a436b:"[cid:%s] No enough memory to create AsyncParas","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::RtcPalDeviceAudioBoostControlImpl"
80b43745:"[cid:%s] Raw mode got used but device doesn\'t support it","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClient"
80e79139:"[cid:%s] Failed to create virtual device. Duplicate device ID found!","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::CreateVirtualAudioDevice"
81fdff8e:"[cid:%s] Really stop logging for: %d, force flag: %d\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StopAudioOSLogging"
8251bcdc:"[cid:%s] Device list is null","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
826bca95:"[cid:%s] clear device info collection of type RtcPalDeviceType_e(%d) failed with hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceInfoListToCollection"
82b6d7c9:"[cid:%s] ConnectEx fails 0x%x OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaConnectEx"
8328ce7d:"[cid:%s] Model Id for %ls is %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::FillModelIdAndManufacturer"
832b34a8:"[cid:%s] GetName failed. hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
847a6422:"[cid:%s] Num of enum operations timeouts exceeded max allowed count. Enum operations timeout count overall: %d Max allowed timeout count overall: %dEnum operations timeout count for current device: %d Max allowed timeout count per device: %dNot querying device pnp name and related fields. Will use default values","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
849720ef:"[cid:%s] Posting device change notification due to ASP telemetry change","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::SetASPTelemetry"
85132c83:"[cid:%s] num of effects detected = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::GetAudioBlurStatus"
852b5976:"[cid:%s] GetNeworkId failed. hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
85871355:"[cid:%s] Using timeout mode to query device format. Overall timeout count: %d. Timeout count for current device: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
85976a7c:"[cid:%s] Write winrt VID to cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveWinRTDeviceInfoToCache"
85a792fa:"[cid:%s] socket close duration was longer than expected: %f seconds.","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/CloseSocketOffload.cpp";"CloseSocketOffload::CloseSocket"
85b92c29:"[cid:%s] EnableTrace for provider %d failed with %lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
85be53f0:"[cid:%s] Successfully marked cache as deleted for key: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::MarkAsDeletedFromCache"
85c379f1:"[cid:%s] Not setting speaker master volume at activation since device is system muted","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
86733ed2:"[cid:%s] SuggestedInitialConfigIDs are either empty or have all failure recorded in the cache, searching for alternatives","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
8747e9dc:"[cid:%s] Failed to QI for IMFTransform interface","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::CreateResamplerMFT"
8777b030:"[cid:%s] ppMicArrayGeometry == NULL, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::GetDeviceArrayGeometry"
87875dff:"[cid:%s] Using timeout mode to query gain fields and device prop store info. Overall timeout count: %d. Timeout count for current device: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
8833d192:"[cid:%s] Request stop logging for: %d\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StopAudioOSLogging"
8867e418:"[cid:%s] No sample in the lock free queue. time:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
897ba6f2:"[cid:%s] rtDevTS = %llu,  rtQPCTS = %llu, total samples queued = %llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADReleaseBufferCBackInternal"
8ab10071:"[cid:%s] Hit exception in async creating thread to call %s 1st time, error: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalThreadHelper.cpp";"WaitForCompletion"
8b8772d6:"[cid:%s] Failed to get vid pid for device: id=%ls. Error code returned: hr=%x. Using default values for vid, pid","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsThreadFunc"
8b9f55c6:"[cid:%s] Releasing session control","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
8c67ba53:"[cid:%s] pal_audio: there are multiple enumerations happening at same time, this is not expected (%d)!","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
8c7b1a36:"[cid:%s] AddressFamily=%u Protocol=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Initialize"
8cc61d11:"[cid:%s] Flush WPP Event: EventsLost %lu LogBuffersLost %lu NumberOfBuffers %lu FreeBuffers %lu BuffersWritten %lu","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"DumpWppStatistics"
8df322a2:"[cid:%s] Session Mute: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
8ea0ef4d:"[cid:%s] EmulatedAcceptEx: DoAccept fails: 0x%x queue OVERLAPPED %p for completion","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectAccept"
8f256b7f:"[cid:%s] Virtual device created: type= %d pVdevice= %p, total: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::CreateVirtualAudioDevice"
8f2a2083:"[cid:%s] [ECS]: StorageKey %s is found, value is: %s","C:/_work/1/s/MSRTC/msrtc/src/common/ecs/EcsSettings.cpp";"MsrtcGetPersistentSetting"
8f475c89:"[cid:%s] Failed to get audio client. hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetAudioClient"
8f6c1fb7:"[cid:%s] Failed to recieve sample from Resampler","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
8f7c8660:"[cid:%s] Unmuted speaker session volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
8fb9b633:"[cid:%s] Could not find any dirty cache entry to write to registry.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::WriteAllCacheToRegistry"
9043998b:"[cid:%s] Could retry previous config for the AUDCLNT_E_CPUUSAGE_EXCEEDED error. Count of error occurrence in usage history = %d, limit = 3","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalInitAudioClientConfigHandler::GetAvailableConfigsAndRecoveryActionsByPriority"
906394fc:"[cid:%s] pal_audio: ActivateMMDeviceInterfaceThreadFunc starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
909139cc:"[cid:%s] skipPopulateGain %d isQueryGainOnDeviceOpen %d isReadGainControlFromCache %d device type %d id %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
909c6c66:"[cid:%s] Successfully read cached value for setting: %s. Value: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/rtcpaldeviceaudioregistryhelper.cpp";"RtcPal::ReadDeviceSettingFromCache"
90ae7537:"[cid:%s] Query registry key value failed.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/windows/RtcPalDeviceUtilsWinClassic.cpp";"RtcPalDeviceUtils::GetMachineId"
90d07df8:"[cid:%s] Error(%d), Failed to get reader first audio stream type","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
90d2c023:"[cid:%s] No microphone array found.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetMicArrayGeometryIfAvailable"
90d9c06d:"[cid:%s] DNS found, can we set the state: %d , isAudioBlurEnabled = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::SetAudioBlurEnabled"
9149097b:"[cid:%s] enumerationControlMode: %d, disabling container id check enum: %d.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformIsSamePhysicalDevice"
9154b7fe:"[cid:%s] %s@%d: if_TRUE(%s) == true","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalTraceCondIfTrue"
915c6c1a:"[cid:%s]  pEffect ={%08lX-%04hX-%04hX-%02hhX%02hhX-%02hhX%02hhX%02hhX%02hhX%02hhX%02hhX}","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::SetAudioBlurEnabled"
91637e1d:"[cid:%s] Failed to initialize Callback","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
91f6d551:"[cid:%s] SetVolumeOnActiveSource failed with hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::SetVolumeOnActiveSource"
92010127:"[cid:%s] Error(%d), Failed to create Resampler MFT","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
928e9398:"[cid:%s] pal_audio: reset the form actor from RPADFormFactor_End to RPADFormFactor_UnknownFormFactor","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
92aef735:"[cid:%s] Updating asp model id from %ls to %ls for device %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::UpdateDeviceInfoWithASPTelemetry"
92dbf725:"[cid:%s] Failed to create GetDefaultDeviceIdThreadFuncParams","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
93185b30:"[cid:%s] ecsSpatialDeviceForceCapableList: %s, ecsSpatialDeviceForceBlockedList=%s. ecsSpatialDeviceDefault=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateSpatialAudioSupport"
9346a583:"[cid:%s] Set parameter = %d, Changed the state, isAudioBlurOn: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::SetAudioBlurEnabled"
945c6e79:"[cid:%s] Socket destroyed","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::~RtcPalSocket"
955997fd:"[cid:%s] tries to decide on whether need to recover or not, hr=0x%08X, totalCountOfFailures=%d, continueoutsCountOfFailure=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::IsInitializationFailureRecoverable"
964d6ea3:"[cid:%s] No available configs to try.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetRecoveryActionAndNewConfigID"
96fea1fe:"[cid:%s] Write winrt WINRTID to cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveWinRTDeviceInfoToCache"
972944ec:"[cid:%s] pal_audio: IAudioClient::Initialize succeeded","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
97979529:"[cid:%s] asyncInfo->get_ErrorCode failed with hr = %8x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/winrt/PnpAsyncCompletionHandler.h";"RtcPalAsyncCompleteHandler::Invoke"
979b28d9:"[cid:%s] pal_audio: populate volume control information succeeded (id=%ls, mingain=%d, maxgain=%d, gainstep=%d, scalingfactor= %d, hardwaregainsupport=%d).","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
97e237f4:"[cid:%s] [ECS]: Set ecs key: %s/%s, value: %s","C:/_work/1/s/MSRTC/msrtc/src/common/ecs/EcsSettings.cpp";"MsrtcEcsSettings::Settings::Set"
980f01a9:"[cid:%s] AcceptEx: SO_UPDATE_ACCEPT_CONTEXT fails 0x%x OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
982b24b3:"[cid:%s] Failed to create socket close offload workitem object with error 0x%x.","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/CloseSocketOffload.cpp";"CloseSocketOffload::Initialize"
98702552:"[cid:%s] Attempted to set system mute before unmuting for device %ls to %d with return %0x hr = %0x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::SetEndpointVolume"
98b152ec:"[cid:%s] GetMessage returns -1 %d times before current valid msg=%d.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::RtcPalMsgThreadProc"
98d3abe2:"[cid:%s] IsEndpointAMicArray: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::IsEndpointAMicArray"
9964b499:"[cid:%s] Write winrt certified ID to cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveWinRTDeviceInfoToCache"
9971a1ec:"[cid:%s] pal_audio: populate device format succeeded (id=%ls, channel=%d, samplerate=%d, bitspersample=%d).","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
997ead60:"[cid:%s] Inited: AddressFamily %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::Init"
9996ac1c:"[cid:%s] ConnectEx succeed OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnConnectExCompletion"
99cbace5:"[cid:%s] Master volume at end of call: %d is different from init setting: %d. Caching user override of speaker master volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
99de4aed:"[cid:%s] Didn\'t find matching output format with SamplesPerSec %u,found closest SamplesPerSec %u instead, its AvgBytesPerSecond is %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/AVWriter/windows/AVWriter.cpp";"RtcPalConvertAudioMediaType"
9a2a4eea:"[cid:%s] spDCDevice->GetId failed. Will set first enumerated device as default device. hr: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdsThreadFunc"
9a5d5b4e:"[cid:%s] Retry is not allowed, do not try to detect and find alternative initial configs.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
9aac0d12:"[cid:%s] skip one device since category check failed, type=%s, id=%ls, uRawFormFactor=%d, ulCategoryMask=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
9b124814:"[cid:%s] enum[guidJackSubType=%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x],form factor=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
9b4c565a:"[cid:%s] pal_audio: IAudioClient::Initialize failed, hr=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
9b889abf:"[cid:%s] DeviceEffects for device %ls before change = %x, after Change = %x, effect filled = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::OnDeviceEffectsChange"
9bac8fa9:"[cid:%s] time:%llu, pSample:%p, enqueued:%d, totalSamplesEnqueued:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DeliverSample"
9c15688d:"[cid:%s] Getting geometry info, for device: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalPlatformExtension.cpp";"RtcPalPlatformExtension::GetDeviceMicArrayGeometry"
9c87f93c:"[cid:%s] IsEndpointAMicArray: Query the connector in the adapter device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::IsEndpointAMicArray"
9cc1519a:"[cid:%s] pal_audio: replace the form factor with usb controller[tertype=0x%x, new ff=%d, updated ff=%d]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
9e250035:"[cid:%s] pal_audio: adding virtual device, eType = %d, privateId = %ls, device = %p, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::RegisterVirtualAudioDevices"
9ee7f899:"[cid:%s] Failed to create Resampler","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GenerateMFT"
9f9e8bc2:"[cid:%s] Failed to GetName with hr = %#x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::GetPart"
9fd40509:"[cid:%s] Hit exception in async creating thread to call %s 2nd time, error: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalThreadHelper.cpp";"WaitForCompletion"
a0797fd7:"[cid:%s] Request start logging for: %d\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLogging"
a07acbea:"[cid:%s] Client session volume set to: %f","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
a086e2c0:"[cid:%s] Can\'t dynamic_cast POSRcallback","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::InitializeCallback"
a0f02f98:"[cid:%s] spEnumerator->GetDefaultAudioEndpoint failed for DAD, hr = %x. Will set first enumerated device as default audio device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
a1578474:"[cid:%s] Exception occurred when new RVDDeviceInfo_t, %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
a15f0dee:"[cid:%s] Start virtual audio device failed: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADStartCBackInternal"
a1d3bae2:"[cid:%s] Another instance of RtcPalMsgThread already exists","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::CreateMsgThread"
a304e0fe:"[cid:%s] Releasing endpoint volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
a32ab834:"[cid:%s] Invalid value received for speaker session volume ECS setting: %d . Ignoring ECS value and not setting session volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
a333904f:"[cid:%s] Error while stopping device: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADStopCBackInternal"
a34685db:"[cid:%s] Found bluetooth dongle device: enum[certifiedDeviceId=%ls, caps=0x%I64X, con=%d]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateCapabilitiesForBluetoothDongle"
a3560b9f:"[cid:%s] AudioCapture (%ls): Activate - FormatTag=%u Channels=%d SamplesPerSec=%d BitsPerSample=%u 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
a36bc344:"[cid:%s] Failed to set Resampler Output Type","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GenerateMFT"
a3981553:"[cid:%s] Client session volume before incall stream start: %f","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
a4a9f102:"[cid:%s] GetRecoveryActionAndNewConfigID returns false.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::RunAndMonitor"
a4f8106e:"[cid:%s] exiting device property change event proc. dwStatus=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::DevicePropertyChangedEventProc"
a522dc90:"[cid:%s] Invalid boost value, stepLevelDb = %f","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
a568c8b4:"[cid:%s] AudioLoopback (%ls): Activate - Clock frequency from IAudioClock does not match device format. ClockFrequency=%llu, AvgBytesPerSec=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Activate"
a629e4f0:"[cid:%s] AcceptEx: SO_UPDATE_ACCEPT_CONTEXT succeeds OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
a6414f3d:"[cid:%s] Start audio OS tracing for %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
a6577ab8:"[cid:%s] RtcPalInitProtocols failed. WSA=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"RtcPalInitProtocols"
a7057da4:"[cid:%s] Invalid arg. !pTimeOutCountCurrentDevice. Not querying device gain info. Will use default values","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
a77c6ad8:"[cid:%s] AcceptEx fails 0x%x OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
a78400a1:"[cid:%s] Failed to allocate output sample for Resampler","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
a7fcb2a1:"[cid:%s] deviceExtId or m_pRealPlatform == nullptr,  hr = %8x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalPlatformExtension.cpp";"RtcPalPlatformExtension::GetDeviceMicArrayGeometry"
a823873a:"[cid:%s] Failed to SetEvent on m_hCloseDoneEvent","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::DevicePropertyChangedEventProc"
a8691359:"[cid:%s] Audio Effects notification received, eventcallCount = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioStreamEffectsCallBack::OnAudioEffectsChanged"
a8692e35:"[cid:%s] pal_audio: ActivateIAudioEndpointVolumeThreadFunc waiting time, time=%llu, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
a8a0a5a6:"[cid:%s] System dB Vol: %d Scaling Factor: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
a8df6840:"[cid:%s] This is our first retry but could not find any good alternative configs. Try 1st config on the list one more time.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetRecoveryActionAndNewConfigID"
a95b22ba:"[cid:%s] RecreateAudioClient failed, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientWithResilienceHandling::<lambda_...>::operator ()"
a9d7a2ed:"[cid:%s] ConfigHandlerName: %ls, device type: %d, constraint: %s, realTimeUsageHistory: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalInitAudioClientConfigHandler::GetAvailableConfigsAndRecoveryActionsByPriority"
a9e21bc8:"[cid:%s] Failed to set context","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
a9ea8bf3:"[cid:%s]  pEffect %d %d %d %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::GetAudioBlurStatus"
aa377483:"[cid:%s] spDCDevice->GetId failed for DCD, hr = %x. Will set first enumerated device as default communications device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
aad81c3b:"[cid:%s] EnableAsyncOSActions is true","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::Initialize"
ab06bb4a:"[cid:%s] clear device microphone array geometry collection failed with hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceMicArrayGeometryToCollection"
ab08d32f:"[cid:%s] pal_audio: ActivateMMDeviceInterfaceThreadFunc waiting time, time=%llu, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
abac17ee:"[cid:%s] m_pAsyncParas->pLoudnessControl->GetEnabled failed with hr = %#x ","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::GetAutoBoostControlEnabled"
ac537408:"[cid:%s] spEndpoints->Item failed in TryGetMixFormat, hr=%x.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"TryGetMixFormat"
add3fe76:"[cid:%s] Audio session disconnected. Disconnect reason: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::NotifySessionDisconnect"
afa9997b:"[cid:%s] Failed to get the function name","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/common/windows/RtcPalWinUtils.cpp";"wcsncpy_s_trace"
afb4beb0:"[cid:%s] Failed to allocate IMFMediaBuffer for Resampler","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
b0fbd4bd:"[cid:%s] pal_audio: %ls finished, device type = %d, isStreaming = %d, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientWithResilienceHandling"
b1412f8f:"[cid:%s] Failed to get audio client in TryGetMixFormat, hr=%x.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"TryGetMixFormat"
b143fddb:"[cid:%s] Get ISimpleAudioVolume successful","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
b146b89a:"[cid:%s] pal_audio: PopulateInfoFromPropStoreThreadFuncParams waiting time, time=%llu, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
b1c931fc:"[cid:%s] %s@%d: if_ERROR(%s) == true","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalTraceCondIfTrue"
b2998557:"[cid:%s] Read certified ID from cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateWinRTDeviceInfoFromCache"
b317aa4d:"[cid:%s] Failed to open property store for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for non critical device properties","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
b330f485:"[cid:%s] Failed to end voipCoordinator call, hr=0x%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalVoipCallCoordinator.cpp";"voipCallCoordinator::RtcPalVoipCallCoordinator::ReleaseVoipCallCoordinator"
b36af14f:"[cid:%s] Error happens while waiting for render event, last error: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::AudioDeviceThreadProc"
b3ca84df:"[cid:%s] async operation failed with hr = %8x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/winrt/PnpAsyncCompletionHandler.h";"RtcPalAsyncCompleteHandler::Invoke"
b4d37480:"[cid:%s] update capability as stereo capable for device: %ls, capabilities: %llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateDeviceCapabilitiesIfCertifiedStereoCapable"
b4ed1b46:"[cid:%s] Get IAudioSessionControl successful","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
b5916cea:"[cid:%s] Failed to create ActivateIAudioClientThreadFuncParams","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
b656c592:"[cid:%s] StartTrace() failed with %lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
b65c3c78:"[cid:%s] Failed to initialize device in exclusive mode at preferred format. Querying device format and attempting to initialize device using format reported by device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
b6810ac7:"[cid:%s] Failed to add IMFMediaBuffer to sample","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
b706b963:"[cid:%s] EmulatedConnectEx: fail to disable WSAEventSelect: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
b77fda5b:"Device id is %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"DeviceIdLoggingComponent::logMachineId"
b7b79b3e:"[cid:%s] Finished running %s async with hr = 0x%8x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalThreadHelper.cpp";"WaitForCompletion"
b8318caf:"[cid:%s] GetEndpointVolume failed hr 0x%x devId %ls eType 0x%x VolumeInfo: MuteSession %d MuteSystem %d volSession %f volSys %f","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetEndpointVolume"
b90c6a4b:"[cid:%s] Queue EmulatedConnectEx OVERLAPPED %p for completion","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
b95687cd:"[cid:%s] pal_audio: enum usb host controller failed, corrupted USBControllerAsyncEnumParams instance","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateUSBControllerAsyncProc"
b9c5155e:"[cid:%s]  Exception %s when accessing map size %zu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalEndpointVolumeController.cpp";"EndpointVolumeController::GetAudioEndpointVolumeIntf"
b9fc7f35:"[cid:%s] AcceptEx succeeds: connection OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
bafe7048:"[cid:%s] %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalDevice.cpp";"RtcPalDeviceTrace"
bb46a72b:"[cid:%s] Failed to CoCreate Resampler","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::CreateResamplerMFT"
bb4ebfc5:"[cid:%s] pal_audio: enum usb host controller failed: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
bb78b949:"[cid:%s] Write device format info to cache failed for writting setting to cache for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveDeviceFormatToCache"
bb90244c:"[cid:%s] GetRecoveryActionAndNewConfigID returns true. New configID to try: %d, description: %s. Recovery action ID: %d, description: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::RunAndMonitor"
bbcb54fd:"[cid:%s] GetPropStoreAndDeviceNameThreadFunc failed with error hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
bbf53caf:"[cid:%s] RecvFrom error 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::RecvFrom"
bbfb7f6c:"[cid:%s] GetDeviceArrayGeometry returning existing geometry info from the map.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::GetDeviceArrayGeometry"
bc0be62e:"[cid:%s] RtcPalGetBestSourceAddress: fails with %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/iphlpapi.cpp";"RtcPalGetBestSourceAddress"
bc952f79:"[cid:%s] ActivateIAudioClientWithTimeout failed with timeout error. Will use default values for device formatTime out counts for current device: %d Time out counts overall: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetAudioClient"
bcf67107:"[cid:%s] SetAudioClientWithPropPlatformSpecific failed, hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientWithResilienceHandling::<lambda_...>::operator ()"
bf50bcd4:"[cid:%s] Found matching output format with SamplesPerSec %u, its AvgBytesPerSecond is %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/AVWriter/windows/AVWriter.cpp";"RtcPalConvertAudioMediaType"
bf83dc9f:"[cid:%s] ConfigHandlerName = %ls, device type = %d, current number of retry = %d, hr = 0x%x, mContinuousCountOfFailures = %d, mTotalCountOfFailures = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::RunAndMonitor"
bfc7f4a7:"[cid:%s] enumerationControlMode: %d, using low access enum: %d.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateWinRTAudioDevicesAndUpdateWinClassicDeviceInfo"
c02be493:"[cid:%s] ActivateAudioStreamEffectNotification successful","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::ActivateAudioStreamEffectNotification"
c11647cc:"[cid:%s] EmulatedConnectEx: fail to disable non-blocking mode: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
c12bb739:"[cid:%s] pal_audio: VolumeControlThreadFunc starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::VolumeControlWithTimeout"
c236b5df:"[cid:%s] CertifiedListId:=%ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::GetCertifiedListDeviceID"
c30d411c:"[cid:%s] MF installation check failed: MFReadWrite.dll missing.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
c32d44f2:"[cid:%s] GetGraphicDeviceInfo failed. hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalGetSystemConfigurationInfo"
c33e63b9:"[cid:%s] EmulatedConnectEx OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaEventSelect"
c34e2d0e:"[cid:%s] pal_audio: GetPnpNameAndRelatedFieldsThreadFunc waiting time, time=%llu, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
c4332a2b:"[cid:%s] pal_audio: activate[%ls] hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
c45dce6c:"[cid:%s] Start OS logging: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.h";"ScopedRtcPalAudioOSLogHelper::ScopedRtcPalAudioOSLogHelper"
c48525ba:"[cid:%s] Queue CloseSocket OVERLAPPED %p for completion","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Close"
c4f5fbdb:"[cid:%s] pal_audio: RecoveryFunc is run due to failure in initializing audio client.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientWithResilienceHandling::<lambda_...>::operator ()"
c532d982:"[cid:%s] stop virtual device with return: %x, openCount = %d, startCount = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioCaptureDeviceImpl::PRtcPALVADStopCBackInternal"
c55b08d6:"[cid:%s] Failed to populate form factor info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for device form factor","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
c57efbc5:"[cid:%s] Working around by adjusting number of channels to %u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
c59003c9:"[cid:%s] GetVolumeOnActiveSink failed with hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetVolumeOnActiveSink"
c593bcb1:"[cid:%s] error occurred while waiting for device property change work item close. dwStatus=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::StopDevicePropertyChangeWorkItem"
c6bebb02:"[cid:%s] MiscStats: MmcssErrorCount:%lu","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorPlatform::OnTimerCallback"
c6dcb934:"[cid:%s] Device is not in blocklist. Deep VQE is available for device with pal_audio: enum[certifiedDeviceId=%ls,isForceBlocked=%d]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateDeepVQEAllowedOnDeviceFlag"
c70309c4:"[cid:%s] Successfully initialized device in exclusive mode using device reported format. FormatTag=%u Channels=%d SamplesPerSec=%d BitsPerSample=%u, ChannelMask=%u, ValidBitsPerSample=%u, SamplesPerBlock=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
c791a9a9:"[cid:%s] The multiple client flag is set to %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::PRtcPALVADActivateCBackInternal"
c7db113f:"[cid:%s] Session volume is 0 at end of call. Not caching user override of speaker session volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
c8255858:"[cid:%s] Failed to set volume on active sink. Using default mode to set volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
c857450b:"[cid:%s] GetDefaultDeviceIdsThreadFunc failed with error hr=%x eRole: %d. Will mark first device as default device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
c8a0e678:"[cid:%s] Could not find a good alternative config, return the first suggested config.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
c9e65090:"[cid:%s] Virtual audio device started, startCount = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADStartCBackInternal"
ca0d1e01:"[cid:%s] Enumerate and update WinRT Device ID failed with hr=0x%08X. ","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
ca254df5:"[cid:%s] Failed to get client session volume. hr: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
caa5664e:"[cid:%s] Write winrt PID to cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveWinRTDeviceInfoToCache"
cab912f1:"[cid:%s] This is a rendering device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::GetPart"
cacf44ea:"[cid:%s] [ECS]: Reset ecs key: %s/%s to default","C:/_work/1/s/MSRTC/msrtc/src/common/ecs/EcsSettings.cpp";"MsrtcEcsSettings::Settings::SetBatch"
cb83b897:"[cid:%s] [ECS]: Reset ecs key: %s/%s to default","C:/_work/1/s/MSRTC/msrtc/src/common/ecs/EcsSettings.cpp";"MsrtcEcsSettings::UnorderedMap_EraseIf"
cc088adf:"[cid:%s] Cannot find cache to read from registry, key: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::ReadCacheFromRegistry"
ccd8feea:"[cid:%s] Device count is 0","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
cd4f13a4:"[cid:%s] Failed to set volume on active source. Using default mode to set volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
cdc23a40:"[cid:%s] m_pAsyncParas->pGainControl->SetEnabled failed with hr = %#x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::SetAutoBoostControlEnabled"
cdf1c864:"[cid:%s] m_pAsyncParas->pLoudnessControl->SetEnabled failed with hr2 = %#x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::SetAutoBoostControlEnabled"
ce8c560f:"[cid:%s] No sample in the queue. glitch info=%d, device position = %llu, expected finished time=%llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::AudioDeviceThreadProcOneTime"
ced08ed9:"[cid:%s] Metrics specific for device client: %p, enqueued:%llu, dequeued:%llu, deliveredToLMS:%llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::GetPerCallMetrics"
cf2b0480:"[cid:%s] Unexpected certified list device id %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::FillCertifiedListDeviceIDForBTDevices"
cf4d63f3:"[cid:%s] Init spk vol override read from cache: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
cf52877c:"[cid:%s] pWinrtDevInfo is nullptr","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateWinrtDeviceInfoOnClassicDevice"
cf9bd3d3:"[cid:%s] EnableTrace failed with %lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StopAudioOSLogging"
d04bbf9e:"[cid:%s] Certified list deivce Id for %ls is %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::FillCertifiedListDeviceIDForBTDevices"
d072d384:"[cid:%s] IsEndpointAMicArray: getting connector.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::IsEndpointAMicArray"
d08e9e07:"[cid:%s] Set on-air mute status: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalVoipCallCoordinator.cpp";"voipCallCoordinator::RtcPalVoipCallCoordinator::SetOnAirMuteStatus"
d0b2bd2e:"[cid:%s] GetDevicePeriod, hr=%#x, hnsDefaultDevicePeriod: %llu, hnsMinimumDevicePeriod: %llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
d0da0381:"[cid:%s] Got format in TryGetMixFormat: wFormatTag = %d, nChannels = %d, nSamplesPerSec = %d, wBitsPerSample = %d, nBlockAlign = %d, nAvgBytesPerSec = %d, cbSize = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"TryGetMixFormat"
d1095e7b:"[cid:%s] System Mute: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
d11669dc:"[cid:%s] DeviceMicArrayGeometry cannot be created because # of microphones is: %hu .","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceMicGeometry.h";"DeviceMicArrayGeometry::DeviceMicArrayGeometry"
d1661d5e:"[cid:%s] PdhQuery has no working counters at all, so it is useless","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorWindows::CollectPdhMetrics"
d18c348d:"[cid:%s] enum[JackSubType=%u, guidJackSubType=%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x],form factor=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
d1a1f0fe:"[cid:%s] Successfully opted out of Windows audio ducking","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
d2172ae4:"[cid:%s] OnEventSelectAccept: WSAEnumNetworkEvents fail 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectAccept"
d22690a8:"[cid:%s] IOCP destroyed","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::~RtcPalIOCP"
d2982e00:"[cid:%s] [ECS]: Reset ecs key: %s/%s to default","C:/_work/1/s/MSRTC/msrtc/src/common/ecs/EcsSettings.cpp";"MsrtcEcsSettings::Settings::ResetAll"
d2b86085:"[cid:%s] Unable to get mic geometry array information for device: %ls, micArrayGeometry == NULL","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceMicArrayGeometryToCollection"
d2ed76f0:"[cid:%s] Failed to set context","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
d3f3708b:"[cid:%s] pal_audio: Get initial config for device[type=%d,dn=%ls,pnp=%ls] WindowsBlueOrHigher: %d, isSetClientPropNeeded: %d, UsingRawMode: %d, UsingCommunicationsMode: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::GetInitialConfigToSetAudioClientPlatformSpecific"
d4132116:"[cid:%s] pal_audio: IAudioClient::GetDevicePeriod failed, hr=%u. Using default device period","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
d432695c:"[cid:%s] OnAddrChangeEventSignaled: AddressFamily %d Win32 %d Cached %p CachedAll %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::OnAddrChangeEventSignaled"
d4f5f3e7:"[cid:%s] RegisterSocket socket=%p: succeeds","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::RegisterSocket"
d5c36dc0:"[cid:%s] Stop existing trace session. status=%lu\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartLogSession"
d6a619f6:"[cid:%s] fFlag value is unexpected, SetMute to FALSE, hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::GetSystemVolAndUpdateInternalVolInfo"
d6c10d3b:"[cid:%s] Frame sample jitter buffer recovered from starvation. time:%llu, fFrameReady:%d, m_startTime:%llu, m_lastStartTime:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
d6c95c8d:"[cid:%s] PopulateInfoFromPropStoreThreadFuncParams failed with timeout error. Using default values for prop store fields","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
d731a4ff:"[cid:%s] Failed to call m_pAudioDevice->GetIMMDevice with hr = %8x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::RtcPalDeviceAudioBoostControlImpl"
d78e411a:"[cid:%s] CreateInstance (NetworkListManager) failed. hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
d7d01625:"[cid:%s] Successfully read and parsed cache from registry. key: %s, valueStr: %s, value: %d timeStampInSecond: %llu, validDurationInSecond: %llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::ReadCacheFromRegistry"
d8bbf37c:"[cid:%s] pal_audio: activate_loopback[nullptr] hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioLoopbackDevice"
d8ffc3d0:"[cid:%s] IsEndpointAMicArray: Get pin-category GUID","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::IsEndpointAMicArray"
d91f09d1:"[cid:%s] Failed to populate jack subtype info for device: type=%d, id=%ls. Error code returned: hr=%x. Not refining device form factor","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
d9881352:"[cid:%s] pal_audio: #%u: skip one device since it\'s failed to query device, type=%s, hr = %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
d9a0b36a:"[cid:%s] pal_audio: not using raw mode as raw mode not supported (%d) or default mode requested (%d)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::GetInitialConfigToSetAudioClientPlatformSpecific"
d9e1466b:"[cid:%s] GetMessage returns -1 10 times continuously. Break message pump.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::RtcPalMsgThreadProc"
d9fdec42:"[cid:%s] Using timeout mode to query device prop store fields. Overall timeout count: %d. Max allowed timeout count overall: %dTimeout count for current device: %d. Max allowed timeout count per device: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
da6be99d:"[cid:%s] pal_audio: IAudioClient::Initialize failed, hr=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
da8c4514:"[cid:%s] Released all COM interfaces","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
db031d35:"[cid:%s] Read gain control info from cache failed for parsing setting for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ReadGainControlInfoFromCache"
db9e48e1:"[cid:%s] Metrics common for all device clients: total samples created:%llu, delivered:%llu, unexpectedSize:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::GetPerCallMetrics"
dbbcde2b:"[cid:%s] ConnectEx unexpectedly succeed OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaConnectEx"
dbca182f:"[cid:%s] Total Samples Produced: %llu, Queued: %llu, Delivered: %llu, openCount = %d, startCount = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADStopCBackInternal"
dbf88c9b:"[cid:%s] ConfigHandlerName = %ls, device type = %d, finished running and monitoring the main function. ConfigID used = %d, hr = 0x%x, timeTakenInMS = %llu, DeviceOpenResult = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::RunAndMonitorMainFunc"
dc123d48:"[cid:%s] Request start logging, but failed. %d\n","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLogging"
dcb62905:"[cid:%s] GetPnpNameAndRelatedFieldsWithTimeout failed with timeout error. Will use default values for pnp name and related fieldsTime out counts for current device: %d Time out counts overall: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
dceb83c6:"[cid:%s] AudioCapture (%ls): Stop - LockBuffer=%u FramesCaptured=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Stop"
dcfef9ff:"[cid:%s] Queue Close request OVERLAPPED %p for completion","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::ReorderRecv"
dd420df9:"[cid:%s] Virtual device deleted:  type= %d pVdevice= %p, total: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::DeleteVirtualAudioDevice"
dd51a493:"[cid:%s] EmulatedConnectEx: succeed OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
dd5334e0:"[cid:%s] Received session volume changed notification for device type: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
de4f9104:"[cid:%s] System Vol: %d Scaling Factor: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
de6fd346:"[cid:%s] Failed to get certified list deivce Id for %ls with hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
de8ab9c1:"[cid:%s] Successfully set flag to not duck other render streams","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
deb9a6b0:"[cid:%s] Failed to lock sample buffer","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
dec6f8ab:"[cid:%s] The device client handle requested to be destructed doesn\'t have an entry in the metrics map.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::PRtcPalVadDestructDeviceCBInternal"
df300e12:"[cid:%s] IsEndpointAMicArray: getting device endpoint.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::IsEndpointAMicArray"
dfc0d72e:"[cid:%s] props.eCategory = %d,  props.cbSize = %d, props.bIsOffload = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientPlatformSpecific"
e1332578:"[cid:%s] Failed to create thread for func from %s","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalThreadHelper.cpp";"WaitForCompletion"
e1c8e51f:"[cid:%s] Fail to get post workitem to UI thread: 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterface"
e1db3ed7:"[cid:%s] [ECS]: Set ecs key: %s/%s, value: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"RtcPalSetEcsSetting"
e26fa86e:"[cid:%s] %s@%d: if_TRACE(%s) == %s","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalTraceCond"
e2b59bf8:"[cid:%s] Assert is hit at:%s:%d:%s","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/instrument/RtcPalAssertReporter.cpp";"AssertReporter::ReportAssert"
e3389002:"[cid:%s] The device client handle requested to be added already exists in the RingBuffer map.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::AddDeviceClientHandle"
e38f8253:"[cid:%s] OnDefaultDeviceChanged, data flow: %d, role: %d, default device id: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnDefaultDeviceChanged"
e38f9d80:"[cid:%s] Failed to GetLevelRange, hrVolumeControl = %#x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
e3ae1d01:"[cid:%s] AddDeviceMicArrayGeometryToCollection : failed with 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
e3cd4afd:"[cid:%s] accept connection succeeds: AcceptSd=%p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::DoAccept"
e3d08128:"[cid:%s] Not setting master volume since current level (%f) is higher than ecs setting","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
e4063c0f:"[cid:%s] Failed to activate IAudioVolumeLevel, hrVolumeControl = %#x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
e411267c:"[cid:%s] spDevice->Activate with CLSCTX_ALL context failed with error: %x, try recovery with INPROC_SERVER","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioActivationHelperWASAPI.cpp";"RtcPalDeviceAudioActivationHelperWASAPI::ActivateAudioInterface"
e52ebc8a:"[cid:%s] Open regkey returns %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/mfhelper/mfhelper.cpp";"CheckMFCorePresent"
e5bab2d0:"[cid:%s] pal_audio: Initializing device[type=%d,dn=%ls,pnp=%ls] WindowsBlueOrHigher: %d, UsingRawMode: %d, UsingCommunicationsMode: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientPlatformSpecific"
e5e7ff49:"[cid:%s] Not setting speaker session volume since user overrode it in previous call","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
e6024a57:"[cid:%s] No sample available in the pool","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADLockBufferCBackInternal"
e61ea445:"[cid:%s] Alternative config %d from the provided list does not have failure cached before.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetInitialConfigurationID"
e63a26b2:"[cid:%s] Received ecs key to disable setting of speaker master volume. Not setting master volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
e649e493:"[cid:%s] Failed to create RtcPalDeviceAudioBoostControlImpl, out of memory","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::Initialize"
e65fa505:"[cid:%s] OnAddrChangeEventSignaled: AddressFamily %d Win32 %d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::OnAddrChangeEventSignaled"
e669b56d:"[cid:%s]  DNS found pEffect %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::GetAudioBlurStatus"
e6d80b46:"[cid:%s] [ECS]: Set ecs key: %s/%s, value: %s","C:/_work/1/s/MSRTC/msrtc/src/common/ecs/EcsSettings.cpp";"MsrtcEcsSettings::Settings::SetBatch"
e6ee0af8:"[cid:%s] pal_audio: Client Initialization Finished. hr = %x, forceStreamModeAsDefault=%d, continuous failure=%d, total failure=%d, waiting time=%d, forceResetAudioClient=%d, isVolumeControl:(%s), forceUseDefaultFormat=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClient"
e89b841b:"[cid:%s] Listen fails 0x%x, backlog=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Listen"
e96c2faf:"[cid:%s] pal_audio: GetPnpNameAndRelatedFieldsThreadFunc starts","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
e98a4993:"[cid:%s] MicGeometryArrayMap cleared.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::ClearDeviceMicGeometryCollection"
ea6abd0d:"[cid:%s] Init spk session vol override read from cache: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
eaa3462a:"[cid:%s] [ECS]: StorageKey %s is found, value is: %llu","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"InitSettingValue"
eac40759:"[cid:%s] MicGeometryArrayMap requested for device id: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::GetDeviceArrayGeometry"
eb129d12:"[cid:%s] pal_audio: enum usb host controller failed: %x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateUSBControllerAsyncProc"
eb4138a5:"[cid:%s] copied data has not been fully read out. read: %d, real data: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::ReleaseBuffer"
eb45ef1b:"[cid:%s] pal_audio: IAudioClient::GetMixFormat failed, hr=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
ec134893:"[cid:%s] Found config in the list without faliure record in the cache.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::GetRecoveryActionAndNewConfigID"
ec1ad7fb:"[cid:%s] AcceptEx fails 0x%x OVERLAPPED %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncAcceptViaAcceptEx"
ec2e4cdd:"[cid:%s] Releasing session volume","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
ecc19f25:"[cid:%s] GetRawStreamSupport: Failed to set client properties to RAW mode. RAW is not supported, hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::MMDevicePropertyStoreHelper::GetRawStreamSupport"
edf671c4:"[cid:%s] EmulatedAcceptEx: WSAEWOUDBLOCK: retry OVERLAPPED %p later","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectAccept"
ee47cf20:"[cid:%s] AudioLoopback: LockBuffer=%u FramesCaptured=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::LockBuffer"
ee7712d5:"[cid:%s] Failed to populate guid container id for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for guid contrainer id","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
ee979796:"[cid:%s] Get IAudioSessionControl for render device faild with 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
eecd770c:"[cid:%s] GetNetworkConnections failed. hr=0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
eeefab42:"[cid:%s] Trying to use specified? (%s) format, wFormatTag = %d, nChannels = %d, nSamplesPerSec = %d, wBitsPerSample = %d, nBlockAlign = %d, nAvgBytesPerSec = %d, cbSize = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
ef1de485:"[cid:%s] DeviceMicArrayGeometryCoordinates failed: i: %zu, N of coordinates: %zu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceMicGeometry.h";"DeviceMicArrayGeometry::GetMicrophoneGeometryCoordinates"
efbd20fe:"[cid:%s] GetType = %d, m_id.GetRawStreamUsage= %d, m_id.GetCommunicationsMode() = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientPlatformSpecific"
efed3fb4:"[cid:%s] Failed to get audio client, will use default format to open device since not possible to get supported format list. hr=%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
f03c5545:"[cid:%s] Failed ReleaseBuffer on sample, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADReleaseBufferCBackInternal"
f1752a4a:"[cid:%s] IsFormatSupported, hr=%#x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
f185fec2:"[cid:%s] OnPropertyValueChanged, (%ls,{%08X-%04hX-%04hX-%02X%02X-%02X%02X%02X%02X%02X%02X}, pid=%d)","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnPropertyValueChanged"
f1d76e35:"[cid:%s] Error allocating sample pool","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::InitializeAudioRenderDevice"
f1fa34f0:"[cid:%s] pal_audio: queried endpoints count: [type=%s,count=%d]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
f20b46e1:"[cid:%s] OnDeviceRemoved, device id: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnDeviceRemoved"
f23f336c:"[cid:%s] Metrics specific for device client: enqueued:%llu, dequeued:%llu, deliveredToLMS:%llu","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::GetPerCallMetrics"
f2533bf5:"[cid:%s] AudioCapture: LockBuffer=%u FramesCaptured=%u","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::LockBuffer"
f2db5f15:"[cid:%s] Failed to get RtcPalAudioMediaExtensionType_VirtualDeviceManager extension, hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UnregisterVirtualAudioDevices"
f38ab05f:"[cid:%s] ConfigHandlerName = %ls, device type = %d, began running and monitoring the main function. ConfigID used = %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioResilienceHandling.cpp";"RtcPalDeviceAudioBasicConfigHandler::RunAndMonitorMainFunc"
f3f578ab:"[cid:%s] ActivateIAudioEndpointVolumeWithTimeout failed with error hr=%x. Will use default values for gain fields","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
f4108628:"[cid:%s] forceMonoForUnspecifiedFmt = %d, isCapture: %d, trying to set mono channel for the default format, but the format is not supported by the audio client, reverting back to %d channels.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
f4127c8f:"[cid:%s] AudioLoopback (%ls): Activated - FormatTag=%u Channels=%d SamplesPerSec=%d BitsPerSample=%u 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Activate"
f53043fb:"[cid:%s] Failed to populate display name for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for device display name","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
f5cf5970:"[cid:%s] Successfully released VoipCallCoordinator","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalVoipCallCoordinator.cpp";"voipCallCoordinator::RtcPalVoipCallCoordinator::ReleaseVoipCallCoordinator"
f5d5a54a:"[cid:%s] AudioRender (%ls): Start","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
f61ab90f:"[cid:%s] GetPropStoreAndDeviceNameWithTimeout failed with error hr=%x. Will use default values for friendly name and display name and other prop store fields","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
f6248f9b:"[cid:%s] Read gain control info from cache failed for reading setting from cache for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ReadGainControlInfoFromCache"
f6a2f62a:"[cid:%s] Raw mode got used but device doesn\'t support it","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientWithResilienceHandling"
f6e66d03:"[cid:%s] pal_audio: activate_loopback[%ls] hr=0x%08X","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioLoopbackDevice"
f83d87cb:"[cid:%s] Set parameter = %d , No need to change the state, isAudioBlurOn: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::SetAudioBlurEnabled"
f86ba81c:"[cid:%s] Queue Close request OVERLAPPED %p for completion","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::RecvFrom"
f89fbf47:"[cid:%s] Starvation occurred in frame sample jitter buffer. time:%llu, m_samplesDeliveredToLMS:%llu, m_totalSamplesDeliveredToLMS:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
f97919f2:"[cid:%s] Using timeout mode to query default device ids. Overall timeout count: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
f98f1493:"[cid:%s] Successfully update winrt info from cache for device: %ls, winrtId: %ls, certifiedId: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::UpdateWinRTDeviceInfoFromCache"
f9f70884:"[cid:%s] Setting system volume for device %ls to %d Scaling Factor: %d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
faa6f736:"[cid:%s] Unable to get mic geometry array for device: %ls, map size: %d,with hr = 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceMicArrayGeometryToCollection"
fac5113d:"[cid:%s] Enumerate and update WinRT Device ID succeeded with hr=0x%08X. ","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
fb55290c:"[cid:%s] spDADevice->GetId failed for DAD, hr = %x. Will set first enumerated device as default audio device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
fb594914:"[cid:%s] Cache entry for the key is marked as deleted. key: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/RtcPalAudioCacheMgr.cpp";"RtcPalAudioCacheMgr::ReadFromCache"
fb5bf9ba:"[cid:%s] ConnectionTypeChange reported for %s side, it is %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetConnectionTypeMetrics"
fba26472:"[cid:%s] Sample size changed. sampleSizeinBytes:%d, expectedSampleSizeinBytes:%d, totalSamplesUnexpectedSize:%llu, object:%p","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/virtual/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::SetFrameSampleJBQueueCap"
fbd6ecaa:"[cid:%s] Close IOCP %p","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCP.cpp";"RtcPalCloseSocketIOCP"
fbd75434:"[cid:%s] Skipping collection of WiFi stats as requested through metrics flags.","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsPlatform.cpp";"RtcPalSystemMetricsCollectorPlatform::OnTimerCallback"
fd753d16:"[cid:%s] Num of enum operations timeouts exceeded max allowed count. Enum operations timeout count overall: %d Max allowed timeout count overall: %dEnum operations timeout count for current device: %d Max allowed timeout count per device: %dNot querying device format. Will use default values","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
fd9692d4:"[cid:%s] Device is in blocklist. Spatial audio capability is disabled for device with pal_audio: enum[certifiedDeviceId=%ls,isForceBlocked=%d,isForceCapable=%d]","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateSpatialAudioSupport"
fddf293e:"[cid:%s] MF is present, handle=%p, fMFHasAVCapture=%d","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/mfhelper/mfhelper.cpp";"RtcPalMFHasAVCapture"
fe57e29a:"[cid:%s] Get IAudioSessionControl for capture device faild with 0x%x","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
fea13799:"[cid:%s] GetInitialSetAudioClientPropPlatformSpecific failed.","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientWithResilienceHandling"
fee8987e:"[cid:%s] skip one device since its display name, friendly name, and pnp name are null, type=%s, id=%ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
fef57221:"[cid:%s] [ECS]: Set ecs key: %s/%s, value: %s","C:/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"RtcPalSetCallContextSetting"
ff0fce6f:"[cid:%s] Failed to populate gain control info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for gain control fields","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
ff26b527:"[cid:%s] SetEndpointVolume failed hr 0x%x devId %ls eType 0x%x VolumeInfo: MuteSession %d MuteSystem %d volSession %f volSys %f","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::SetEndpointVolume"
ff348776:"[cid:%s] Num of enum operations timeouts %d exceeded max allowed timeout count %d. Not querying default id for eRole: %d. Will mark first device as default device","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
ffc5b743:"[cid:%s] Failed to get driver version for device: id=%ls. Error code returned: hr=%x. Using default value for driver version","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsThreadFunc"
ffd7159f:"[cid:%s] Write winrt CONN to cache failed for device: %ls","C:/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::SaveWinRTDeviceInfoToCache"
