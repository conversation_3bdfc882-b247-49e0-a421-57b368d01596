namespace AccureMD.TeamsBot.MediaService.Models;

public class MediaServiceResponse
{
    public bool Success { get; set; }
    public string? CallId { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime? JoinedAt { get; set; }
    public string? MediaState { get; set; }
}

public class CallbackResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
}
