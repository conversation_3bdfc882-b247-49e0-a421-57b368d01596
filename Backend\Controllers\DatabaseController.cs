using Microsoft.AspNetCore.Mvc;
using AccureMD.TeamsBot.Services;
using AccureMD.TeamsBot.Data;
using Microsoft.EntityFrameworkCore;

namespace AccureMD.TeamsBot.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DatabaseController : ControllerBase
{
    private readonly DatabaseInitializationService _dbInitService;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<DatabaseController> _logger;

    public DatabaseController(
        DatabaseInitializationService dbInitService,
        ApplicationDbContext dbContext,
        ILogger<DatabaseController> logger)
    {
        _dbInitService = dbInitService;
        _dbContext = dbContext;
        _logger = logger;
    }

    [HttpGet("test-connection")]
    public async Task<IActionResult> TestConnection()
    {
        try
        {
            var canConnect = await _dbInitService.TestDatabaseConnectionAsync();
            
            if (canConnect)
            {
                var userCount = await _dbContext.Users.CountAsync();
                var meetingCount = await _dbContext.Meetings.CountAsync();
                var transcriptCount = await _dbContext.Transcripts.CountAsync();

                return Ok(new
                {
                    success = true,
                    message = "Database connection successful",
                    statistics = new
                    {
                        users = userCount,
                        meetings = meetingCount,
                        transcripts = transcriptCount
                    }
                });
            }
            else
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Database connection failed"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connection test failed");
            return StatusCode(500, new
            {
                success = false,
                message = "Database connection test failed",
                error = ex.Message
            });
        }
    }

    [HttpGet("schema-info")]
    public async Task<IActionResult> GetSchemaInfo()
    {
        try
        {
            // Get table information
            var tables = new List<object>();

            // Check if tables exist
            var usersExist = await _dbContext.Database.SqlQueryRaw<int>(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'users'").FirstOrDefaultAsync();
            
            var meetingsExist = await _dbContext.Database.SqlQueryRaw<int>(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'meetings'").FirstOrDefaultAsync();
            
            var transcriptsExist = await _dbContext.Database.SqlQueryRaw<int>(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'transcripts'").FirstOrDefaultAsync();

            return Ok(new
            {
                success = true,
                tables = new
                {
                    users_exists = usersExist > 0,
                    meetings_exists = meetingsExist > 0,
                    transcripts_exists = transcriptsExist > 0
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get schema info");
            return StatusCode(500, new
            {
                success = false,
                message = "Failed to get schema info",
                error = ex.Message
            });
        }
    }

    [HttpPost("initialize")]
    public async Task<IActionResult> InitializeDatabase()
    {
        try
        {
            await _dbInitService.InitializeAsync();
            return Ok(new
            {
                success = true,
                message = "Database initialized successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database initialization failed");
            return StatusCode(500, new
            {
                success = false,
                message = "Database initialization failed",
                error = ex.Message
            });
        }
    }
}
