using Microsoft.EntityFrameworkCore;
using AccureMD.TeamsBot.Data;

namespace AccureMD.TeamsBot.Services;

public class DatabaseInitializationService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<DatabaseInitializationService> _logger;

    public DatabaseInitializationService(ApplicationDbContext dbContext, ILogger<DatabaseInitializationService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Starting database initialization...");

            // Ensure database is created
            await _dbContext.Database.EnsureCreatedAsync();
            _logger.LogInformation("Database ensured to exist");

            // Check if we can connect to the database
            var canConnect = await _dbContext.Database.CanConnectAsync();
            if (canConnect)
            {
                _logger.LogInformation("Successfully connected to PostgreSQL database");
                
                // Log table counts for verification
                var userCount = await _dbContext.Users.CountAsync();
                var meetingCount = await _dbContext.Meetings.CountAsync();
                var transcriptCount = await _dbContext.Transcripts.CountAsync();
                
                _logger.LogInformation("Database statistics - Users: {UserCount}, Meetings: {MeetingCount}, Transcripts: {TranscriptCount}", 
                    userCount, meetingCount, transcriptCount);
            }
            else
            {
                _logger.LogError("Failed to connect to PostgreSQL database");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize database");
            throw;
        }
    }

    public async Task<bool> TestDatabaseConnectionAsync()
    {
        try
        {
            var canConnect = await _dbContext.Database.CanConnectAsync();
            _logger.LogInformation("Database connection test result: {CanConnect}", canConnect);
            return canConnect;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connection test failed");
            return false;
        }
    }
}
