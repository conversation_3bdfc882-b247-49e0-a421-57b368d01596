using System;
using Microsoft.Owin.Hosting;

namespace AccureMD.MediaBot.Worker
{
    internal static class Program
    {
        static void Main(string[] args)
        {
            try
            {
                var url = System.Configuration.ConfigurationManager.AppSettings["ListenUrl"] ?? "http://localhost:8787/";
                Console.WriteLine($"[MediaBot] Starting Web API on {url} (reverse-proxied by IIS) ...");
                WebApp.Start<Startup>(url);
                Console.WriteLine("[MediaBot] Started. Press Ctrl+C to exit.");
                System.Threading.Thread.Sleep(System.Threading.Timeout.Infinite);
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(ex);
                throw;
            }
        }
    }
}

