<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication Fix - AccureMD</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #6264A7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #5a5c9d;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentication Fix Test Page</h1>
        <p>This page tests the Teams authentication fix implementation.</p>

        <div class="test-section info">
            <h3>1. Test Configuration</h3>
            <button onclick="testConfiguration()">Test Configuration</button>
            <div id="configResult"></div>
        </div>

        <div class="test-section info">
            <h3>2. Test Auth URL Generation</h3>
            <button onclick="testAuthUrl()">Test Auth URL Generation</button>
            <div id="authUrlResult"></div>
        </div>

        <div class="test-section info">
            <h3>3. Test Auth Start Page</h3>
            <button onclick="testAuthStartPage()">Open Auth Start Page</button>
            <div id="authStartResult"></div>
        </div>

        <div class="test-section info">
            <h3>4. Test Teams Authentication Flow</h3>
            <button onclick="testTeamsAuth()">Test Teams Auth (Popup)</button>
            <div id="teamsAuthResult"></div>
        </div>

        <div class="test-section info">
            <h3>5. Test Direct URL</h3>
            <p>Test the direct URL that Teams would call:</p>
            <button onclick="testDirectUrl()">Test Direct Auth Start URL</button>
            <div id="directUrlResult"></div>
        </div>
    </div>

    <script>
        async function testConfiguration() {
            const resultDiv = document.getElementById('configResult');
            resultDiv.innerHTML = '<p>Testing configuration...</p>';

            try {
                const response = await fetch('/api/auth/test');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Configuration Test Successful</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Configuration Test Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Configuration Test Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testAuthUrl() {
            const resultDiv = document.getElementById('authUrlResult');
            resultDiv.innerHTML = '<p>Testing auth URL generation...</p>';

            try {
                const response = await fetch('/api/auth/test-auth-url?userId=test-user-123');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Auth URL Generation Successful</h4>
                            <p><strong>Auth URL:</strong> <a href="${data.authUrl}" target="_blank">Open Auth URL</a></p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Auth URL Generation Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Auth URL Generation Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function testAuthStartPage() {
            const resultDiv = document.getElementById('authStartResult');
            const userId = 'test-user-' + Date.now();
            const redirectUri = encodeURIComponent(window.location.origin + '/html/auth-callback.html');
            const authStartUrl = `/html/auth-start.html?userId=${userId}&redirectUri=${redirectUri}`;
            
            resultDiv.innerHTML = `
                <div class="info">
                    <h4>🔗 Opening Auth Start Page</h4>
                    <p><strong>URL:</strong> <a href="${authStartUrl}" target="_blank">${authStartUrl}</a></p>
                    <p>Click the link above to test the auth start page in a new tab.</p>
                </div>
            `;
            
            // Open in new tab
            window.open(authStartUrl, '_blank');
        }

        function testTeamsAuth() {
            const resultDiv = document.getElementById('teamsAuthResult');
            const userId = 'test-user-' + Date.now();
            const redirectUri = encodeURIComponent(window.location.origin + '/html/auth-callback.html');
            const authStartUrl = `/html/auth-start.html?userId=${userId}&redirectUri=${redirectUri}`;
            
            resultDiv.innerHTML = '<p>Opening Teams-style authentication popup...</p>';

            // Simulate Teams authentication popup
            const popup = window.open(authStartUrl, 'teamsAuth', 'width=600,height=700,scrollbars=yes,resizable=yes');
            
            if (popup) {
                resultDiv.innerHTML = `
                    <div class="info">
                        <h4>🔗 Teams Auth Popup Opened</h4>
                        <p>A popup window has been opened to simulate Teams authentication.</p>
                        <p><strong>URL:</strong> ${authStartUrl}</p>
                        <p>Check the popup window for the authentication flow.</p>
                    </div>
                `;
                
                // Check if popup is closed
                const checkClosed = setInterval(() => {
                    if (popup.closed) {
                        clearInterval(checkClosed);
                        resultDiv.innerHTML += `
                            <div class="info">
                                <p>✅ Popup was closed. Check browser console for authentication results.</p>
                            </div>
                        `;
                    }
                }, 1000);
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Popup Blocked</h4>
                        <p>The popup was blocked by the browser. Please allow popups for this site and try again.</p>
                    </div>
                `;
            }
        }

        function testDirectUrl() {
            const resultDiv = document.getElementById('directUrlResult');
            const userId = '5f2cd7d6-fad4-4770-9c2e-6a461872fd6a'; // Use the actual user ID from the error
            const redirectUri = 'https://accuremd.azurewebsites.net/html/auth-callback.html';
            const directUrl = `/html/auth-start.html?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;
            
            resultDiv.innerHTML = `
                <div class="info">
                    <h4>🔗 Direct URL Test</h4>
                    <p>This is the exact URL that Teams would call:</p>
                    <p><strong>URL:</strong> <a href="${directUrl}" target="_blank">${directUrl}</a></p>
                    <p>Click the link above to test the exact scenario from the error logs.</p>
                </div>
            `;
        }

        // Auto-run configuration test on page load
        window.addEventListener('load', () => {
            console.log('Auth Fix Test Page loaded');
            testConfiguration();
        });
    </script>
</body>
</html>
