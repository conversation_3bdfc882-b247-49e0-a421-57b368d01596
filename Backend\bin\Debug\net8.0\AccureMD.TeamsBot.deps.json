{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AccureMD.TeamsBot/1.0.0": {"dependencies": {"Azure.Identity": "1.13.1", "Azure.Storage.Blobs": "12.18.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.Bot.Builder": "4.21.2", "Microsoft.Bot.Builder.Integration.AspNet.Core": "4.21.2", "Microsoft.CognitiveServices.Speech": "1.34.0", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "Microsoft.EntityFrameworkCore.Tools": "8.0.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Graph": "5.89.0", "Microsoft.Identity.Client": "4.74.1", "Npgsql.EntityFrameworkCore.PostgreSQL": "8.0.4", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"AccureMD.TeamsBot.dll": {}}}, "AdaptiveCards/1.2.3": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/AdaptiveCards.dll": {"assemblyVersion": "*******", "fileVersion": "1.2.1909.25002"}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.4400.124.50905"}}}, "Azure.Identity/1.13.1": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.74.1", "Microsoft.Identity.Client.Extensions.Msal": "4.66.1", "System.Memory": "4.5.5", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.13.1.0", "fileVersion": "1.1300.124.52405"}}}, "Azure.Storage.Blobs/12.18.0": {"dependencies": {"Azure.Storage.Common": "12.17.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.18.0.0", "fileVersion": "12.1800.23.46203"}}}, "Azure.Storage.Common/12.17.0": {"dependencies": {"Azure.Core": "1.44.1", "System.IO.Hashing": "6.0.0"}, "runtime": {"lib/net6.0/Azure.Storage.Common.dll": {"assemblyVersion": "12.17.0.0", "fileVersion": "12.1700.23.46203"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Bcl.Memory/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Bcl.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Bot.Builder/4.21.2": {"dependencies": {"Microsoft.Bot.Connector": "4.21.2", "Microsoft.Bot.Connector.Streaming": "4.21.2", "Microsoft.Bot.Streaming": "4.21.2", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Builder.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Builder.Integration.AspNet.Core/4.21.2": {"dependencies": {"Microsoft.Bot.Builder": "4.21.2", "Microsoft.Bot.Configuration": "4.21.2", "Microsoft.Bot.Connector.Streaming": "4.21.2", "Microsoft.Bot.Streaming": "4.21.2", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/net6.0/Microsoft.Bot.Builder.Integration.AspNet.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Configuration/4.21.2": {"dependencies": {"Newtonsoft.Json": "13.0.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Configuration.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Connector/4.21.2": {"dependencies": {"Microsoft.Bot.Schema": "4.21.2", "Microsoft.CSharp": "4.5.0", "Microsoft.Extensions.Http": "2.1.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Identity.Client": "4.74.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.6.1", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Connector.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Connector.Streaming/4.21.2": {"dependencies": {"Microsoft.Bot.Schema": "4.21.2", "Microsoft.Bot.Streaming": "4.21.2", "Microsoft.Extensions.Logging": "8.0.0", "Newtonsoft.Json": "13.0.1", "System.IO.Pipelines": "6.0.3", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Connector.Streaming.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Schema/4.21.2": {"dependencies": {"AdaptiveCards": "1.2.3", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Schema.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Streaming/4.21.2": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Net.Http.Headers": "2.1.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Streaming.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CognitiveServices.Speech/1.34.0": {"runtime": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assemblyVersion": "1.34.0.28", "fileVersion": "1.34.0.28"}}, "runtimeTargets": {"runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-arm", "assetType": "runtime", "assemblyVersion": "1.34.0.28", "fileVersion": "1.34.0.28"}, "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-arm64", "assetType": "runtime", "assemblyVersion": "1.34.0.28", "fileVersion": "1.34.0.28"}, "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-x64", "assetType": "runtime", "assemblyVersion": "1.34.0.28", "fileVersion": "1.34.0.28"}, "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "osx-arm64", "assetType": "runtime", "assemblyVersion": "1.34.0.28", "fileVersion": "1.34.0.28"}, "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "osx-x64", "assetType": "runtime", "assemblyVersion": "1.34.0.28", "fileVersion": "1.34.0.28"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "ios-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "iossimulator-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "iossimulator-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.34.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.34.0.28"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.EntityFrameworkCore/8.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.4", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.4", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "8.0.4.0", "fileVersion": "8.0.424.16902"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.4": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "8.0.4.0", "fileVersion": "8.0.424.16902"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.4": {}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.4", "Microsoft.Extensions.DependencyModel": "8.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.4", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "8.0.4.0", "fileVersion": "8.0.424.16902"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.0"}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Http/2.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Graph/5.89.0": {"dependencies": {"Microsoft.Graph.Core": "3.2.4"}, "runtime": {"lib/net5.0/Microsoft.Graph.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Graph.Core/3.2.4": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.6.1", "Microsoft.IdentityModel.Validators": "8.6.1", "Microsoft.Kiota.Abstractions": "1.17.1", "Microsoft.Kiota.Authentication.Azure": "1.17.1", "Microsoft.Kiota.Http.HttpClientLibrary": "1.17.1", "Microsoft.Kiota.Serialization.Form": "1.17.1", "Microsoft.Kiota.Serialization.Json": "1.17.1", "Microsoft.Kiota.Serialization.Multipart": "1.17.1", "Microsoft.Kiota.Serialization.Text": "1.17.1"}, "runtime": {"lib/net6.0/Microsoft.Graph.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Identity.Client/4.74.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.6.1", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.66.1": {"dependencies": {"Microsoft.Identity.Client": "4.74.1", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.IdentityModel.Abstractions/8.6.1": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Logging/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Protocols/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.6.1", "System.IdentityModel.Tokens.Jwt": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Tokens/8.6.1": {"dependencies": {"Microsoft.Bcl.Memory": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.IdentityModel.Logging": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Validators/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.6.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.6.1", "Microsoft.IdentityModel.Tokens": "8.6.1", "System.IdentityModel.Tokens.Jwt": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Validators.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.Kiota.Abstractions/1.17.1": {"dependencies": {"Std.UriTemplate": "2.0.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Authentication.Azure/1.17.1": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Authentication.Azure.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Http.HttpClientLibrary/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Http.HttpClientLibrary.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Form/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Form.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Json/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Json.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Multipart/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Multipart.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Text/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Text.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Net.Http.Headers/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.Rest.ClientRuntime/2.3.24": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Rest.ClientRuntime.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1.25517"}}}, "Npgsql/8.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "8.0.3.0", "fileVersion": "8.0.3.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/8.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.4", "Microsoft.EntityFrameworkCore.Abstractions": "8.0.4", "Microsoft.EntityFrameworkCore.Relational": "8.0.4", "Npgsql": "8.0.3"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "8.0.4.0", "fileVersion": "8.0.4.0"}}}, "Std.UriTemplate/2.0.1": {"runtime": {"lib/net5.0/Std.UriTemplate.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "System.Buffers/4.5.0": {}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.100.24.46703"}}}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Diagnostics.EventLog/8.0.0": {}, "System.IdentityModel.Tokens.Jwt/8.6.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.6.1", "Microsoft.IdentityModel.Tokens": "8.6.1"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "System.IO.Hashing/6.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IO.Pipelines/6.0.3": {}, "System.Memory/4.5.5": {}, "System.Memory.Data/6.0.0": {"dependencies": {"System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection.Metadata/6.0.1": {"dependencies": {"System.Collections.Immutable": "6.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.Cryptography.ProtectedData/4.5.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.5": {}, "System.Threading.Channels/6.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}}}, "libraries": {"AccureMD.TeamsBot/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AdaptiveCards/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-C3lR/uspvCmWmbE9ku4xGD7M5GKjd+1VksQ5HWlu8Lmv99P4tfya3gGArDO99GqGodwK5z9IHmhfemcqk84VCA==", "path": "adaptivecards/1.2.3", "hashPath": "adaptivecards.1.2.3.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Identity/1.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-4eeK9XztjTmvA4WN+qAvlUCSxSv45+LqTMeC8XT2giGGZHKthTMU2IuXcHjAOf5VLH3wE3Bo6EwhIcJxVB8RmQ==", "path": "azure.identity/1.13.1", "hashPath": "azure.identity.1.13.1.nupkg.sha512"}, "Azure.Storage.Blobs/12.18.0": {"type": "package", "serviceable": true, "sha512": "sha512-IUqHRXnabXCzmmvkzqPK4FuGzLxmKSugDEt5Hm5B/JlJFR+aHDsPW4nCLbG0txThqBSKPqcBBU/oA6c5TaFJgA==", "path": "azure.storage.blobs/12.18.0", "hashPath": "azure.storage.blobs.12.18.0.nupkg.sha512"}, "Azure.Storage.Common/12.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-/h8SpUkxMuQy/MbNFeJQGmhYt3JnYfEiGeDojtNgLNzzhyDnRYgjF3ZKYgjORYQpn0Spr+4+v2MZy+0GNJBLrg==", "path": "azure.storage.common/12.17.0", "hashPath": "azure.storage.common.12.17.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rwxaZYHips5M9vqxRkGfJthTx+Ws4O4yCuefn17J371jL3ouC5Ker43h2hXb5yd9BMnImE9rznT75KJHm6bMgg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bTUtGfpGyJnohQzjdXbtc7MqNzkv7CWUSRz54+ucNm0i32rZiIU0VdVPHDBShOl1qhVKRjW8mnEBz3d2vH93tQ==", "path": "microsoft.bcl.memory/9.0.0", "hashPath": "microsoft.bcl.memory.9.0.0.nupkg.sha512"}, "Microsoft.Bot.Builder/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-T9ovZoL3PJylGZeNg6UBwEhrD9agjSYeHrNcRcASaYeje+FI6T4DHa1NMn3ZT8A3skuKvgQah303rZ+KYvfezw==", "path": "microsoft.bot.builder/4.21.2", "hashPath": "microsoft.bot.builder.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Builder.Integration.AspNet.Core/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZzlPj9BigGxYXZ3xcgjNK7fC11pHsoWCBntrOLNdoAZz2yvbtibWnSoN/PTVfi9XqvDCvzsPLxcyavwYiO48+g==", "path": "microsoft.bot.builder.integration.aspnet.core/4.21.2", "hashPath": "microsoft.bot.builder.integration.aspnet.core.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Configuration/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-pzXVCUg2ixQNioU33CpEF7pUzbjEs44Y4RB5M/XzCEvU7QbMupaMlPijFeCzZpg4y5KZZoBaHRKAWO9acDBPXg==", "path": "microsoft.bot.configuration/4.21.2", "hashPath": "microsoft.bot.configuration.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Connector/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-tOOb/gyfI28Vsof7P9QrnJo62okkcCxHXgrA7BuUpjazEyWdPfaRTQOgXUBqqgnHGQrJRGe8I98JFQMr/hBFmA==", "path": "microsoft.bot.connector/4.21.2", "hashPath": "microsoft.bot.connector.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Connector.Streaming/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-dhxrPhBORX2U60dq2EmbjVSwwoEcKgcFGUznlr6NaizYZKER1q4JqZ5BXOCg2HsTwqe7uB3YOx8fkWt+mXE5jA==", "path": "microsoft.bot.connector.streaming/4.21.2", "hashPath": "microsoft.bot.connector.streaming.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Schema/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-OkJQOwhYPiHpRhoehzQGHVh5lQmzQb636K+ZWmDTJluNcrFBMkvEj0WWc7rDX+caWD7+OrqhYt+i/iofbjZnqg==", "path": "microsoft.bot.schema/4.21.2", "hashPath": "microsoft.bot.schema.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Streaming/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-4IAMvO6SN/nLaHVdB2IlC538+eM59L6st62ouVO20peo9Hdkol5PIIw7fW2gOXXWL32aZ//8LhYmHzFbzKF/gA==", "path": "microsoft.bot.streaming/4.21.2", "hashPath": "microsoft.bot.streaming.4.21.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512"}, "Microsoft.CognitiveServices.Speech/1.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-AnsDqBc6DJ7dkmLMH1RsduuJzIT8rm9Y5WNP5T1Fl5V+Zo81g7HsVdVybnlBp3Y8H3asWYxRSfR8yOeW8lDx0g==", "path": "microsoft.cognitiveservices.speech/1.34.0", "hashPath": "microsoft.cognitiveservices.speech.1.34.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-/kyu9pXuxQvhg8RO/oN5Q5Og7cDIVvZtrt1z48rX7Yido+zEGkUkp3/Bjd9u45N2uuPPF8mn2pKDlAewCvv3/Q==", "path": "microsoft.entityframeworkcore/8.0.4", "hashPath": "microsoft.entityframeworkcore.8.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-S50pjtPNOvRktacaO6UAhvGCPMT56wxqEq8fQfcjaSUySPGba6mKWo6ackw6DdeAR1cA6U+U0uj27warA2KtJA==", "path": "microsoft.entityframeworkcore.abstractions/8.0.4", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-P8hfMZECdbgle4Us8HGRUKAjqVwgbtr5JqtCxqEoiVORrNQAmcpu3g1NKwTAoUsO9Z0QRgExtYoAmdggR/DkMQ==", "path": "microsoft.entityframeworkcore.analyzers/8.0.4", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-94reKYu63jg4O75UI3LMJHwOSi8tQ6IfubiZhdnSsWcgtmAuF8OyLfjK/MIxuvaQRJZAF6E747FIuxjOtb8/og==", "path": "microsoft.entityframeworkcore.design/8.0.0", "hashPath": "microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-aWLT6e9a8oMzXgF0YQpYYa3mDeU+yb2UQSQ+RrIgyGgSpzPfSKgpA7v2kOVDuZr2AQ6NNAlWPaBG7wZuKQI96w==", "path": "microsoft.entityframeworkcore.relational/8.0.4", "hashPath": "microsoft.entityframeworkcore.relational.8.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zRdaXiiB1gEA0b+AJTd2+drh78gkEA4HyZ1vqNZrKq4xwW8WwavSiQsoeb1UsIMZkocLMBbhQYWClkZzuTKEgQ==", "path": "microsoft.entityframeworkcore.tools/8.0.0", "hashPath": "microsoft.entityframeworkcore.tools.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "path": "microsoft.extensions.hosting/8.0.0", "hashPath": "microsoft.extensions.hosting.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vkSkGa1UIZVlAd18oDhrtoawN/q7fDemJcVpT9+28mP7bP0I8zKLSLRwqF++GmPs/7e0Aqlo6jpZm3P7YYS0ag==", "path": "microsoft.extensions.http/2.1.0", "hashPath": "microsoft.extensions.http.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "path": "microsoft.extensions.logging.configuration/8.0.0", "hashPath": "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "path": "microsoft.extensions.logging.console/8.0.0", "hashPath": "microsoft.extensions.logging.console.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "path": "microsoft.extensions.logging.debug/8.0.0", "hashPath": "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "path": "microsoft.extensions.logging.eventlog/8.0.0", "hashPath": "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "path": "microsoft.extensions.logging.eventsource/8.0.0", "hashPath": "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Graph/5.89.0": {"type": "package", "serviceable": true, "sha512": "sha512-pnv9i6PIO673YkKApqtw7d92ylfNIrVoNXpMBnZjLWI5lzmEHJ5QLwKw2aKB+nmGdrl57ssoN31txbwEiA2fGw==", "path": "microsoft.graph/5.89.0", "hashPath": "microsoft.graph.5.89.0.nupkg.sha512"}, "Microsoft.Graph.Core/3.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-0kBbgRiWUrrc7Um0oDcXl9t8Hxzgoz9SddErRDhqdDm4TWDlT8ItYuIfjwoFPHO1O51kwkDtTxzDRwZThbW5Uw==", "path": "microsoft.graph.core/3.2.4", "hashPath": "microsoft.graph.core.3.2.4.nupkg.sha512"}, "Microsoft.Identity.Client/4.74.1": {"type": "package", "serviceable": true, "sha512": "sha512-OJbHQ26k5TkvHdIss6WYjI1MyJQpi1dF8PivqgSmUIllHQAWNBnV3jg0QNfZAiZbGgwIH8f8Th+CfVQT0gGRMg==", "path": "microsoft.identity.client/4.74.1", "hashPath": "microsoft.identity.client.4.74.1.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.66.1": {"type": "package", "serviceable": true, "sha512": "sha512-osgt1J9Rve3LO7wXqpWoFx9UFjl0oeqoUMK/xEru7dvafQ28RgV1A17CoCGCCRSUbgDQ4Arg5FgGK2lQ3lXR4A==", "path": "microsoft.identity.client.extensions.msal/4.66.1", "hashPath": "microsoft.identity.client.extensions.msal.4.66.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-OwmvCXYTttrxV3qT7QKDkoQP4/DB4RWjTwEqV+dNfb2opHn29WGDzoF+r4BVFQVy+BDYMhRlhIp8g3jSyJd+4Q==", "path": "microsoft.identitymodel.abstractions/8.6.1", "hashPath": "microsoft.identitymodel.abstractions.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-CAu9DWsPZVtnyE3bOJ83rlPWpahY37sP/0bIOdRlxS90W88zSI4V3FyoCDlXxV8+gloT+a247pwPXfSNjYyAxw==", "path": "microsoft.identitymodel.jsonwebtokens/8.6.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-BdWlVgJYdmcR9TMUOhaZ3vJyaRO7zr7xgK+cRT4R2q59Xl7JMmTB4ctb/VOsyDhxXb497jDNNvLwldp+2ZVBEg==", "path": "microsoft.identitymodel.logging/8.6.1", "hashPath": "microsoft.identitymodel.logging.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-myul8Jm/kWOtbD+yDeU0LfDPGHDDhNO2Q6U40QlmL0LAK0u1wYl76yKM3/Mzv7ceOkporWAQoAb85QIFnXraOA==", "path": "microsoft.identitymodel.protocols/8.6.1", "hashPath": "microsoft.identitymodel.protocols.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-RTZIO/vZOPoy7dZzk3JfAD+EAWZg32xvcF7yNK8DcnIJy86OI1l2AIT7tZp0FG95cLrACV6X8xlc0StOfgB8ag==", "path": "microsoft.identitymodel.protocols.openidconnect/8.6.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-FvED2com8LIFl9yFXneiX0uxNf9fuf8jKDFcvxC93qXOAfFa8fdLkCiur1vWF+PvgQHhsHVBe6CtDZHzsN8nCQ==", "path": "microsoft.identitymodel.tokens/8.6.1", "hashPath": "microsoft.identitymodel.tokens.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Validators/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-FwST3dwbP4IgPsVVueMau8pHdFllesFSiZy+6L7/BtuflE8Tl1Z7MQW1/4ujmOOYQoBZCjdUQPzOFrC7NlqaBw==", "path": "microsoft.identitymodel.validators/8.6.1", "hashPath": "microsoft.identitymodel.validators.8.6.1.nupkg.sha512"}, "Microsoft.Kiota.Abstractions/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-nznpJUkC8F0iwZBael68nIS90g0wKgipJsLwarknd8hsGom0nJhyogZvIJUIzRWfNNSp0JhsxRYEW0WhvmppKw==", "path": "microsoft.kiota.abstractions/1.17.1", "hashPath": "microsoft.kiota.abstractions.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Authentication.Azure/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-BkzhCChAH+IOfH7NnB86ynvRqPfHRDdxQw1XJ/ShwsG66A9dRLJ/T4K+d+U5l4EiaCllgXDN6+TUEc5KXMgOgw==", "path": "microsoft.kiota.authentication.azure/1.17.1", "hashPath": "microsoft.kiota.authentication.azure.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Http.HttpClientLibrary/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-7cSZoEJ8G9YT3UxCILASZIB0GbmVnO3jdLDAMgMeU4O/SNemKtgA58pvKYJVrIZnJ9Up/mflQZFkGrSCOij4WQ==", "path": "microsoft.kiota.http.httpclientlibrary/1.17.1", "hashPath": "microsoft.kiota.http.httpclientlibrary.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Form/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-L35vwCSp01r7FoRunmSrKVfGnDBx1+3kcRnSMdleukRIAEeldnrNrjcitqVNSXhCqqnsVIT//B5dBvOCcZFCcA==", "path": "microsoft.kiota.serialization.form/1.17.1", "hashPath": "microsoft.kiota.serialization.form.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Json/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-EIUiZDVi1XS83k8ybtHMZr7NzHTtiIVGGIif6hiQV2CUEEhTQKxK/eggwajw8+CRfSPrs7ksvKNJleQnAIecHA==", "path": "microsoft.kiota.serialization.json/1.17.1", "hashPath": "microsoft.kiota.serialization.json.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Multipart/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-bbWg8iQrxBqPVRHfZnNuo1x9oHiRJJfRreTsAb9aO8ViO46wtB7niMgCWt69XG9iQdzAy9pKF8Quho14bNOAmg==", "path": "microsoft.kiota.serialization.multipart/1.17.1", "hashPath": "microsoft.kiota.serialization.multipart.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Text/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-dx1kIbBt21DqRt1tTPt3XkC8doEY1xFf+amPmPedCOM6X8IqdVs8xHymE/66aSWr5alufhEebu/bDxpR90M7mQ==", "path": "microsoft.kiota.serialization.text/1.17.1", "hashPath": "microsoft.kiota.serialization.text.1.17.1.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-c08F7C7BGgmjrq9cr7382pBRhcimBx24YOv4M4gtzMIuVKmxGoRr5r9A2Hke9v7Nx7zKKCysk6XpuZasZX4oeg==", "path": "microsoft.net.http.headers/2.1.0", "hashPath": "microsoft.net.http.headers.2.1.0.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime/2.3.24": {"type": "package", "serviceable": true, "sha512": "sha512-hZH7XgM3eV2jFrnq7Yf0nBD4WVXQzDrer2gEY7HMNiwio2hwDsTHO6LWuueNQAfRpNp4W7mKxcXpwXUiuVIlYw==", "path": "microsoft.rest.clientruntime/2.3.24", "hashPath": "microsoft.rest.clientruntime.2.3.24.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Npgsql/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-6WEmzsQJCZAlUG1pThKg/RmeF6V+I0DmBBBE/8YzpRtEzhyZzKcK7ulMANDm5CkxrALBEC8H+5plxHWtIL7xnA==", "path": "npgsql/8.0.3", "hashPath": "npgsql.8.0.3.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-/hHd9MqTRVDgIpsToCcxMDxZqla0HAQACiITkq1+L9J2hmHKV6lBAPlauF+dlNSfHpus7rrljWx4nAanKD6qAw==", "path": "npgsql.entityframeworkcore.postgresql/8.0.4", "hashPath": "npgsql.entityframeworkcore.postgresql.8.0.4.nupkg.sha512"}, "Std.UriTemplate/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ix5VXZwLfolwVHyGTSSJl6KIJ2le6E9YjLdZBMS1Xxzw7VJankRvQW8JoUL69tEgfcw+0qjgWrlxANrhvS0QCQ==", "path": "std.uritemplate/2.0.1", "hashPath": "std.uritemplate.2.0.1.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-EXL1Tj+pizswtHHPiQyNumrTo8XOLX7SoTm7Bz00/DyiIoG2H/kQItoajSvr1MYtvDNXveqULsoWDoJFI3aHzQ==", "path": "system.identitymodel.tokens.jwt/8.6.1", "hashPath": "system.identitymodel.tokens.jwt.8.6.1.nupkg.sha512"}, "System.IO.Hashing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "path": "system.io.hashing/6.0.0", "hashPath": "system.io.hashing.6.0.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "path": "system.memory.data/6.0.0", "hashPath": "system.memory.data.6.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Metadata/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "path": "system.reflection.metadata/6.0.1", "hashPath": "system.reflection.metadata.6.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}}}