using Microsoft.AspNetCore.Mvc;
using AccureMD.TeamsBot.MediaService.Models;
using AccureMD.TeamsBot.MediaService.Services;
using System.Text.Json;

namespace AccureMD.TeamsBot.MediaService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MeetingController : ControllerBase
{
    private readonly Services.MediaService _mediaService;
    private readonly CallStateService _callStateService;
    private readonly CallbackProcessingService _callbackProcessingService;
    private readonly ILogger<MeetingController> _logger;

    public MeetingController(
        Services.MediaService mediaService,
        CallStateService callStateService,
        CallbackProcessingService callbackProcessingService,
        ILogger<MeetingController> logger)
    {
        _mediaService = mediaService;
        _callStateService = callStateService;
        _callbackProcessingService = callbackProcessingService;
        _logger = logger;
    }

    /// <summary>
    /// Join a Teams meeting with media streaming capabilities
    /// </summary>
    [HttpPost("join")]
    public async Task<IActionResult> JoinMeetingAsync([FromBody] JoinMeetingRequest request)
    {
        try
        {
            _logger.LogInformation("Received join meeting request for URL: {MeetingUrl}", request.MeetingUrl);

            if (string.IsNullOrWhiteSpace(request.MeetingUrl))
            {
                return BadRequest(new MediaServiceResponse
                {
                    Success = false,
                    Message = "Meeting URL is required"
                });
            }

            var result = await _mediaService.JoinMeetingWithMediaAsync(
                request.MeetingUrl,
                request.TenantId,
                request.DisplayName);

            if (result.Success)
            {
                _logger.LogInformation("Successfully joined meeting. Call ID: {CallId}", result.CallId);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Failed to join meeting: {Message}", result.Message);
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing join meeting request");
            return StatusCode(500, new MediaServiceResponse
            {
                Success = false,
                Message = $"Internal server error: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Get the current state of a call
    /// </summary>
    [HttpGet("call/{callId}/state")]
    public IActionResult GetCallState(string callId)
    {
        try
        {
            var callState = _callStateService.GetCallState(callId);
            
            if (callState == null)
            {
                return NotFound(new { message = "Call not found" });
            }

            return Ok(callState);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving call state for {CallId}", callId);
            return StatusCode(500, new { message = "Internal server error" });
        }
    }

    /// <summary>
    /// Get all active calls
    /// </summary>
    [HttpGet("calls/active")]
    public IActionResult GetActiveCalls()
    {
        try
        {
            var activeCalls = _callStateService.GetAllActiveCalls();
            return Ok(activeCalls);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active calls");
            return StatusCode(500, new { message = "Internal server error" });
        }
    }

    /// <summary>
    /// Health check endpoint
    /// </summary>
    [HttpGet("health")]
    public IActionResult HealthCheck()
    {
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            service = "AccureMD Teams Media Service"
        });
    }
}

[ApiController]
[Route("api/calls")]
public class CallsController : ControllerBase
{
    private readonly CallStateService _callStateService;
    private readonly CallbackProcessingService _callbackProcessingService;
    private readonly ILogger<CallsController> _logger;

    public CallsController(
        CallStateService callStateService,
        CallbackProcessingService callbackProcessingService,
        ILogger<CallsController> logger)
    {
        _callStateService = callStateService;
        _callbackProcessingService = callbackProcessingService;
        _logger = logger;
    }

    /// <summary>
    /// Callback endpoint for Microsoft Graph Communications API
    /// This endpoint receives notifications about call state changes, media events, etc.
    /// </summary>
    [HttpPost("callback")]
    public async Task<IActionResult> CallbackAsync([FromBody] JsonElement body)
    {
        try
        {
            var bodyText = body.GetRawText();
            _logger.LogInformation("Received Graph callback: {Body}", bodyText);

            // Record the callback for tracking
            _callStateService.RecordCallbackBody(bodyText);

            // Process the callback asynchronously
            await _callbackProcessingService.ProcessCallbackAsync(bodyText);

            // Return 202 to indicate async processing
            return Accepted();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Graph callback");
            return StatusCode(500, new CallbackResponse
            {
                Success = false,
                Message = $"Error processing callback: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Validation endpoint for Microsoft Graph webhook subscriptions
    /// </summary>
    [HttpGet("callback")]
    public IActionResult ValidateCallback([FromQuery] string validationToken)
    {
        _logger.LogInformation("Received validation request with token: {Token}", validationToken);

        // Graph change notifications expect a plain-text echo
        return Content(validationToken ?? string.Empty, "text/plain");
    }
}
