<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teams Authentication Test</title>
    <script src="https://res.cdn.office.net/teams-js/2.31.0/js/MicrosoftTeams.min.js"></script>
</head>
<body>
    <div style="text-align: center; padding: 20px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
        <h1>Teams Authentication Test</h1>
        <p>This page simulates the Teams environment for testing authentication.</p>
        
        <div style="margin: 20px;">
            <button id="testTeamsAuth" style="padding: 10px 20px; font-size: 16px; background-color: #6264a7; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Test Teams Authentication
            </button>
        </div>
        
        <div id="result" style="margin: 20px; padding: 10px; border: 1px solid #ccc; border-radius: 4px; background-color: #f9f9f9; min-height: 100px;">
            <p>Click the button above to test Teams authentication flow.</p>
        </div>
        
        <div id="logs" style="margin: 20px; padding: 10px; border: 1px solid #ccc; border-radius: 4px; background-color: #f0f0f0; text-align: left; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
            <strong>Console Logs:</strong><br>
        </div>
    </div>

    <script>
        const resultDiv = document.getElementById('result');
        const logsDiv = document.getElementById('logs');
        
        // Override console.log to show logs on page
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            logsDiv.innerHTML += args.join(' ') + '<br>';
            logsDiv.scrollTop = logsDiv.scrollHeight;
        };
        
        const originalError = console.error;
        console.error = function(...args) {
            originalError.apply(console, args);
            logsDiv.innerHTML += '<span style="color: red;">ERROR: ' + args.join(' ') + '</span><br>';
            logsDiv.scrollTop = logsDiv.scrollHeight;
        };
        
        console.log('Teams test page loaded');
        
        // Initialize Teams SDK
        microsoftTeams.app.initialize().then(() => {
            console.log('Teams SDK initialized successfully');
            
            // Get Teams context
            microsoftTeams.app.getContext().then((context) => {
                console.log('Teams context:', JSON.stringify(context, null, 2));
            }).catch((error) => {
                console.error('Failed to get Teams context:', error);
            });
            
        }).catch((error) => {
            console.error('Failed to initialize Teams SDK:', error);
        });
        
        document.getElementById('testTeamsAuth').addEventListener('click', async () => {
            console.log('Starting Teams authentication test...');
            resultDiv.innerHTML = '<p>Testing Teams authentication...</p>';
            
            try {
                // Test the auth start page URL
                const authStartUrl = window.location.origin + '/html/auth-start.html';
                console.log('Auth start URL:', authStartUrl);
                
                // Use modern Teams authentication API
                console.log('Calling microsoftTeams.authentication.authenticate with modern parameters...');

                // Check if authentication is supported
                if (!microsoftTeams.authentication.isSupported()) {
                    throw new Error('Teams authentication is not supported in this context');
                }

                const result = await microsoftTeams.authentication.authenticate({
                    url: authStartUrl,
                    width: 600,
                    height: 535,
                    // Add hostRedirectUrl for Teams SDK 2.11.0+ compatibility
                    hostRedirectUrl: window.location.origin + '/html/auth-callback.html'
                });
                
                console.log('Authentication result:', result);
                
                // Parse the result
                let authResult;
                if (typeof result === 'string' && result.startsWith('accuremd.auth.result.')) {
                    // Get the stored result from localStorage
                    const storedData = localStorage.getItem(result);
                    if (storedData) {
                        authResult = JSON.parse(storedData);
                        // Clean up the stored data
                        localStorage.removeItem(result);
                        console.log('Retrieved auth result from localStorage:', authResult);
                    } else {
                        throw new Error('Authentication data not found in storage');
                    }
                } else {
                    // Fallback: try to parse as direct JSON
                    authResult = typeof result === 'string' ? JSON.parse(result) : result;
                    console.log('Parsed auth result directly:', authResult);
                }
                
                if (authResult && authResult.accessToken) {
                    resultDiv.innerHTML = `
                        <div style="color: green;">
                            <h3>✅ Authentication Successful!</h3>
                            <p><strong>Access Token:</strong> ${authResult.accessToken.substring(0, 20)}...</p>
                            <p><strong>Token Type:</strong> ${authResult.tokenType}</p>
                            <p><strong>Expires In:</strong> ${authResult.expiresIn} seconds</p>
                            <p><strong>Scope:</strong> ${authResult.scope}</p>
                        </div>
                    `;
                } else {
                    throw new Error('No access token received');
                }
                
            } catch (error) {
                console.error('Authentication test failed:', error);
                resultDiv.innerHTML = `
                    <div style="color: red;">
                        <h3>❌ Authentication Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
