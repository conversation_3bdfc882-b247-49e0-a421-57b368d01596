{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/teams-app.css", "AssetFile": "css/teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11424"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:01:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ="}]}, {"Route": "css/teams-app.vwl5012ydz.css", "AssetFile": "css/teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11424"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:01:32 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwl5012ydz"}, {"Name": "integrity", "Value": "sha256-0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ="}, {"Name": "label", "Value": "css/teams-app.css"}]}, {"Route": "html/auth-callback.html", "AssetFile": "html/auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28396"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 18:39:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk="}]}, {"Route": "html/auth-callback.q6j5iap63k.html", "AssetFile": "html/auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28396"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 18:39:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q6j5iap63k"}, {"Name": "integrity", "Value": "sha256-nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk="}, {"Name": "label", "Value": "html/auth-callback.html"}]}, {"Route": "html/auth-start.html", "AssetFile": "html/auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6131"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:33:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU="}]}, {"Route": "html/auth-start.ksq8v2n24n.html", "AssetFile": "html/auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6131"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:33:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ksq8v2n24n"}, {"Name": "integrity", "Value": "sha256-XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU="}, {"Name": "label", "Value": "html/auth-start.html"}]}, {"Route": "html/auth-test.html", "AssetFile": "html/auth-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11310"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Aug 2025 13:11:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k="}]}, {"Route": "html/auth-test.zbbusfory5.html", "AssetFile": "html/auth-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11310"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Aug 2025 13:11:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zbbusfory5"}, {"Name": "integrity", "Value": "sha256-3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k="}, {"Name": "label", "Value": "html/auth-test.html"}]}, {"Route": "html/configure.8inynrhuf2.html", "AssetFile": "html/configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:45:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inynrhuf2"}, {"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}, {"Name": "label", "Value": "html/configure.html"}]}, {"Route": "html/configure.html", "AssetFile": "html/configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:45:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}]}, {"Route": "html/index.5cqt4i9h8l.html", "AssetFile": "html/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"a/meDWy3DNvnGb/a1OEnuc7vKsp5/RFzckmJulzdb5U=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 16:14:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5cqt4i9h8l"}, {"Name": "integrity", "Value": "sha256-a/meDWy3DNvnGb/a1OEnuc7vKsp5/RFzckmJulzdb5U="}, {"Name": "label", "Value": "html/index.html"}]}, {"Route": "html/index.html", "AssetFile": "html/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"a/meDWy3DNvnGb/a1OEnuc7vKsp5/RFzckmJulzdb5U=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 16:14:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/meDWy3DNvnGb/a1OEnuc7vKsp5/RFzckmJulzdb5U="}]}, {"Route": "html/privacy.9ukuo7vfri.html", "AssetFile": "html/privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ukuo7vfri"}, {"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}, {"Name": "label", "Value": "html/privacy.html"}]}, {"Route": "html/privacy.html", "AssetFile": "html/privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/termsofuse.html", "AssetFile": "html/termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "html/termsofuse.tv39flyyfq.html", "AssetFile": "html/termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tv39flyyfq"}, {"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}, {"Name": "label", "Value": "html/termsofuse.html"}]}, {"Route": "html/test-auth-fix.ew7c11alxw.html", "AssetFile": "html/test-auth-fix.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:36:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ew7c11alxw"}, {"Name": "integrity", "Value": "sha256-AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ="}, {"Name": "label", "Value": "html/test-auth-fix.html"}]}, {"Route": "html/test-auth-fix.html", "AssetFile": "html/test-auth-fix.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:36:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ="}]}, {"Route": "js/teams-app.js", "AssetFile": "js/teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8x9bMylEvRg7ipKZ/a+RU49RlF+FsxUTWfddl9BUI7E=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 10:58:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8x9bMylEvRg7ipKZ/a+RU49RlF+FsxUTWfddl9BUI7E="}]}, {"Route": "js/teams-app.zozdymfjfk.js", "AssetFile": "js/teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8x9bMylEvRg7ipKZ/a+RU49RlF+FsxUTWfddl9BUI7E=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 10:58:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zozdymfjfk"}, {"Name": "integrity", "Value": "sha256-8x9bMylEvRg7ipKZ/a+RU49RlF+FsxUTWfddl9BUI7E="}, {"Name": "label", "Value": "js/teams-app.js"}]}, {"Route": "teams-test.cztlu2xxj7.html", "AssetFile": "teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6390"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:44:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cztlu2xxj7"}, {"Name": "integrity", "Value": "sha256-+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo="}, {"Name": "label", "Value": "teams-test.html"}]}, {"Route": "teams-test.html", "AssetFile": "teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6390"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:44:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo="}]}, {"Route": "test-auth.2r3ito90zx.html", "AssetFile": "test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2r3ito90zx"}, {"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}, {"Name": "label", "Value": "test-auth.html"}]}, {"Route": "test-auth.html", "AssetFile": "test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}, {"Route": "test-teams-auth.html", "AssetFile": "test-teams-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:45:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I="}]}, {"Route": "test-teams-auth.r54zswcdn2.html", "AssetFile": "test-teams-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:45:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r54zswcdn2"}, {"Name": "integrity", "Value": "sha256-zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I="}, {"Name": "label", "Value": "test-teams-auth.html"}]}]}