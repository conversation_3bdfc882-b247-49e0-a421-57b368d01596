{"format": 1, "restore": {"D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.csproj": {}}, "projects": {"D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.csproj", "projectName": "AccureMD.TeamsBot", "projectPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.TeamsBot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.Identity": {"target": "Package", "version": "[1.13.1, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.18.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Bot.Builder": {"target": "Package", "version": "[4.21.2, )"}, "Microsoft.Bot.Builder.Integration.AspNet.Core": {"target": "Package", "version": "[4.21.2, )"}, "Microsoft.CognitiveServices.Speech": {"target": "Package", "version": "[1.34.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.89.0, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.74.1, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.4, )"}, "System.Diagnostics.DiagnosticSource": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}